# Testimonial 组件使用文档

`Testimonial` 是一个用于展示客户评价或推荐语的 Vue
组件。它支持响应式布局，可以根据PC和移动设备配置不同的显示行数和样式（默认网格布局或自动滚动覆盖布局）。组件还提供了丰富的插槽，允许对标题、内容展示乃至单个评价卡片的渲染进行深度定制。

## Props (属性)

以下是 `Testimonial` 组件可接受的 props 列表：

| Prop 名称  | 类型                                                                 | 描述                                                                            | 默认值 (PC)                               | 默认值 (Mobile)                           |
|:---------|:-------------------------------------------------------------------|:------------------------------------------------------------------------------|:---------------------------------------|:---------------------------------------|
| `pc`     | `Partial<{ rows: number; displayStyle: 'default' \| 'overlay'; }>` | PC设备上的显示配置。`rows` 定义显示行数，`displayStyle` 定义布局模式。                               | `{ rows: 1, displayStyle: 'default' }` | -                                      |
| `mobile` | `Partial<{ rows: number; displayStyle: 'default' \| 'overlay'; }>` | 移动设备上的显示配置。`rows` 定义显示行数（在 `overlay` 模式下，通常指同时可见的滚动行数），`displayStyle` 定义布局模式。 | -                                      | `{ rows: 1, displayStyle: 'overlay' }` |

**`displayStyle` 可选值:**

- `'default'`: 瀑布流网格布局，适用于PC端，展示多个评价。
- `'overlay'`: 自动无限滚动的覆盖式布局，常用于移动端或空间有限的区域，以吸引用户注意力。

## 数据配置

`Testimonial` 组件的内容主要通过以下方式配置和获取：

1. **默认文本配置**:
    * 组件的默认主标题和描述文本由 `/config/testimonial.ts` (或相应的 `.json` 文件)驱动。
    * 在该配置文件中，`title` 和 `description` 字段应为 `I18nTextMap` 类型，以支持国际化。组件内部使用
      `getLocalizedConfigText` 工具函数根据当前i18n语言环境自动加载对应的文本。

2. **评价数据获取**:
    * 实际的评价列表数据通过调用 `testimonialApi.page()` 方法从后端API异步获取。
    * API应返回一个包含 `TestimonialResponse[]` 类型数组的响应。每个 `TestimonialResponse` 对象代表一条评价，其具体字段结构取决于后端定义以及
      `TestimonialCard.vue` 组件的期望。常见的字段可能包括：
        * `avatar`: `string` (评价者头像链接)
        * `authorName`: `string` (评价者名称)
        * `authorRole`: `string` (评价者角色或职位)
        * `companyName`: `string` (评价者所在公司)
        * `content`: `string` (评价内容)
        * *(以及其他 `TestimonialCard.vue` 可能需要的字段)*

组件加载时会显示骨架屏（Skeleton）直到数据加载完成。如果数据获取失败或返回为空，则不显示评价内容。

## 插槽 (Slots)

`Testimonial` 组件提供了以下插槽以实现灵活的定制：

- **`before-layout`**:
    - **描述**: 在组件主要布局结构渲染之前插入自定义内容。
    - **作用域数据**: 无。

- **`header` (作用域插槽)**:
    - **描述**: 自定义或替换组件的默认标题和描述区域。
    - **作用域数据**:
        - `localizedTitle`: `string` - 本地化后的默认标题 (来自 `testimonialConfig`)。
        - `localizedDescription`: `string` - 本地化后的默认描述 (来自 `testimonialConfig`)。

- **`before-show-testimonial-content`**:
    - **描述**: 在实际的评价列表（无论是网格还是滚动布局）渲染之前插入自定义内容。此插槽在 `header` 插槽之后。
    - **作用域数据**: 无。

- **`default-layout-content` (作用域插槽)**:
    - **描述**: 当 `displayStyle` 为 `'default'` (网格布局) 时，用于自定义单个评价项的渲染。如果不提供此插槽，默认使用
      `TestimonialCard` 组件渲染。
    - **作用域数据**:
        - `testimonial`: `TestimonialResponse` - 当前正在迭代的单个评价对象。

- **`overlay-layout-content` (作用域插槽)**:
    - **描述**: 当 `displayStyle` 为 `'overlay'` (滚动布局) 时，用于自定义单个评价项的渲染。如果不提供此插槽，默认使用
      `TestimonialCard` 组件渲染，并包裹在 `AutoScrollContainer` 的子项中。
    - **作用域数据**:
        - `testimonial`: `TestimonialResponse` - 当前正在迭代的单个评价对象。

- **`after-layout`**:
    - **描述**: 在组件主要布局结构渲染之后，但在组件根元素闭合之前插入自定义内容。
    - **作用域数据**: 无。

## 基本用法示例

```vue

<template>
	<Testimonial
			:pc="{ displayStyle: 'default', rows: 2 }"
			:mobile="{ displayStyle: 'overlay', rows: 3 }"
	/>
</template>

<script setup lang="ts">
	// Testimonial 组件通常会自动导入 (Nuxt 3项目特性)
	// import Testimonial from '~/components/market/testimonials/Testimonial.vue';
</script>
```

此示例将在PC上显示一个2行的网格评价列表，在移动设备上显示一个3行的滚动评价列表。标题和描述将从 `testimonialConfig` 中获取。

## 使用自定义插槽示例

```vue

<template>
	<Testimonial :pc="{ displayStyle: 'default', rows: 1 }" :mobile="{displayStyle: 'overlay', rows: 2}">
		<template #header="{ localizedTitle, localizedDescription }">
			<div class="my-custom-header text-center mb-10">
				<h1 class="text-4xl font-bold text-purple-600">{{ localizedTitle.toUpperCase() }} - Our Fans!</h1>
				<p class="text-lg text-gray-500 mt-2">~ {{ localizedDescription }} ~</p>
			</div>
		</template>
		
		<template #before-layout>
			<div class="p-4 bg-yellow-100 text-yellow-800 text-center">
				✨ Special Announcement Before Testimonials! ✨
			</div>
		</template>
		
		<template #default-layout-content="{ testimonial }">
			<article
					class="p-6 mb-6 bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow">
				<div class="flex items-center mb-3">
					<img v-if="testimonial.avatar" :src="testimonial.avatar" :alt="testimonial.authorName"
						 class="w-12 h-12 rounded-full mr-3 object-cover">
					<div>
						<h3 class="font-semibold text-lg text-gray-800">{{ testimonial.authorName }}</h3>
						<p v-if="testimonial.authorRole" class="text-sm text-purple-500">{{ testimonial.authorRole }}
							<span v-if="testimonial.companyName">@ {{ testimonial.companyName }}</span></p>
					</div>
				</div>
				<blockquote class="italic text-gray-600">
					"{{ testimonial.content }}"
				</blockquote>
			</article>
		</template>
		
		<template #overlay-layout-content="{ testimonial }">
			<div class="mx-2 flex-shrink-0 w-[320px] p-5 bg-gradient-to-br from-purple-500 to-pink-500 text-white rounded-xl shadow-xl h-64 flex flex-col justify-between">
				<div>
					<div class="flex items-center mb-2">
						<img v-if="testimonial.avatar" :src="testimonial.avatar" :alt="testimonial.authorName"
							 class="w-10 h-10 rounded-full mr-2 border-2 border-white">
						<h4 class="font-bold">{{ testimonial.authorName }}</h4>
					</div>
					<p class="text-sm line-clamp-4">{{ testimonial.content }}</p>
				</div>
				<p v-if="testimonial.companyName" class="text-xs text-purple-200 text-right mt-1">~ {{
					testimonial.companyName }}</p>
			</div>
		</template>
		
		<template #after-layout>
			<div class="mt-10 text-center">
				<NuxtLink to="/submit-testimonial">
					<Button variant="outline">
						Want to share your story?
						<Icon name="lucide:send" class="ml-2 h-4 w-4"/>
					</Button>
				</NuxtLink>
			</div>
		</template>
	</Testimonial>
</template>

<script setup lang="ts">
	// import Testimonial from '~/components/market/testimonials/Testimonial.vue';
	// import { Button } from '@/components/ui/button'; // 如果在插槽中使用
	// import { Icon } from '#components'; // 如果在插槽中使用
	// import { NuxtLink } from '#components'; // 如果在插槽中使用
</script>
```

**注意**:

- 上述插槽示例中的CSS类名主要基于Tailwind CSS。实际项目中请根据您的样式系统调整。
- `TestimonialResponse` 对象的具体字段（如 `testimonial.avatar`, `testimonial.authorName` 等）取决于您的API响应和
  `TestimonialCard.vue` 组件（如果未使用插槽覆盖默认渲染）的内部实现。请确保这些字段可用。
- 在插槽中使用到的如 `Button`, `Icon`, `NuxtLink` 等组件，如果不是全局注册或自动导入的，可能需要在使用它们的父组件中单独导入。 
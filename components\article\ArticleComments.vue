<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {Skeleton} from '@/components/ui/skeleton'
import {tMsg} from '../../utils/i18n'

interface Comment {
    id: number;
    author: string;
    avatar?: string;
    content: string;
    date: string;
    replyTo?: {
        id: number;
        author: string;
    };
}

// 组件属性
const props = defineProps<{
    articleId: string | number;
    articleType: 'blog' | 'docs';
}>()

// 评论加载状态
const loading = ref(true)

// 加载更多状态
const loadingMore = ref(false)

// 提交评论状态
const submitting = ref(false)

// 评论列表数据
const comments = ref<Comment[]>([])

// 新评论内容
const newComment = ref('')

// 回复信息
const replyTo = ref<{ id: number, author: string } | null>(null)

// 是否还有更多评论
const hasMoreComments = ref(true)

// 加载评论数据
const loadComments = () => {
    loading.value = true

    // 模拟API调用，使用articleId和articleType获取评论
    console.log(`加载${props.articleType}类型的文章(ID: ${props.articleId})的评论`)

    setTimeout(() => {
        comments.value = [
            {
                id: 1001,
                author: '用户A',
                content: '这篇文章写得非常好，内容丰富，解释清晰。',
                date: '2023-11-15'
            },
            {
                id: 1000,
                author: '用户B',
                content: '非常感谢分享，我学到了很多新知识。',
                date: '2023-11-14'
            }
        ]
        loading.value = false
        hasMoreComments.value = true
    }, 1000)
}

// 加载更多评论
const loadMoreComments = () => {
    if (loadingMore.value || !hasMoreComments.value) {
        return
    }

    loadingMore.value = true

    // 获取当前评论列表的最后一个评论ID作为游标
    const lastCommentId = comments.value.length > 0
        ? comments.value[comments.value.length - 1].id
        : null

    console.log(`加载更多评论，最后评论ID: ${lastCommentId}`)

    // 模拟API调用
    setTimeout(() => {
        // 模拟返回的更多评论
        const moreComments: Comment[] = []

        // 根据lastCommentId生成一些模拟数据
        if (lastCommentId === 1000) {
            moreComments.push(
                {
                    id: 999,
                    author: '用户C',
                    content: '文章中的例子非常实用，我已经开始在项目中尝试这些技术了。',
                    date: '2023-11-13'
                },
                {
                    id: 998,
                    author: '用户D',
                    content: '这个主题很有深度，希望能看到更多相关内容。',
                    date: '2023-11-12'
                }
            )
        } else if (lastCommentId === 998) {
            moreComments.push(
                {
                    id: 997,
                    author: '用户E',
                    content: '我对第三部分有一些疑问，希望作者能够进一步解释。',
                    date: '2023-11-11',
                    replyTo: {
                        id: 999,
                        author: '用户C'
                    }
                },
                {
                    id: 996,
                    author: '用户F',
                    content: '文章的结构很清晰，逻辑性强，很容易理解。',
                    date: '2023-11-10'
                }
            )
            // 最后一批评论，设置没有更多
            hasMoreComments.value = false
        }

        // 将新加载的评论添加到列表末尾
        comments.value = [...comments.value, ...moreComments]
        loadingMore.value = false
    }, 800)
}

// 组件挂载时加载评论
onMounted(() => {
    loadComments()

    // 聚焦到评论框
    setTimeout(() => {
        const textarea = document.querySelector('.comment-textarea') as HTMLTextAreaElement
        if (textarea) {
            textarea.focus()
        }
    }, 100)
})

// 设置回复对象
const setReplyTo = (comment: Comment) => {
    replyTo.value = {
        id: comment.id,
        author: comment.author
    }
    // 聚焦到评论框
    setTimeout(() => {
        const textarea = document.querySelector('.comment-textarea') as HTMLTextAreaElement
        if (textarea) {
            textarea.focus()
        }
    }, 100)
}

// 取消回复
const cancelReply = () => {
    replyTo.value = null
}

// 提交评论
const submitComment = () => {
    if (!newComment.value.trim() || submitting.value) {
        return
    }

    submitting.value = true

    // 构建评论数据
    const commentData = {
        titleId: props.articleId,
        content: newComment.value,
        replyCommentId: replyTo.value?.id
    }

    console.log('提交评论:', commentData)

    // 模拟API调用
    setTimeout(() => {
        // 模拟服务器返回的新评论
        const comment: Comment = {
            // 使用时间戳作为临时ID
            id: Date.now(),
            author: '当前用户',
            content: newComment.value,
            date: new Date().toISOString().split('T')[0]
        }

        // 如果是回复其他评论
        if (replyTo.value) {
            comment.replyTo = {
                id: replyTo.value.id,
                author: replyTo.value.author
            }
        }

        // 将新评论添加到列表顶部
        comments.value.unshift(comment)
        newComment.value = ''
        replyTo.value = null
        submitting.value = false
    }, 500)
}
</script>

<template>
  <div class="mt-10">
    <h3 class="text-xl font-bold mb-6">{{ tMsg('blog.comments') }} ({{ comments.length }})</h3>

    <!-- 评论输入框 -->
    <div class="mb-8">
      <!-- 回复提示 -->
      <div v-if="replyTo" class="flex items-center justify-between mb-2 px-4 py-2 bg-muted rounded-t-lg">
        <div class="text-sm">
          {{ tMsg('blog.reply_to') }} <span class="font-medium">@{{ replyTo.author }}</span>
        </div>
        <button class="text-muted-foreground hover:text-foreground" @click="cancelReply">
          <Icon name="heroicons:x-mark" class="w-4 h-4"/>
        </button>
      </div>

      <textarea
        v-model="newComment"
        :placeholder="tMsg('blog.write_comment')"
        class="comment-textarea w-full p-4 border border-border rounded-lg bg-background focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors resize-none min-h-[100px]"
        :class="{ 'rounded-t-none': replyTo }"
      />
      <div class="flex justify-end mt-2">
        <button
          class="px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          :disabled="!newComment.trim() || submitting"
          @click="submitComment"
        >
          <span v-if="submitting">{{ tMsg('common.submitting') }}</span>
          <span v-else>{{ tMsg('blog.post_comment') }}</span>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="space-y-6">
      <div v-for="i in 3" :key="i" class="border-b border-border pb-6">
        <div class="flex items-start">
          <!-- 头像骨架 -->
          <Skeleton class="w-10 h-10 rounded-full mr-3"/>

          <!-- 内容骨架 -->
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-2">
              <Skeleton class="h-4 w-24"/>
              <Skeleton class="h-3 w-16"/>
            </div>
            <Skeleton class="h-4 w-full mb-1"/>
            <Skeleton class="h-4 w-5/6 mb-1"/>
            <Skeleton class="h-4 w-4/6"/>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div v-else class="space-y-6">
      <div v-for="comment in comments" :key="comment.id" class="border-b border-border pb-6">
        <div class="flex items-start">
          <!-- 头像 -->
          <div class="w-10 h-10 rounded-full overflow-hidden mr-3 bg-muted flex items-center justify-center">
            <img v-if="comment.avatar"
                 :src="comment.avatar"
                 alt="用户头像"
                 class="w-full h-full object-cover">
            <span v-else class="text-primary text-sm font-medium">{{ comment.author.charAt(0) }}</span>
          </div>

          <!-- 评论内容 -->
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <span class="font-medium">{{ comment.author }}</span>
              <span class="text-sm text-muted-foreground">{{ comment.date }}</span>
            </div>

            <!-- 如果是回复其他评论 -->
            <p v-if="comment.replyTo" class="mt-1 text-sm text-muted-foreground">
              {{ tMsg('blog.reply_to') }} <span class="text-foreground">@{{ comment.replyTo.author }}</span>
            </p>

            <p class="mt-2 text-foreground">{{ comment.content }}</p>
            <div class="mt-2 flex items-center text-sm text-muted-foreground">
              <button
                class="hover:text-foreground transition-colors"
                @click="setReplyTo(comment)"
              >
                {{ tMsg('blog.reply_to') }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多按钮 -->
      <div v-if="comments.length > 0 && hasMoreComments" class="flex justify-center">
        <button
          class="px-4 py-2 text-sm text-muted-foreground hover:text-foreground transition-colors disabled:opacity-50 flex items-center gap-1"
          :disabled="loadingMore"
          @click="loadMoreComments"
        >
          <Icon v-if="loadingMore" name="heroicons:arrow-path" class="w-4 h-4 animate-spin"/>
          <span v-if="loadingMore">{{ tMsg('common.loading') }}</span>
          <span v-else>{{ tMsg('blog.load_more') }}</span>
        </button>
      </div>

      <!-- 没有评论时的提示 -->
      <div v-if="comments.length === 0" class="text-center py-8 text-muted-foreground">
        {{ tMsg('blog.no_comments') }}
      </div>

      <!-- 全部加载完毕提示 -->
      <div v-if="comments.length > 0 && !hasMoreComments" class="text-center py-4 text-sm text-muted-foreground">
        {{ tMsg('blog.all_comments_loaded') }}
      </div>
    </div>
  </div>
</template>

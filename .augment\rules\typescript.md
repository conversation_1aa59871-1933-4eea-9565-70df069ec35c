---
type: "agent_requested"
description: "TypeScript前端项目的最佳实践和编码标准。为开发者提供了可操作的指导，以编写清晰、可维护和可扩展的TypeScript代码"
---

- 优先使用命名导出而不是默认导出，以提高可发现性和重构
- 反模式和代码异味:
    - 过度使用`any`类型: 尽可能避免使用`any`。使用更具体的类型、接口或泛型。
    - 过长的方法/函数: 将大型函数分解为更小、更易管理的单元。
    - 魔法数字/字符串: 对具有特定含义的值使用常量。
    - 重复代码: 将通用逻辑提取到可重用的/composables或/utils。
    - 忽略错误: 始终优雅地处理错误。不要只是捕获并忽略它们。
    - 代码缩进: .ts中缩进是4空格
- 错误处理模式:
    - 使用`try...catch`块处理潜在错误。
    - 使用Nuxt的错误处理系统，包括错误页面(已存在)和全局错误处理。
    - 使用TypeScript的类型系统减少运行时错误。
    - 对没有向上throw的异常，需要记录异常日志
- lint错误处理:
    - 不要把时间花在无关的代码格式处理工作上,比如空格,尾部分号(;)等处理,不用管这类错误,编辑器会自行处理
    - 仅对很严重的lint错误处理，如类型错误等
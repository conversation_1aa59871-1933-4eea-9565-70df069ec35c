<script setup lang="ts">
import footerConfig from '@/config/footer'
import siteConfig from '@/config/site'
import {getLocalizedConfigText, resolveLocalePath} from '@/utils/i18n'
import type {FooterConfig} from '@/types/site/footer'

// 定义 props
interface Props {

    // 是否显示站点详情
    showSiteDetails?: boolean;

    // 站点详情布局方式
    siteDetailsLayout?: 'top' | 'bottom' | 'left' | 'right';

    // 站点详情是否显示社交列表
    showSocialLinksInSiteDetails?: boolean;

    // 底部版权信息是否显示社交列表
    showSocialLinksInCopyright?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    showSiteDetails: true,
    siteDetailsLayout: 'left',
    showSocialLinksInSiteDetails: true,
    showSocialLinksInCopyright: false
})

// 计算站点详情是否优先显示
const isSiteDetailsFirst = computed(() => {
    return props.siteDetailsLayout === 'top' || props.siteDetailsLayout === 'left'
})

// 计算是否为水平排列
const isHorizontalArrangement = computed(() => {
    return props.siteDetailsLayout === 'left' || props.siteDetailsLayout === 'right'
})

// 使用类型断言确保 footerConfig 符合 FooterConfig 类型
const typedFooterConfig = footerConfig as FooterConfig
</script>

<template>
  <footer class="bg-card text-card-foreground transition-colors duration-200 py-16">
    <slot name="before-layout"/>

    <div class="container">
      <div class="flex flex-col md:grid md:grid-cols-12 gap-8">
        <!-- 站点详情 -->
        <div v-if="props.showSiteDetails"
             :class="[
               'col-span-1',
               isHorizontalArrangement ? 'md:col-span-3' : 'md:col-span-12',
               isSiteDetailsFirst ? 'order-1 md:order-1' : 'order-2 md:order-2']">
          <slot name="site-details-prepend" :site-config="siteConfig" :footer-config="typedFooterConfig"/>
          <slot name="site-details" :site-config="siteConfig" :footer-config="typedFooterConfig">
            <div class="flex flex-col items-start space-y-3 md:space-y-5 text-left">
              <slot name="site-info" :site-config="siteConfig" :footer-config="typedFooterConfig">
                <div class="flex items-center">
                  <img v-if="siteConfig.logo"
                       :src="siteConfig.logo"
                       alt="Logo"
                       class="w-8 h-8 mr-2">
                  <span class="hidden md:inline font-semibold">{{
                    getLocalizedConfigText(siteConfig.title)
                  }}</span>
                </div>
                <p class="text-sm">
                  {{ getLocalizedConfigText(typedFooterConfig.organizationDescription) }}
                </p>
              </slot>
              <slot name="site-social-links" :social-links="typedFooterConfig.socialLinks">
                <div v-if="props.showSocialLinksInSiteDetails" class="flex space-x-4 md:space-x-6">
                  <template v-for="social in typedFooterConfig.socialLinks" :key="social.href">
                    <a :href="social.href"
                       target="_blank"
                       class="transition-colors duration-200 text-muted-foreground/70 hover:text-foreground">
                      <span class="sr-only">{{ getLocalizedConfigText(social.title) }}</span>
                      <Icon :name="social.icon" class="w-5 h-5"/>
                    </a>
                  </template>
                </div>
              </slot>
            </div>
          </slot>
          <slot name="site-details-append" :site-config="siteConfig" :footer-config="typedFooterConfig"/>
        </div>

        <!-- 导航链接 -->
        <div :class="[
          'col-span-1 grid grid-cols-2 gap-8 md:grid-cols-4',
          props.showSiteDetails
            ? (isHorizontalArrangement ? 'md:col-span-9' : 'md:col-span-12')
            : 'md:col-span-12',
          props.showSiteDetails
            ? (isSiteDetailsFirst ? 'order-2 md:order-2' : 'order-1 md:order-1')
            : 'order-1 md:order-1'
        ]">
          <div v-for="(section, index) in typedFooterConfig.sections" :key="index" class="col-span-1 md:col-span-1">
            <slot name="section-header" :section="section" :localized-title="getLocalizedConfigText(section.title)">
              <h3 class="font-medium mb-3 md:mb-4 text-foreground">
                {{ getLocalizedConfigText(section.title) }}
              </h3>
            </slot>
            <ul class="space-y-2 md:space-y-3">
              <li v-for="(link, index) in section.links" :key="index">
                <slot name="nav-link"
                      :link="link"
                      :section="section"
                      :localized-link-title="getLocalizedConfigText(link.title)">
                  <a v-if="link.href.startsWith('http')"
                     :href="link.href"
                     target="_blank"
                     class="text-sm transition-colors duration-200 text-muted-foreground hover:text-foreground">
                    {{ getLocalizedConfigText(link.title) }}
                  </a>
                  <NuxtLink :to="resolveLocalePath(link.href)"
                            class="text-sm transition-colors duration-200 text-muted-foreground hover:text-foreground">
                    {{ getLocalizedConfigText(link.title) }}
                  </NuxtLink>
                </slot>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <slot name="copyright-prepend" :footer-config="typedFooterConfig"/>

      <!-- 版权信息 -->
      <div
        class="mt-4 md:mt-8 pt-4 md:pt-8 md:flex justify-between items-center transition-colors duration-200 border-t border-border">
        <slot name="copyright">
          <p class="text-sm text-muted-foreground/70 mb-4 md:mb-0">
            {{ getLocalizedConfigText(typedFooterConfig.copyright) }}
          </p>
        </slot>
        <div v-if="props.showSocialLinksInCopyright" class="flex space-x-4 md:space-x-6">
          <slot name="footer-social-links" :social-links="typedFooterConfig.socialLinks">
            <template v-for="social in typedFooterConfig.socialLinks" :key="social.href">
              <a :href="social.href"
                 target="_blank"
                 class="transition-colors duration-200 text-muted-foreground/70 hover:text-foreground">
                <span class="sr-only">{{ getLocalizedConfigText(social.title) }}</span>
                <Icon :name="social.icon" class="w-5 h-5"/>
              </a>
            </template>
          </slot>
        </div>
      </div>
    </div>
    <slot name="after-layout"/>
  </footer>
</template>

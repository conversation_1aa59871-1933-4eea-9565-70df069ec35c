API
ChatArea实例对象提供的方法
updateConfig
描述：当需要修改实例对象的配置项时，可以使用该方法更新配置
业务场景中的切换聊天框调用该api更新即可，无需多次实例化对象
api参数按需传入更新的配置项即可
<script>
    const chat = new ChatArea({
        ...
    })

    // 按需更新 如果只需要更新人员列表 只传入对应的更新项即可
    chat.updateConfig({
        userList: [{ userId: 'NEW1', userName: '新的人员' }]
    })

    chat.updateConfig({
        // 常用更新
        userList: [{ userId: 'NEW1', userName: '新的人员' }], // 更新用户列表数据
        needCallEvery: false, // 更新是否需要@所有人
        // 更多更新配置
        customTrigger: [], // 更新自定义触发符
        selectList: [], // 更新选择标签元素
        maxLength: 1000, // 更新文本长度限制
        copyType: ['text'], // 更新粘贴类型
        userProps: { id: 'userId', name: 'userName' }, // 更新用户数据值索引
        uploadImage: async () => { // 更新粘贴图片回调方法
            return 'img src'
        },
        placeholder: '新的提示语', // 更新提示语
        needCallSpace: true, // 更新@人员标签是否需要衔接空格
        wrapKeyFun: (event) => event.shiftKey && ['Enter', 'NumpadEnter'].includes(event.code), // 更新自定义折行
        sendKeyFun: (event) => !event.shiftKey && ['Enter', 'NumpadEnter'].includes(event.code) // 更新自定义发送
    })
</script>
enterSend
描述：当触发配置项的 发送键 时，将会触发该方法
<script>
    const chat = new ChatArea({
        ...
    })

    // 4.5.2+版本推荐使用事件订阅的形式
    chat.addEventListener('enterSend', () => {})

    // 旧版本赋值发送消息方法 （5.2.0+版本已彻底废除该方式，请使用上述的事件订阅形式）
    chat.enterSend = () => {}
</script>
getHtml
描述：获取聊天框精简后的html内容
<script>
    const chat = new ChatArea({
        ...
    })

    // 获取聊天框html内容
    const htmlMsg_1 = chat.getHtml()

    // 如果你的业务需要对@人员标签有特殊的交互处理
    const htmlMsg_2 = chat.getHtml({
        saveTagData: true, // 5.4.2+版本只需配置该属性即可保存所有数据信息
        needUserId: true, // 返回的html内容@人员标签上将保留对应的人员id
        needTagId: true, // 返回的html内容上自定义标签将保留对应的数据id
        needSelectId: true, // // 返回的html内容上下拉选择标签将保留对应的数据id
    })

    // 该方法将会自动识别聊天框内容中的链接并转化为a标签
    const htmlMsg_3 = chat.getHtml({
        identifyLink: true
    })

    // 除此之外你还可以为html最外层容器以及每一行级容器添加自己定义的class样式名
    const htmlMsg_4 = chat.getHtml({
        wrapClassName: 'demo-wrap',
        rowClassName: 'demo-row'
    })
</script>
getText
描述：获取聊天框精简后的文本内容
<script>
    const chat = new ChatArea({
        ...
    })

    // 获取聊天框text内容
    const textMsg_1 = chat.getText()

    // 假设当前聊天框内容为 111 并插入了一张表情图片
    chat.insertHtml('<img data-img-text="自定义文字" src="https://www.jianfv.top/image/bq1.gif">')

    // 方法返回值为 111[自定义文字]
    const textMsg_2 = chat.getText({
        imgToText: true
    })
</script>
getCallUserList
描述：获取聊天框内容里 配置项userList 所包含@人员标签的人员信息
返回的人员信息对应配置项传入的userList信息
当出现 @所有人标签 时 数组将会格外返回一个 id为: isALL 的成员
<script>
    const chat = new ChatArea({
        ...
    })

    // 获取聊天框选中的@人员，目标人员 必须 存在于userList集合中
    const callList = chat.getCallUserList()
</script>
getCallUserTagList
描述：获取聊天框内容里所有@人员标签的人员信息
返回的人员信息只包含id和name，同步userProps转义
本API只负责收集@聊天框中所有的@人员标签数据，小于5.1.0以下的版本需要自行对返回值进行去重处理
<script>
    const chat = new ChatArea({
        ...
    })

    // 获取聊天框选中的@人员，目标人员 无需 存在于userList集合中
    const callList = chat.getCallUserTagList()
</script>
getCustomTagList
描述：获取聊天框内容里所有自定义字符元素的标签信息
<script>
    const chat = new ChatArea({
        ...
    })

    // 获取聊天框选中的自定义标签
    const customTagList = chat.getCustomTagList()
</script>
getSelectTagList
描述：获取聊天框内容里所有下拉选择元素的标签信息
<script>
    const chat = new ChatArea({
        ...
    })

    // 获取聊天框选中的下拉选择标签
    const selectTagList = chat.getSelectTagList()
</script>
getInputTagList
描述：获取聊天框内容里所有输入元素的内容信息
<script>
    const chat = new ChatArea({
        ...
    })

     // 获取聊天框选中的输入标签内容
    const inputTags = chat.getInputTagList()
</script>
reverseAnalysis
描述：将getHtml生成的html片段进行逆向解析回填至聊天框，常用于消息撤回重新编辑功能
当业务场景需要使用该api时，请将getHtml里的人员id保留
<script>
    const chat = new ChatArea({
        ...
    })

    const sendHtml = chat.getHtml({
        needUserId: true // 发送的消息体需要保留人员id
        needTagId: true, // 如果有配置自定义触发符选择则需要保留标签id
        needSelectId: true // 如果有配置下拉选择数据则需要保留标签id
    })

    // 逆向解析回填至聊天框内
    chat.reverseAnalysis(sendHtml)

    // 第二配置项参数可以控制是否保留当前聊天框内容进行拼接回填内容
    chat.reverseAnalysis(sendHtml, true)
</script>
insertHtml
描述：往聊天框光标处插入html内容, 4.0.5+版本将会返回html在聊天框内生成的元素对象
您可以用该方法用来实现聊天框表情包、链接、卡片的功能
被插入的html属于一个整体 在退格删除的时候 这一块整体是一起删除
插入的html标签必须是 行内 或 行内块元素，如果需要块级元素标签 请自行插入行内元素然后修改其css属性为块级元素
因为如果复制的内容不符合HTML的语法规则，或者包含了不允许的行内元素包含块级元素的情况。会导致解析异常
﻿

请输入
<script>
    const chat = new ChatArea({
        ...
    })

    // 往聊天框光标处插入图片
    chat.insertHtml('<img width="auto" height="22px" style="vertical-align: bottom" src="https://www.jianfv.top/image/bq1.gif">')
    // 往聊天框光标处插入链接
    chat.insertHtml('<a href="https://www.baidu.com" target="_blank">链接跳转</a>')
</script>

<!-- 示例实现复杂html插入 -->
<script>
    // 创建chat实例对象
    const chat = new ChatArea({
        ...
    })

    // 向上获取含有绑定数据元素
    const getElmBindInfo = (node, max = 1) => {
      if (node.dataset && JSON.stringify(node.dataset) !== '{}') return node.dataset
      // 最大获取次数 按需修改
      if (max >= 4) return {}
      return getElmBindInfo(node.parentElement, max + 1)
    }

    // 文件卡片点击事件
    const fileCardClick = (event) => {
      const bindInfo = getElmBindInfo(event.target)
      if (bindInfo.type === 'fileCard') {
        // 您可以自行理你的业务逻辑, 需要其余值就在目标节点上绑定
        alert(JSON.stringify(bindInfo, undefined, 4))
      }
    }

    // 文件卡片插入方法
    const setFileCard = (fileInfo) => {
        // 往聊天框光标处插入文件卡片 (不能含有 div p 等此类块级标签)
        const fileCardElm = chat.insertHtml(
          `<span class="file-card" data-type="fileCard" data-file-url="${fileInfo.previewUrl}" data-name="${fileInfo.name}">
               <span class="flex-tool">
                  <img class="card-img" src="${fileInfo.type}" alt="">
                  <span class="file-info">
                      <span class="file-name">${fileInfo.name}</span>
                      <span class="file-size">${fileInfo.size}</span>
                  </span>
               </span>
          </span>`
        )

        console.log(fileCardElm, '生成的元素')
    }

    // 监听chat点击事件, 利用元素冒泡原理去触发文件卡片的点击操作
    chat.richText.addEventListener('click', fileCardClick)

    // 业务场景调用卡片插入
    setFileCard({
        name: '测试文件.xlsx',
        size: '10.24MB',
        previewUrl: 'https://www.baidu.com',
        type: 'image src'
    })
</script>

<!-- 简单的样式参考 -->
<style scoped lang="less">
    :deep(.file-card) {
      display: inline-block;
      padding: 20px;
      vertical-align: bottom;
      white-space: normal;
      width: 230px;
      margin: 4px;
      cursor: pointer;
      background: #FFFFFF;
      box-shadow: 0 3px 12px 0 rgba(0, 0, 0, .3);
      border-radius: 4px;
      transform: scale(0.98);
      transition: all .3s ease;
      &:hover {
        transform: scale(1);
        box-shadow: 0 3px 12px 0 rgba(0, 0, 0, .5);
      }

      .flex-tool {
        display: flex;
        align-items: center;
      }

      .card-img {
        width: 40px;
        height: 48px;
      }

      .file-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-left: 16px;

        .file-name {
          font-size: 16px;
          color: #272E3A;
        }

        .file-size {
          font-size: 14px;
          color: rgba(39, 46, 58, .5);
        }
      }
    }
</style>
insertText
描述：往聊天框光标处插入文本内容
<script>
    const chat = new ChatArea({
        ...
    })

    // 往聊天框光标处插入自定义文本
    chat.insertText('插入文本')
</script>
setUserTag
描述：往聊天框光标处插入@人员标签元素
如果配置项userList使用userProps转接过，那可以直接传入转接的格式
<script>
    const chat = new ChatArea({
        ...
    })

    // 往聊天框光标处插入一个@人员标签
    chat.setUserTag({ id: '1', name: '松松' })

    // 如使用userProps转接过
    const diffChat = new ChatArea({
        userProps: {
            id: 'userId',
            name: 'userName'
        }
    })
    // 那么插入数据结构与其一致
    diffChat.setUserTag({ userId: '1', userName: '松松' })
</script>
setCustomTag
描述：往聊天框光标处插入自定义标签元素
<script>
    const chat = new ChatArea({
        ...
    })

    // 往聊天框光标处插入一个自定义标签
    chat.setCustomTag(
        { id: '1', name: '股票趋势' }, // 自定义标签数据
        '#' // 对应的prefix
    )
</script>
setSelectTag
描述：往聊天框光标处插入下拉选择标签元素
<script>
    const chat = new ChatArea({
        ...
    })

    // 往聊天框光标处插入一个下拉选择标签
    chat.setSelectTag(
        { id: '1', name: '人像摄影' }, // 下拉选择项数据
        'style' // 对应key
    )
</script>
setInputTag
描述：在聊天框光标处插入一个输入标签元素
<script>
    const chat = new ChatArea({
        ...
    })

    // 往聊天框光标处插入一个可输入的标签
    chat.setInputTag(
        'jop', // 输入内容对应的key值
        '请输入你的工作', // placeholder
        '开发' // 默认值（非必传）
    )
</script>
delUserTags
描述：移除聊天框内容里对应的@人员标签元素
<script>
    const chat = new ChatArea({
        ...
    })

    // 移除对应@人员标签
    chat.delUserTags(['userId1', 'userId2']) // 传入对应的用户id集合

    // 不传入id集合则默认移除全部
    chat.delUserTags()
</script>
delCustomTags
描述：移除聊天框内容里对应的自定义标签元素
<script>
    const chat = new ChatArea({
        ...
    })

    // 移除对应自定义标签
    chat.delCustomTags(
        '#', // 对应的prefix
        ['customId1', 'customId2'] // 传入对应的自定义标签id集合
    )

    // 不传入id集合则默认移除全部
    chat.delCustomTags('#')
</script>
delSelectTags
描述：移除聊天框内容里对应的下拉选择标签元素
<script>
    const chat = new ChatArea({
        ...
    })

    // 移除对应下拉标签
    chat.delSelectTags(
        'style', // 对应的key
        ['selectId1', 'selectId2'] // 传入对应的自定义标签id集合
    )

    // 不传入id集合则默认移除全部
    chat.delSelectTags('style')
</script>
delInputTags
描述：移除聊天框内容里对应的输入标签元素
<script>
    const chat = new ChatArea({
        ...
    })

    // 移除对应key的输入标签
    chat.delInputTags(['jop', 'name'])

    // 不传入key则移除全部输入标签
    chat.delInputTags()
</script>
openTipTag
描述：在聊天框前置区域唤起一个提示标签
﻿

请输入
<script>
    const chat = new ChatArea({
        ...
    })

    // 唤起前置提示标签
    chat.openTipTag({
        tagLabel: '图像生成',
        popoverLabel: '点击退出技能',
        codeLabel: 'ESC' // 仅展示作用，如需要按键关闭功能 请自行监听对应按键调用closeTip即可
    })
</script>
closeTipTag
描述：关闭已唤起的前置提示标签
<script>
    const chat = new ChatArea({
        ...
    })

    // 关闭前置提示标签
    chat.closeTipTag()
</script>
clear
描述：清空聊天框内容
<script>
    const chat = new ChatArea({
        ...
    })

    // 清空聊天框
    chat.clear()

    // 清空聊天框内容 并 显示传入的文本内容
    chat.clear('文本内容')
</script>
isEmpty
描述：获取聊天框内容是否为空
<script>
    const chat = new ChatArea({
        ...
    })

    // 识别聊天框内容是否为空
    const isEmpty = chat.isEmpty()

    // 识别聊天框内容是否为空 并 识别非多个空格
    const isSpaceEmpty= chat.isEmpty(true)
</script>
dispose
描述：用于页面或组件销毁时卸载在body内挂载的元素并释放实例对象
<script>
    const chat = new ChatArea({
        ...
    })

    // 释放实例
    chat.dispose()
</script>
disabled
描述：禁止聊天框编辑
<script>
    const chat = new ChatArea({
        ...
    })

    // 禁止用户编辑聊天区域
    chat.disabled()
</script>
enable
描述：允许聊天框编辑并将光标定向到聊天内容末尾
<script>
    const chat = new ChatArea({
        ...
    })

    // 允许用户编辑聊天区域
    chat.enable()
</script>
addEventListener
描述：为实例对象添加事件订阅
<script>
    const chat = new ChatArea({
        ...
    })

    // 当用户键盘触发发送操作时
    chat.addEventListener('enterSend', () => {})
    // 当用户对聊天框进行操作时, 包含聊天框内容发生变化，执行了粘贴复制撤销，调用了插入聊天框内容的api等操作
    chat.addEventListener('operate', () => {})

    // 5.0.0+版本支持获取@后置输入内容事件（*需要开启对应配置项：asyncMatch）
    chat.addEventListener('atMatch', async (str) => {
        return [] // 返回需要渲染的用户列表
    })

    // 5.0.7+版本新增监听事件
    // 各类标签选择完成并渲染到聊天框 之前 事件
    chat.addEventListener('atCheck', (userTags) => {
        console.log(userTags, '本次触发监听所选的用户信息集合')
    })
    chat.addEventListener('tagCheck', (customTag, type) => {
        console.log(customTag, '本次触发监听所选的标签')
        console.log(type, '本次触发监听所选标签的类别')
    })
    chat.addEventListener('selectCheck', (selectTag, type) => {
        console.log(selectTag, '本次触发监听下拉选择弹窗所选的标签')
        console.log(type, '本次触发监听所选标签的类别')
    })

    // 5.5.1+版本新增监听事件
    // 各类标签选择完成并渲染到聊天框 之后 事件
    chat.addEventListener('afterAtCheck', (userTags) => {})
    chat.addEventListener('afterTagCheck', (customTag, type) => {})
    chat.addEventListener('afterSelectCheck', (selectTag, type) => {})

    // 在关闭内置弹窗交互后 使用自定义弹窗时 所提供的便捷事件
    chat.addEventListener('showAtDialog', () => {}) // 识别到需要唤起@提及弹窗交互
    chat.addEventListener('showTagDialog', (type) => {}) // 识别到需要唤起自定义标签弹窗交互
    chat.addEventListener('showSelectDialog', (type, elm) => {}) // 识别到需要唤起下拉选择标签弹窗交互

    // 5.5.9+版本支持捕获用户具体在聊天区域内点击了哪一个元素
    chat.addEventListener('elementClicked', (type, elm) => {})

    // 4.5.5+版本支持 4.6.1+版本整合事件名 根据对应的标识返回 PREVENT 将阻止内置交互
    chat.addEventListener('defaultAction', (type) => {
        switch (type) {
            case 'COPY': // 复制
                return 'PREVENT'
            case 'CUT': // 剪切
                return 'PREVENT'
            case 'PASTE': // 粘贴
                return 'PREVENT'
            case 'UNDO': // 撤销
                return 'PREVENT'
            case 'REDO': // 恢复
                return 'PREVENT'
        }
    })
</script>
removeEventListener
描述：移除实例对象已订阅的事件
<script>
    const chat = new ChatArea({
        ...
    })

    // 卸载已订阅的事件需要函数内存地址指向一致

    // 正确示例
    const onOperate = () => {
        console.log('聊天框进行了操作')
    }
    chat.addEventListener('operate', onOperate)
    chat.removeEventListener('operate', onOperate)

    // 错误示例
    chat.addEventListener('operate', () => {
        console.log('聊天框进行了操作')
    })
    chat.removeEventListener('operate', () => {
        console.log('聊天框进行了操作')
    })
</script>
showPCPointDialog
描述：PC唤起人员光标选择弹窗
<script>
    const chat = new ChatArea({
        ...
    })

    // 在当前聊天框光标处插入@字符并唤起人员光标选择弹窗，该api只允许pc端使用
    chat.showPCPointDialog()
</script>
showPCCheckDialog
描述：PC唤起人员多选选择弹窗
<script>
    const chat = new ChatArea({
        ...
    })

    // 唤起人员多选弹窗，该api只允许pc端使用
    chat.showPCCheckDialog()
</script>
showPCCustomTagDialog
描述：唤起PC自定义触发符选择弹窗
<script>
    const chat = new ChatArea({
        ...
    })

    // 唤起PC自定义触发符选择弹窗，该api只允许pc端使用 入参值与配置项customTrigger中的prefix对应
    chat.showPCCustomTagDialog('#')
</script>
showPCSelectDialog
描述：唤起PC下拉选择标签弹窗
<script>
    const chat = new ChatArea({
        ...
    })

    // 唤起PC下拉选择标签弹窗 该api只允许pc端使用
    chat.showPCSelectDialog(
        'key', // 取值来源配置项selectList中定义的key值
        document.getElementById('elmId'), // 下拉选择弹窗基于目标元素出现
    )
</script>
showH5Dialog
描述：H5唤起人员多选选择弹窗
<script>
    const chat = new ChatArea({
        ...
    })

    // 唤起人员多选弹窗，该api只允许h5端使用
    chat.showH5Dialog()
</script>
undo
描述：撤销上一次聊天框操作
使用ctrl + z会默认调用该api
调用clear方法将会清空撤销栈
<script>
    const chat = new ChatArea({
        ...
    })

    // 执行撤销
    chat.undo()
</script>
redo
描述：恢复上一次撤销的聊天框操作
使用ctrl + y会默认调用该api
调用clear方法将会清空恢复栈
<script>
    const chat = new ChatArea({
        ...
    })

    // 执行恢复
    chat.redo()
</script>
cursorMove
描述：使当前光标移动X格位置
<script>
    const chat = new ChatArea({
        ...
    })

    // 当前光标位置 向右移动 10 格
    chat.cursorMove(10)

    // 当前光标位置 向左移动 10 格
    chat.cursorMove(-10)
</script>
cursorDel
描述：使当前光标退格删除X格位置
<script>
    const chat = new ChatArea({
        ...
    })

    // 当前光标位置 向右删除 10 格
    chat.cursorDel(10)

    // 当前光标位置 向左删除 10 格
    chat.cursorDel(-10)
</script>
revisePCPointDialogLabel
描述：修改PC光标选择弹窗默认文案
<script>
    const chat = new ChatArea({
        ...
    })

    // 修改PC光标选择弹窗默认文案
    chat.revisePCPointDialogLabel({
        title: '群成员',
        callEveryLabel: '所有人' // 5.2.0+
        checkLabel: '多选',
        emptyLabel: '暂无数据'
    })
</script>
revisePCCheckDialogLabel
描述：修改PC多选选择弹窗默认文案
<script>
    const chat = new ChatArea({
        ...
    })

    // 修改PC多选选择弹窗默认文案
    chat.revisePCCheckDialogLabel({
        title: '选择要@的人',
        searchPlaceholder: '搜素人员名称',
        searchEmptyLabel: '没有匹配到任何结果',
        userTagTitle: '研讨成员列表',
        checkAllLabel: '全选',
        checkEmptyLabel: '请选择需要@的成员',
        confirmLabel: '确定',
        cancelLabel: '取消'
    })
</script>
reviseH5DialogLabel
描述：修改H5选择弹窗默认文案
<script>
    const chat = new ChatArea({
        ...
    })

    // 修改H5选择弹窗默认文案
    chat.reviseH5DialogLabel({
        title: '选择提醒的人',
        callEveryLabel: '所有人' // 5.2.0+
        searchPlaceholder: '搜素人员名称',
        searchEmptyLabel: '没有匹配到任何结果',
        confirmLabel: '确定',
        cancelLabel: '收起'
    })
</script>
Contributors：
JianFv
Last Updated：
2025/7/10 16:27
配置项
ChatNode

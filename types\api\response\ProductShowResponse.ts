import type {PayModeEnum} from '~/utils/constants/enums/PayModeEnum'

export interface ProductShowResponse {
    /**
     * 价格 最小货币单位 根据currency
     */
    price: number;

    /**
     * 商品id
     */
    id: number;

    /**
     * ISO 4217 货币 小写
     */
    currency: string;

    /**
     * 支付模式
     */
    paymentMode: PayModeEnum;
    /**
     * 订阅类型
     */
    subscriptionType?: string;

    /**
     * 购买是否需要登录
     */
    loginLimit?: boolean;

    /**
     * 单次最大购买数量
     */
    maxBuyCount?: number;

    /**
     * 支付周期 仅当SUBSCRIPTION有效 单位天
     */
    payPeriod?: number;

    /**
     * 是否是主推
     */
    popularStatus?: boolean

    /**
     * 参数
     */
    params?: string;

    /**
     * 是否允许折扣
     */
    allowDiscount: boolean;

    /**
     * 小数
     */
    discountRate?: number;
}
import base64
from pywinauto.application import Application
import pywinauto.keyboard as keyboard
import time
import win32clipboard
import win32con
from PIL import Image
import io
import os
import tempfile


def _copy_image_to_clipboard(filepath):
    """将图片文件路径复制到剪贴板"""
    try:
        if not isinstance(filepath, str) or not os.path.exists(filepath):
            raise ValueError("Invalid image file path provided.")
            
        image = Image.open(filepath)
        output = io.BytesIO()
        # 转换为位图(BMP)格式存入内存
        image.convert("RGB").save(output, "BMP")
        data = output.getvalue()[14:]  # BMP文件头有14字节，需要去掉
        output.close()

        win32clipboard.OpenClipboard()
        win32clipboard.EmptyClipboard()
        # DIB: Device-Independent Bitmap，设备无关位图
        win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
        win32clipboard.CloseClipboard()
        return True
    except Exception as e:
        print(f"复制图片到剪贴板失败: {e}")
        return False

def _copy_files_to_clipboard(file_path):
    """
    将文件路径复制到剪贴板，以便粘贴为附件。
    注意：此功能依赖于文件系统路径，无法直接处理字节流。
    """
    try:
        if not (isinstance(file_path, str) and os.path.exists(file_path)):
             raise ValueError("File content for 'file' type must be a valid file path.")
        
        win32clipboard.OpenClipboard()
        win32clipboard.EmptyClipboard()
        # CF_HDROP format requires a double-null-terminated string list.
        encoded_path = (file_path + '\0\0').encode('utf-16le')
        win32clipboard.SetClipboardData(win32con.CF_HDROP, encoded_path)
        win32clipboard.CloseClipboard()
        return True
    except Exception as e:
        print(f"复制文件到剪贴板失败: {e}")
        return False


class QianNiuRPA:
    def __init__(self):
        self.workbench_window = None
        self.chat_window = None
        self.close_button = None
        self.connect()
        
    @property
    def app(self):
        """Dynamically get the application object from an existing window."""
        if self.workbench_window and self.workbench_window.exists():
            return self.workbench_window.app
        if self.chat_window and self.chat_window.exists():
            return self.chat_window.app
        return None

    def connect(self, retry_count=30, retry_delay_seconds=2):
        """
        Connects to the QianNiu application. Handles cases where the app is
        not running, or windows are closed (running in system tray).
        """
        print("正在尝试连接到千牛...")
        app = None
        try:
            # Try connecting via WorkbenchView first, as it's the main entry point
            app = Application(backend="uia").connect(class_name="WorkbenchView", timeout=3)
            print("成功通过工作台(WorkbenchView)连接。")
        except Exception as e:
            print(f"连接 WorkbenchView 失败: {e}，尝试连接 ChatView...")
            try:
                # If WorkbenchView is not found, try connecting via ChatView
                app = Application(backend="uia").connect(class_name="ChatView", timeout=2)
                print("成功通过聊天窗口(ChatView)连接。")
            except Exception as e2:
                print(f"连接 ChatView 也失败: {e2}")
                app = None
        
        if not app:
            print("未找到现有窗口。正在尝试启动或从系统托盘唤醒...")
            try:
                os.startfile("aliim:login")
                print(f"已发送启动/唤醒指令。将在 {retry_count * retry_delay_seconds} 秒内循环检测...")
            except Exception as e:
                print(f"通过协议 'aliim:login' 启动千牛失败: {e}。请确认千牛已安装。")
                return False

            for i in range(retry_count):
                print(f"检测窗口... (第 {i+1}/{retry_count} 次尝试)")
                try:
                    app = Application(backend="uia").connect(class_name="WorkbenchView", timeout=2)
                    print("检测到工作台(WorkbenchView)并成功连接！")
                    break
                except Exception as e:
                    # This failure is expected during startup. Logging for debug purposes.
                    try:
                        app = Application(backend="uia").connect(class_name="ChatView", timeout=2)
                        print("检测到聊天窗口(ChatView)并成功连接！")
                        break
                    except Exception as e2:
                        # Log message indicates this is an expected part of the retry loop
                        if i % 5 == 0: # Log every 5 attempts to avoid spam
                             print(f"重试中... (Workbench: {type(e).__name__}, Chat: {type(e2).__name__})")
                        time.sleep(retry_delay_seconds)
        
        if not app:
            print("超时错误: 未能连接到千牛。请确认您已登录。")
            self.workbench_window = None
            self.chat_window = None
            return False

        # If connected, get window handles
        if app.window(class_name="WorkbenchView").exists():
            self.workbench_window = app.window(class_name="WorkbenchView")
        if app.window(class_name="ChatView").exists():
            self.chat_window = app.window(class_name="ChatView")
            
        return True


    def _check_and_prepare_workbench_window(self):
        """Checks connection and ensures the workbench window is accessible."""
        try:
            current_app = self.app
            if not current_app or not current_app.is_process_running():
                print("千牛进程连接已丢失，正在尝试重新连接...")
                return self.connect()

            if not self.workbench_window or not self.workbench_window.exists():
                print("工作台(WorkbenchView)句柄丢失或未初始化，尝试重新查找...")
                workbench = current_app.window(class_name="WorkbenchView")
                if workbench.exists(timeout=2):
                    self.workbench_window = workbench
                    print("成功找到工作台窗口。")
                else:
                    print("错误：无法找到工作台(WorkbenchView)窗口。无法打开新聊天。")
                    return False
            
            if self.workbench_window.is_minimized():
                print(f"工作台窗口 '{self.workbench_window.window_text()}' 已最小化，正在恢复...")
                self.workbench_window.restore()
                time.sleep(0.5)

            return True
        except Exception as e:
            print(f"准备工作台窗口时出错: {e}")
            self.workbench_window = None
            return False
            
    def _find_chat_window_full_search(self):
        """Finds the active chat window by performing a full, slow search."""
        print("正在执行全量搜索查找活动的聊天窗口...")
        current_app = self.app
        if not current_app:
            print("未连接到千牛应用，无法查找聊天窗口。")
            return None

        try:
            # Prioritize finding by the specific class name for accuracy
            chat_window = current_app.window(class_name="ChatView")
            if chat_window.exists(timeout=0.2) and chat_window.is_visible():
                print(f"通过 ClassName 'ChatView' 找到聊天窗口: '{chat_window.window_text()}'")
                self.chat_window = chat_window # Update cache
                return chat_window
        except Exception as e:
            print(f"通过 ClassName 'ChatView' 查找时出错: {e}。将继续使用备用方法。")

        print("通过 ClassName 'ChatView' 查找失败，将使用备用方法(查找'发送'按钮)...")
        try:
            # Fallback: iterate all top-level windows
            for window in current_app.windows(top_level_only=True):
                # This should look for "发送" (Send) to identify an open CHAT window.
                if window.child_window(title="发送", control_type="Button").exists(timeout=0.1):
                    print(f"通过'发送'按钮找到符合条件的聊天窗口: '{window.window_text()}'")
                    self.chat_window = window # Update cache
                    return window
            print("未找到任何打开的聊天窗口。")
            self.chat_window = None
            self.close_button = None
            return None
        except Exception as e:
            print(f"通过'发送'按钮查找聊天窗口时出错: {e}")
            self.chat_window = None
            self.close_button = None
            return None

    def _get_or_open_chat_window(self):
        """
        Gets a handle to the chat window. Uses an optimistic cache-first approach
        to improve performance.
        """
        # --- 步骤 1: 乐观地使用缓存 ---
        if self.chat_window:
            try:
                # set_focus() is a lightweight validation. It will fail if the window is gone.
                self.chat_window.set_focus()
                # If we are here, the cached window is valid. No print needed to keep logs clean.
                return self.chat_window
            except Exception as e:
                print(f"缓存的聊天窗口已失效({type(e).__name__})，将执行全量搜索...")
                self.chat_window = None
                self.close_button = None

        # --- 步骤 2: 执行全量搜索 ---
        if self._find_chat_window_full_search():
            print("通过全量搜索找到聊天窗口。")
            self.chat_window.set_focus()
            return self.chat_window

        # --- 步骤 3: 尝试从工作台打开 ---
        print("未找到聊天窗口，正在尝试从工作台打开...")
        if not self._check_and_prepare_workbench_window():
            print("无法准备工作台窗口，不能打开新聊天。")
            return None

        try:
            self.workbench_window.set_focus()
            print("正在工作台查找'接待中心'按钮以打开聊天窗口...")
            # User has changed this to use title, respecting this change.
            messages_button = self.workbench_window.child_window(
                title="接待中心",
                control_type="Button"
            ).wait('visible', timeout=5)
            print(f"找到 '{messages_button.window_text()}' 按钮，正在 invoke...")
            messages_button.invoke()

            # --- Handle the optional "re-open previous messages" dialog ---
            try:
                # Check for the dialog window non-blockingly. Title contains "询问".
                inquiry_dialog = self.app.window(
                    class_name="UIMessageBox"
                )
                if inquiry_dialog.exists(timeout=2):
                    print("检测到'重新打开消息'弹窗，正在点击'取消'...")
                    cancel_button = inquiry_dialog.child_window(title="取消", control_type="Button")
                    cancel_button.invoke()
                    print("'取消'按钮已点击。")
                    time.sleep(0.5) # Allow dialog to close
            except Exception as e:
                # This is not a critical error, just means the dialog didn't appear or couldn't be handled.
                print(f"未检测到'询问'弹窗或处理时出错(可忽略): {e}")
            
            time.sleep(2)

            print("再次尝试全量搜索查找聊天窗口...")
            if self._find_chat_window_full_search():
                print("成功打开并找到聊天窗口。")
                self.chat_window.set_focus()
                return self.chat_window
            else:
                print("点击'消息'按钮后，仍然未能找到聊天窗口。")
                return None
        except Exception as e:
            print(f"尝试从工作台打开聊天窗口时失败: {e}")
            print("请确认工作台上是否有'消息'按钮，或者修改rpa.py以匹配正确的按钮名称。")
            return None
            
    def _execute_send_action(self, input_box):
        """
        Executes the send action by focusing the input box and sending 'Enter'.
        """
        # --- 方法1: 模拟回车键 (首选) ---
        # 根据UI提示，这是最直接且最可靠的方式。
        try:
            input_box.set_focus()
            time.sleep(0.1)
            keyboard.send_keys('{ENTER}')
            print("已通过 'Enter' 键发送。")
            return True
        except Exception as e:
            print(f"通过 'Enter' 键发送失败: {e}。")
            return False

    def send_message(self, auto_close=False):
        chat_window = self._get_or_open_chat_window()
        if not chat_window:
            return {"code": 500, "message": "无法找到或打开聊天窗口"}

        # For sending, we always find the input box relative to the current window
        # to ensure we're targeting a live element, not a stale cached one.
        print("查找输入框 (TextRichEdit) 以便发送...")
        # Use a shorter retry_interval for better performance in the common case.
        input_box = chat_window.child_window(class_name="TextRichEdit").wait('ready', timeout=5, retry_interval=0.1)

        if self._execute_send_action(input_box):
            if auto_close:
                print("准备自动关闭窗口...")
                time.sleep(0.5)
                self.close_chat_window(chat_window)
            return {"code": 200, "message": "消息已发送"}
        else:
            return {"code": 500, "message": "发送失败，尝试了所有方法。"}


    def paste_and_send(self, content_bytes, content_type, auto_close=False, filename="file"):
        """
        Handles pasting content from a byte stream.
        For both 'image' and 'file' types, it saves the bytes to a temporary file
        to ensure reliable clipboard operations.
        """
        chat_window = self._get_or_open_chat_window()
        if not chat_window:
            return {"code": 500, "message": "无法找到或打开聊天窗口"}

        temp_file_path = None
        try:
            # Step 1: Prepare the content and put it on the clipboard.
            # This is done first, as it doesn't require any window focus.
            _ , extension = os.path.splitext(filename)
            with tempfile.NamedTemporaryFile(delete=False, suffix=extension, prefix="rpa_") as tmp:
                tmp.write(content_bytes)
                temp_file_path = tmp.name
            print(f"内容已保存到临时文件: {temp_file_path}")

            if content_type == 'image':
                if not _copy_image_to_clipboard(temp_file_path):
                    raise Exception("无法将图片临时文件复制到剪贴板")
            
            elif content_type == 'file':
                if not _copy_files_to_clipboard(temp_file_path):
                    raise Exception("无法将文件临时文件复制到剪贴板")

            # Step 2: Focus the window and the specific input control.
            chat_window.set_focus()
            
            # Always find the input box relative to the current window for robustness.
            print("正在查找输入框 (TextRichEdit)...")
            # Use a shorter retry_interval for better performance in the common case.
            input_box = chat_window.child_window(class_name="TextRichEdit").wait('ready', timeout=5, retry_interval=0.1)

            input_box.set_focus()
            time.sleep(0.2) # Allow a moment for focus to settle.
            
            # Step 3: Perform the paste action.
            print("正在执行粘贴操作...")
            keyboard.send_keys('^v')
            
            # Step 4: Send the message.
            time.sleep(1) # Wait for the paste to render in the UI.
            
            if self._execute_send_action(input_box):
                # After sending, the app needs time to process the file from the temp path
                # before we delete it in the finally block. This is not needed for images,
                # as their raw data is on the clipboard, not just a path reference.
                if content_type == 'file':
                    print("文件发送指令已发出，等待2秒以便千牛处理文件...")
                    time.sleep(2)

                if auto_close:
                    self.close_chat_window(chat_window)
                return {"code": 200, "message": f"粘贴并发送 {content_type} 成功"}
            else:
                 return {"code": 500, "message": "粘贴后发送失败"}

        except Exception as e:
            print(f"粘贴{content_type}失败: {e}")
            return {"code": 500, "message": f"粘贴失败: {e}"}
        finally:
            if temp_file_path and os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                print(f"临时文件 {temp_file_path} 已删除。")

    def close_chat_window(self, window_to_close=None):
        """Closes the specified chat window, using a cache for the close button."""
        if window_to_close is None:
            window_to_close = self._find_chat_window_full_search()
            if not window_to_close:
                return {"code": 200, "message": "没有需要关闭的聊天窗口。"}
             
        try:
            window_to_close.set_focus()

            # --- Optimistic Caching for the Close Button ---
            # Use cached button if it exists and belongs to the window we're closing.
            if self.close_button and self.close_button.exists() and self.chat_window == window_to_close:
                 print("使用缓存的'关闭'按钮。")
                 close_button = self.close_button
            else:
                # Otherwise, find it. This is the slower path.
                print(f"在窗口 '{window_to_close.window_text()}' 中查找'关闭'按钮...")
                close_button = window_to_close.child_window(
                    auto_id="UIWindow.centralwidget.SubChatView.ChatDisplayWidget.ChatContentView.splitter.sendMsgWidget.enterAreaKeyWidget.closecon",
                    control_type="Button"
                ).wait('visible', timeout=5)
                # Cache the button only if it belongs to our main cached window.
                if self.chat_window == window_to_close:
                    self.close_button = close_button

            print("找到窗口关闭按钮，正在 invoke...")
            close_button.invoke()
            print("'关闭'按钮已点击。")

            # Clear all caches related to the closed window.
            self.chat_window = None
            self.close_button = None
            return {"code": 200, "message": "窗口已关闭"}
        except Exception as e:
            print(f"关闭窗口失败: {e}")
            # Also clear caches on failure, as handles may be invalid.
            self.chat_window = None
            self.close_button = None
            return {"code": 500, "message": f"关闭窗口失败: {e}"}

if __name__ == '__main__':
    # This part is for direct testing of the script
    # To test, run "python rpa.py" in the terminal
    print("--- RPA 模块直接测试 ---")
    rpa = QianNiuRPA()
    if rpa.app:
        print("\n连接成功，你可以取消下面的注释来进行单元测试。")
        # Example: Test sending a message after 5 seconds
        # print("将在5秒后测试发送消息...")
        # time.sleep(5)
        # rpa.send_message(auto_close=False)
    else:
        print("\n连接失败，请确保千牛已运行并重试。") 
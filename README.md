# 前端项目

基于Nuxt 3构建的现代化前端项目，集成了TypeScript、Tailwind CSS和shadcn-vue组件库。

## 项目技术栈

- **框架**: [Nuxt 3](https://nuxt.com)
- **UI组件**: [shadcn-vue](https://www.shadcn-vue.com/)
- **样式**: [Tailwind CSS](https://tailwindcss.com/)
- **状态管理**: [Pinia](https://pinia.vuejs.org/)
- **类型检查**: [TypeScript](https://www.typescriptlang.org/)
- **代码规范**: [ESLint](https://eslint.org/)

## 开发准备

### 安装依赖
```bash
# npm
npm install
```

### 启动开发服务器
```bash
# npm
npm run dev
```

应用会在 [http://localhost:3000](http://localhost:3000) 运行

### 生产环境构建
```bash
# npm
npm run build
```

在本地预览生产构建：
```bash
npm run preview
```

## 代码规范与格式化

本项目使用 ESLint 进行代码规范检查和自动修复，并使用 husky 与 lint-staged 在提交代码前自动运行 lint。

### ESLint 命令

检查代码规范问题：
```bash
npm run lint:check
```

自动修复代码规范问题：
```bash
npm run lint
```

修复特定文件：
```bash
npx eslint path/to/file.ts --fix
```

### Git 提交规范

项目配置了 Git 钩子，会在提交前自动运行 ESLint 检查并尝试修复。如果有无法自动修复的问题，提交会被阻止，需要手动修复后再次提交。

## 编码规范说明

项目遵循以下编码规范：

- **缩进**: 4个空格，switch-case语句有额外的缩进
- **括号**: 所有控制结构必须使用大括号，即使只有一行代码
- **引号**: 使用单引号
- **组件命名**: PascalCase (如 `MyComponent.vue`)
- **变量命名**: camelCase (如 `myVariable`)
- **注释**: 代码注释应当位于代码上方，而非行尾

完整的ESLint规则可在 `eslint.config.mjs` 文件中查看。

## 相关文档

- [Nuxt 3 文档](https://nuxt.com/docs/getting-started/introduction)
- [Vue 3 文档](https://vuejs.org/guide/introduction.html)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)

# HTTP请求模块

本模块提供了一个基于fetch的HTTP请求封装，符合Nuxt.js最佳实践，支持错误处理、登录过期处理、请求头设置等功能。

## 主要特性

- 基于原生fetch API实现，符合Nuxt.js推荐
- 支持四种基本HTTP方法：GET、POST、PUT、DELETE
- 统一的错误处理和响应格式
- 自动处理登录过期和token刷新
- 支持TypeScript类型安全
- 提供批量请求功能
- 客户端懒加载请求支持

## 目录结构

```
utils/
  ├── constants/http/
  │   ├── content-types.ts    # 内容类型常量
  │   ├── request-headers.ts  # 请求头常量
  │   ├── status-codes.ts     # 状态码常量
  │   └── index.ts            # 导出所有常量
  │
  ├── http/
  │   ├── auth.ts             # 认证相关逻辑
  │   ├── request.ts          # 核心请求封装
  │   ├── types.ts            # 类型定义
  │   └── index.ts            # 导出API方法
  │
  └── api/
      ├── crud.ts             # 通用CRUD接口
      └── baseApi.ts          # 业务API示例

composables/
  └── useApi.ts               # 组合式函数封装

plugins/
  └── api.ts                  # 全局API插件

server/
  ├── api/auth/
  │   └── refresh-token.ts    # 刷新token接口
  │
  └── api/examples/
      └── user-crud.ts        # 示例API
```

## 使用方法

### 基本使用

```typescript
// 直接导入方法使用
import { Get, Post, Put, Delete } from '~/utils/http'

// GET请求
const response = await Get('/api/users', { page: 1, limit: 10 })

// POST请求
const createResponse = await Post('/api/users', { name: '张三', age: 25 })

// PUT请求
const updateResponse = await Put('/api/users/1', { name: '李四' })

// DELETE请求
const deleteResponse = await Delete('/api/users/1')
```

### 使用组合式函数

```typescript
// 在组件中使用组合式函数
const { get, post, put, delete: del } = useApi()

// GET请求
const response = await get('/api/users', { page: 1, limit: 10 })

// POST请求
const createResponse = await post('/api/users', { name: '张三', age: 25 })
```

### 客户端懒加载请求

```typescript
// 仅在客户端渲染时使用，不会阻塞SSR
import { LazyGet } from '~/utils/http'

// 在setup中使用
const { data, pending, refresh, error } = LazyGet('/api/users', { page: 1 })
```

### 批量请求

```typescript
import { batch } from '~/utils/http'

// 批量发送多个请求
const responses = await batch([
  { url: '/api/users', method: 'GET' },
  { url: '/api/posts', method: 'GET' }
])
```

## 错误处理

请求模块内置了统一的错误处理机制，会自动处理常见的HTTP错误和业务逻辑错误。

- 网络错误：自动提示网络连接失败
- 超时错误：自动提示请求超时
- 业务错误：显示业务错误信息
- 登录过期：自动尝试刷新token，如果刷新失败则跳转到登录页面

## 类型安全

所有方法都支持泛型，可以指定响应数据的类型：

```typescript
interface User {
  id: number
  name: string
  age: number
}

// 指定响应数据类型
const response = await Get<User>('/api/users/1')

// 类型安全的访问
if (response.code === 200) {
  const user = response.data
  console.log(user.name) // 类型安全
}
```
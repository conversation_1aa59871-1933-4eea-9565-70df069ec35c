// BentoGrid组件的配置数据
import type {BentoGridConfig} from '~/types/site/bentoGrid'

export const bentoGridConfig: BentoGridConfig = {
    items: [
        // 第一行
        [
            {
                proportion: 3,
                imageUrl: 'https://tailwindcss.com/plus-assets/img/component-images/bento-02-releases.png',
                title: {
                    zh: '实时部署监控实时部署监控实时部署监控',
                    en: 'Real-time Deployment Monitoring'
                },
                description: {
                    zh: '通过GitHub Actions实时跟踪您的项目部署状态，包括成功、失败和进行中的部署。通过GitHub Actions实时跟踪您的项目部署状态，包括成功、失败和进行中的部署。通过GitHub Actions实时跟踪您的项目部署状态，包括成功、失败和进行中的部署。通过GitHub Actions实时跟踪您的项目部署状态，包括成功、失败和进行中的部署。通过GitHub Actions实时跟踪您的项目部署状态，包括成功、失败和进行中的部署。通过GitHub Actions实时跟踪您的项目部署状态，包括成功、失败和进行中的部署。',
                    en: 'Track your project deployment status in real-time through GitHub Actions, including successful, failed, and in-progress deployments.'
                },
                type: {
                    zh: '实时更新',
                    en: 'Live Update'
                },
                icon: 'lucide:activity'
                // backgroundColor: {light: 'bg-green-900', dark: 'bg-blue-600'}
            },
            {
                proportion: 2,
                type: {
                    zh: '集成',
                    en: 'Integrations'
                },
                title: {
                    zh: '连接您喜爱的工具',
                    en: 'Connect Your Favorite Tools'
                },
                description: {
                    zh: '无缝集成Gmail, Microsoft Teams, Slack, Adobe Creative Cloud等常用工具，提升工作效率。',
                    en: 'Seamlessly integrate with Gmail, Microsoft Teams, Slack, Adobe Creative Cloud and other commonly used tools to improve work efficiency.'
                },
                imageUrl: 'https://tailwindcss.com/plus-assets/img/component-images/bento-02-integrations.png',
                icon: 'lucide:puzzle'
                // backgroundColor: {light: 'bg-blue-300', dark: 'bg-red-500'}
            }
        ],
        // 第二行
        [
            {
                proportion: 1.7,
                type: {
                    zh: '发布',
                    en: 'Releases'
                },
                title: {
                    zh: '一键推送部署',
                    en: 'One-click Deploy'
                },
                description: {
                    zh: '精简的部署流程，从代码提交到线上发布，一键完成，高效可靠。本区域文字较多用于测试排版效果，确保内容显示完整。',
                    en: 'Streamlined deployment process, from code commit to online release, completed with one click, efficient and reliable. This area has more text to test typography effects and ensure content displays completely.'
                },
                imageUrl: 'https://tailwindcss.com/plus-assets/img/component-images/bento-02-security.png',
                icon: 'lucide:send-to-back'
                // backgroundColor: {light: 'bg-lime-700', dark: 'bg-purple-900'}
            },
            {
                proportion: 3,
                title: {
                    zh: '项目概览与性能',
                    en: 'Project Overview & Performance'
                },
                description: {
                    zh: '关键指标一览：405次部署，平均部署耗时3.65秒，当前服务节点3个。体验闪电般的构建速度。',
                    en: 'Key metrics at a glance: 405 deployments, average deployment time of 3.65 seconds, 3 current service nodes. Experience lightning-fast build speeds.'
                },
                imageUrl: 'https://tailwindcss.com/plus-assets/img/component-images/bento-02-performance.png',
                type: {
                    zh: '仪表盘指标',
                    en: 'Dashboard Metrics'
                }
                // backgroundColor: {light: 'bg-blue-300', dark: 'bg-green-900'}
            }
        ],
        // 第三行
        [
            {
                proportion: 1,
                type: {
                    zh: '安全',
                    en: 'Security'
                },
                title: {
                    zh: '高级访问控制',
                    en: 'Advanced Access Control'
                },
                description: {
                    zh: '通过精细化的权限管理和多重验证机制，确保您的项目数据和操作安全无虞。',
                    en: 'Ensure the security of your project data and operations through fine-grained permission management and multi-factor authentication mechanisms.'
                },
                imageUrl: 'https://tailwindcss.com/plus-assets/img/component-images/bento-01-integrations.png',
                icon: 'lucide:shield-check'
                // backgroundColor: {light: 'bg-blue-300', dark: 'bg-pink-900'}
            },
            {
                proportion: 1,
                type: {
                    zh: '性能优化',
                    en: 'Performance Optimization'
                },
                title: {
                    zh: '闪电般的构建',
                    en: 'Lightning-fast Builds'
                },
                description: {
                    zh: '采用先进的构建技术和缓存策略，大幅缩短项目构建时间，加速产品迭代。',
                    en: 'Significantly reduce project build time and accelerate product iteration through advanced build technologies and caching strategies.'
                },
                imageUrl: 'https://tailwindcss.com/plus-assets/img/component-images/bento-01-network.png'
                // backgroundColor: {light: 'bg-sky-50', dark: 'bg-violet-700'}
            },
            {
                proportion: 1,
                type: {
                    zh: '移动友好',
                    en: 'Mobile Friendly'
                },
                title: {
                    zh: '移动端优化',
                    en: 'Mobile Optimized'
                },
                description: {
                    zh: '完美适配各种移动设备，为用户提供一致且高质量的跨平台体验。',
                    en: 'Perfectly adapts to various mobile devices, providing users with a consistent and high-quality cross-platform experience.'
                },
                imageUrl: 'https://tailwindcss.com/plus-assets/img/component-images/bento-03-mobile-friendly.png',
                icon: 'lucide:smartphone'
                // backgroundColor: {light: 'bg-blue-300', dark: 'bg-red-500'}
            }
        ]
    ]
}
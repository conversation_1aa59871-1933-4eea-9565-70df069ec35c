<script setup lang="ts">
import {cn} from '@/lib/utils'
import {computed, type HTMLAttributes} from 'vue'

// 定义头像对象的接口
interface Avatar {
    imageUrl: string
    profileUrl: string
}

// 定义组件的 props
interface AvatarCirclesProps {
    class?: HTMLAttributes['class']
    numPeople?: number
    avatarUrls: Avatar[]
    avatarHeightClass?: string
    avatarWidthClass?: string,
    showPeopleNum?: boolean
}

const props = withDefaults(defineProps<AvatarCirclesProps>(), {
    avatarHeightClass: 'h-10',
    avatarWidthClass: 'w-10',
    showPeopleNum: true
})

// 计算属性：从 avatarHeightClass 提取数字部分，例如从 "h-10" 提取 10
const numericClassValue = computed(() => {
    const match = props.avatarHeightClass?.match(/-(\d+)$/)
    // 如果匹配成功，则解析数字，否则默认为 10 (对应 h-10/w-10)
    return match && match[1] ? parseInt(match[1], 10) : 10
})

// 计算属性：用于 img 标签的 width 和 height 属性
const calculatedImgDimension = computed(() => {
    return numericClassValue.value * 4
})

// 计算属性，用于格式化额外人数的显示
const formattedNumPeopleDisplay = computed(() => {
    if (typeof props.numPeople !== 'number' || props.numPeople <= 0) {
        return ''
    }

    // 使用 Intl.NumberFormat 进行紧凑格式化，并允许最多两位小数
    const formatter = new Intl.NumberFormat('en', {
        notation: 'compact',
        compactDisplay: 'short',
        maximumFractionDigits: 2
    })

    // 格式化并返回，保留默认的大写 K
    return formatter.format(props.numPeople)
})

// 新增：根据 numericClassValue 动态计算精确的字体大小 (px)
// 基准：当 numericClassValue 为 30 时，字号为 24px。
// 比例：24px / 30 = 0.8
// 最小字号：8px
const calculatedFontSize = computed(() => {
    const targetSize = numericClassValue.value * 0.8
    const finalSize = Math.max(targetSize, 8)
    return `${finalSize}px`
})
</script>

<template>
  <div :class="cn('flex -space-x-5 rtl:space-x-reverse', props.class)">
    <!-- 在头像组之前插入内容的插槽 -->
    <slot name="before" :num-people="props.numPeople" :avatar-urls="avatarUrls"/>

    <!-- 新增：用于自定义头像列表渲染的作用域插槽 -->
    <slot name="avatars-list"
          :avatars="avatarUrls"
          :avatar-height-class="props.avatarHeightClass"
          :avatar-width-class="props.avatarWidthClass"
          :calculated-img-dimension="calculatedImgDimension">
      <!-- 默认的头像列表渲染：遍历头像链接并展示图片 -->
      <a v-for="(avatar, index) in avatarUrls"
         :key="index"
         :href="avatar.profileUrl"
         target="_blank"
         rel="noopener noreferrer">
        <img :class="[
               'rounded-full border-2 border-white dark:border-gray-800',
               props.avatarHeightClass,
               props.avatarWidthClass,
             ]"
             :width="calculatedImgDimension"
             :height="calculatedImgDimension"
             :src="avatar.imageUrl"
             :alt="`Avatar ${index + 1}`">
      </a>
    </slot>

    <!-- 如果 numPeople 大于 0，则显示额外人数的标识或自定义溢出指示器 -->
    <template v-if="(props.numPeople ?? 0) > 0 && showPeopleNum">
      <slot name="overflow" :count-display="formattedNumPeopleDisplay" :num-people="props.numPeople">
        <!-- 默认的溢出指示器 -->
        <a :class="[
             'flex items-center justify-center rounded-full border-2 p-0.5 text-center font-bold border-white bg-black text-primary-foreground dark:border-gray-800 dark:bg-white dark:text-black',
             props.avatarHeightClass,
             props.avatarWidthClass,
           ]"
           :style="{ fontSize: calculatedFontSize }">
          +{{ formattedNumPeopleDisplay }}
        </a>
      </slot>
    </template>

    <!-- 在头像组之后插入内容的插槽 -->
    <slot name="after" :num-people="props.numPeople" :avatar-urls="avatarUrls"/>

  </div>

</template>
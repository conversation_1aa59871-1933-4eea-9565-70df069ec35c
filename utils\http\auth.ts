// 导入i18n工具函数
import {localStg} from '~/utils/localStorageService'
import {LocalStorageConstant} from '~/utils/constants/localStorage'

// 用于记录当前是否正在刷新token
const isRefreshing = false
// 存储等待token刷新的请求队列
const waitingQueue: Array<{ resolve: (token: string) => void; reject: (error: any) => void }> = []
// 默认最大重试次数
const MAX_RETRY_COUNT = 5

/**
 * 获取认证Token
 * 优先从cookie获取，并确保在客户端和服务端都能正常工作
 */
export function getToken(): string | null {
    // 在服务端渲染期间不使用cookie
    if (import.meta.server) {
        return null
    }

    // 客户端环境下从cookie获取token
    // const cookies = document.cookie.split(';')
    // const tokenCookie = cookies.find(c => c.trim().startsWith('token='))

    // if (tokenCookie) {
    //     return tokenCookie.split('=')[1].trim()
    // }

    return localStg.get(LocalStorageConstant.TOKEN)
}

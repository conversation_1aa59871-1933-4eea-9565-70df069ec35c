# 文件上传功能使用示例

本文档展示如何使用HTTP请求工具进行文件上传操作。最新优化的文件上传功能支持以下特性：

1. 单文件上传
2. 多文件上传
3. 混合数据上传（文件+普通数据）
4. 智能类型检测与转换

## 简单文件上传

以下示例展示如何上传单个文件：

```typescript
import { request } from '~/utils/http/request'

// 在表单或文件选择器中获取文件
const fileInput = document.getElementById('fileInput') as HTMLInputElement
const file = fileInput.files?.[0]

if (file) {
  // 创建包含文件的数据对象
  const formData = {
    file: file,
    fileName: file.name,
    fileType: file.type
  }

  // 发送文件上传请求，将文件数据放在FormData中
  const result = await request('/api/upload', 'POST', formData, {
    additionalFormData: {}, // 指定空对象即可启用FormData模式
    showError: true
  })

  if (result.code === 200) {
    console.log('文件上传成功:', result.data)
  }
}
```

## 多文件上传

以下示例展示如何上传多个文件：

```typescript
import { request } from '~/utils/http/request'

// 获取多个文件
const fileInput = document.getElementById('multiFileInput') as HTMLInputElement
const files = fileInput.files

if (files && files.length > 0) {
  // 创建包含多文件的数据对象
  const formData = {
    files: Array.from(files), // 转换FileList为数组
    description: '多文件上传',
    uploadType: 'multiple'
  }

  // 发送多文件上传请求
  const result = await request('/api/upload/multiple', 'POST', formData, {
    additionalFormData: {} // 启用FormData处理模式
  })

  if (result.code === 200) {
    console.log('多文件上传成功:', result.data)
  }
}
```

## 在Vue组件中使用

以下是在Vue组件中使用文件上传的示例：

```vue
<template>
  <div>
    <h2>文件上传</h2>
    <input type="file" @change="handleFileChange" />
    <button @click="uploadFile" :disabled="!selectedFile">上传</button>
    
    <div v-if="uploadResult">
      <p>上传结果: {{ uploadResult.message }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { request } from '~/utils/http/request'

const selectedFile = ref(null)
const uploadResult = ref(null)

const handleFileChange = (event) => {
  selectedFile.value = event.target.files[0]
}

const uploadFile = async () => {
  if (!selectedFile.value) return
  
  try {
    // 创建上传数据
    const uploadData = {
      file: selectedFile.value,
      uploadTime: new Date().toISOString(),
      // 可以同时上传普通数据
      metadata: {
        userId: 1001,
        department: '技术部',
        tags: ['文档', '重要']
      }
    }
    
    // 发送请求
    const response = await request('/api/files/upload', 'POST', uploadData, {
      additionalFormData: {} // 启用FormData处理模式
    })
    
    uploadResult.value = response
  } catch (error) {
    console.error('上传失败:', error)
  }
}
</script>
```

## 实现细节说明

最新的文件上传功能提供了以下改进：

1. **智能类型识别**：自动检测File和Blob类型，以及它们的数组
2. **深度类型转换**：
    - 单个文件直接添加到FormData
    - 文件数组会被正确处理
    - 对象会被转换为JSON字符串
    - 数组的每个元素都添加为同名字段
    - 基本类型自动转为字符串
3. **内容类型处理**：自动设置正确的Content-Type或删除以允许浏览器自动处理
4. **混合数据支持**：可以同时上传文件和其他表单数据，无需手动创建FormData
5. **额外数据合并**：可以通过additionalFormData参数添加额外的表单数据，会与主数据合并

## 注意事项

1. 使用 `additionalFormData` 选项（即使是空对象）来启用FormData模式处理上传数据
2. 文件上传请求会自动设置正确的 `multipart/form-data` Content-Type
3. 可以同时上传文件和其他表单数据
4. 大文件上传可能需要显示进度，请考虑使用适当的UI组件显示上传进度
5. 当使用additionalFormData时，如果与主数据中有同名字段，主数据的值会优先使用 
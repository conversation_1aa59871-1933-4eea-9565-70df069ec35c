# LogoCloudOne 组件使用文档

`LogoCloudOne` 是一个用于展示合作伙伴Logo墙的Vue组件，旨在灵活适应不同的展示需求，并支持通过配置文件进行内容管理。

## Props (属性)

以下是 `LogoCloudOne` 组件可接受的 props列表：

| Prop 名称             | 类型                     | 描述                                                                             | 可选值                  | 默认值      |
|:--------------------|:-----------------------|:-------------------------------------------------------------------------------|:---------------------|:---------|
| `mobileDisplayMode` | `'grid'` \| `'scroll'` | 控制移动设备上 Logo 的显示模式。"grid" 表示网格布局，"scroll" 表示自动无限滚动布局。在非移动设备上，通常会优先考虑或强制使用网格布局。 | `'grid'`, `'scroll'` | `'grid'` |

## 数据配置

`LogoCloudOne` 组件的内容（包括默认的组件标题以及Logo列表）主要由 `/config/logoCloud.ts` (或同名 `.json`
文件，具体取决于项目配置)驱动。

在该配置文件中，通常包含：

- `title`: `I18nTextMap` 类型，用于设置组件的默认主标题，支持中英文等国际化文本。
- `logos`: 一个对象数组，每个Logo对象通常包含：
    - `name`: `string` (Logo的名称或品牌名，也用作`alt`文本)
    - `svgUrl`: `string` (Logo图片的URL，推荐使用SVG格式以保证清晰度)
    - `height?`: `string | number` (可选，用于在默认模板中控制图片高度)

组件内部会使用 `getLocalizedConfigText` 工具函数 (通常来自 `@/utils/i18n`) 根据当前i18n语言环境自动加载对应的标题文本。Logo的其他信息（如
`name`, `svgUrl`）通常直接使用。

## 插槽 (Slots)

为了提供更高级的自定义能力，`LogoCloudOne` 组件提供了以下插槽：

- **`before_title` (作用域插槽)**: 在组件主标题区域之前插入内容。
    - **作用域数据**:
        - `logoConfig`: `LogoCloudConfig` - 从配置文件加载的完整Logo配置对象。

- **`title` (作用域插槽)**: 完全自定义组件的主标题区域。会覆盖默认的标题显示。
    - **作用域数据**:
        - `localizedTitle`: `string` - 根据当前语言环境本地化后的标题文本。
        - `logoConfig`: `LogoCloudConfig` - 完整的Logo配置对象。

- **`before_logo`**: 在Logo列表容器（无论是滚动模式还是网格模式）之前插入内容。

- **`scroll-logo-item` (作用域插槽)**: 自定义在移动设备"滚动"(`scroll`)模式下，单个Logo项的渲染。
    - **作用域数据**:
        - `logo`: `{ name: string, svgUrl: string, ... }` - 当前正在迭代的单个Logo对象。

- **`grid-logo-item` (作用域插槽)**: 自定义在"网格"(`grid`)模式下（通常用于桌面端，或移动端选择grid模式时），单个Logo项的渲染。
    - **作用域数据**:
        - `logo`: `{ name: string, svgUrl: string, ... }` - 当前正在迭代的单个Logo对象。

- **`after_logo` (作用域插槽)**: 在Logo列表容器之后插入内容。
    - **作用域数据**:
        - `logoConfig`: `LogoCloudConfig` - 完整的Logo配置对象。

## 基本用法示例

```vue

<template>
		<div>
				<!-- 在移动端使用滚动模式 -->
				<LogoCloudOne mobile-display-mode="scroll"/>
				
				<!-- 在移动端使用网格模式 (默认行为) -->
				<LogoCloudOne mobile-display-mode="grid"/>
		</div>
</template>

<script setup lang="ts">
		// LogoCloudOne 组件通常会自动导入 (Nuxt 3项目特性)
		// import LogoCloudOne from '~/components/market/logo-cloud/LogoCloudOne.vue';
</script>
```

## 使用自定义插槽示例

以下示例展示了如何使用一些关键插槽来自定义 `LogoCloudOne` 组件：

```vue

<template>
		<LogoCloudOne mobile-display-mode="scroll">
				<!-- 自定义标题前的内容 -->
				<template #before_title="{ logoConfig }">
						<div class="text-center p-2 bg-gray-100 dark:bg-gray-800 rounded-t-lg">
								<p class="text-xs text-gray-500 dark:text-gray-400">
										特别鸣谢以下 {{ logoConfig.logos.length }} 家合作伙伴 (来自 before_title 插槽)
								</p>
						</div>
				</template>
				
				<!-- 完全自定义标题区域 -->
				<template #title="{ localizedTitle, logoConfig }">
						<div class="my-6 text-center">
								<h2 class="text-3xl font-extrabold text-purple-600 dark:text-purple-400">
										{{ localizedTitle }} ({{ logoConfig.logos.length }}家精选)
								</h2>
								<p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
										感谢他们的鼎力支持 (来自 title 插槽)
								</p>
						</div>
				</template>
				
				<!-- 自定义滚动模式下的Logo项 -->
				<template #scroll-logo-item="{ logo }">
						<div class="flex flex-col items-center justify-center p-4 mx-2 my-2 
                  border border-dashed border-purple-300 dark:border-purple-700 
                  rounded-lg shadow-sm hover:shadow-md transition-shadow">
								<img :src="logo.svgUrl"
													:alt="logo.name"
													class="h-12 object-contain"
													loading="lazy">
								<p class="mt-2 text-xs text-purple-700 dark:text-purple-300 font-medium">
										{{ logo.name }} (S)
								</p>
						</div>
				</template>
				
				<!-- 自定义网格模式下的Logo项 (仅在grid模式或大屏幕下显示) -->
				<template #grid-logo-item="{ logo }">
						<div class="flex flex-col items-center justify-center p-5 
                  border border-solid border-gray-200 dark:border-gray-700 
                  rounded-xl bg-gray-50 dark:bg-gray-800 
                  hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 ease-in-out transform hover:scale-105">
								<img :src="logo.svgUrl"
													:alt="logo.name"
													class="max-h-14 w-full object-contain"
													loading="lazy">
								<p class="mt-3 text-sm text-gray-800 dark:text-gray-200 font-semibold">
										{{ logo.name }} (G)
								</p>
						</div>
				</template>
				
				<!-- 自定义Logo区域后的内容 -->
				<template #after_logo="{ logoConfig }">
						<div class="text-center mt-8 mb-4">
								<button @click="handleMorePartners"
																class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
										探索更多 ({{ logoConfig.logos.length }} 家)
								</button>
						</div>
				</template>
		</LogoCloudOne>
</template>

<script setup lang="ts">
		// import LogoCloudOne from '~/components/market/logo-cloud/LogoCloudOne.vue';
		
		function handleMorePartners() {
				console.log('查看更多合作伙伴...');
				// 实际应用中可能会导航到新页面或执行其他操作
		}
</script>
```

**注意**: 上述插槽示例中的CSS类名主要基于Tailwind CSS。实际项目中请根据您的样式系统调整。 
/**
 * 用户信息接口
 */
export interface User {
    /**
     * 用户id
     */
    userId: string;
    /**
     * 用户名
     */
    username: string;
    /**
     * 昵称
     */
    nickname?: string;
    /**
     * 用户标签
     */
    userTag?: string;

    /**
     * 用户手机号(已脱敏)
     */
    phone?: string;

    /**
     * 用户邮箱(已脱敏)
     */
    email?: string;

    /**
     * 头像
     */
    avatar?: string;

    /**
     * 项目名
     */
    projectName?: string;

    /**
     * 第三方账号平台名
     */
    thirdPartName?: string;

    /**
     * 创建时间
     */
    createTime: string | Date;

    /**
     * 密码设置状态
     */
    passwordStatus?: boolean;
}
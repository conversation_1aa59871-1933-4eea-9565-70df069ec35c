import type {I18nTextMap} from '~/types/i18n'

// 单个Bento格子项的接口
export interface BentoItemConfig {
    proportion: number;
    type?: I18nTextMap;
    title: I18nTextMap;
    description: I18nTextMap;
    imageUrl?: string;
    icon?: string;
    backgroundColor?: {
        // 亮色模式下的Tailwind类名 (例如 'bg-blue-100')
        light: string;
        // 暗色模式下的基础Tailwind类名 (例如 'bg-blue-900', 不带 'dark:' 前缀)
        dark: string;
    };
}

// 整个BentoGrid配置的接口
export interface BentoGridConfig {
    items: BentoItemConfig[][];
}

// 本地化后的单个Bento格子项
export interface LocalizedBentoItem {
    proportion: number;
    type?: string;
    title: string;
    description: string;
    imageUrl?: string;
    icon?: string;
    backgroundColor?: {
        light: string;
        dark: string;
    };
}
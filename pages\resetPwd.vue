<script setup lang="ts">
import {computed, ref} from 'vue'
import {Button} from '@/components/ui/button'
import {Card, CardContent} from '@/components/ui/card'
import {Input} from '@/components/ui/input'
import {Label} from '@/components/ui/label'
import {useI18n} from 'vue-i18n'

const {t} = useI18n()

const newPassword = ref('')
const confirmPassword = ref('')
const isSubmitting = ref(false)
const formSubmitted = ref(false)

// 密码匹配验证
const passwordsMatch = computed(() => {
    if (!confirmPassword.value) {
        return true
    }
    // 确认密码为空时不显示错误
    return newPassword.value === confirmPassword.value
})

// 表单验证
const isFormValid = computed(() => {
    return newPassword.value && confirmPassword.value && passwordsMatch.value
})

// 提交表单
const handleSubmit = () => {
    formSubmitted.value = true

    if (!isFormValid.value) {
        return
    }

    isSubmitting.value = true

    // 这里打印密码，实际使用时会调用API
    console.log('密码重置成功:', {
        newPassword: newPassword.value,
        confirmPassword: confirmPassword.value
    })

    // 模拟API调用延迟
    setTimeout(() => {
        isSubmitting.value = false
        // 这里可以添加成功提示或跳转
    }, 1000)
}
</script>

<template>
  <div class="flex min-h-screen items-center justify-center p-4">
    <Card class="mx-auto w-full max-w-md">
      <CardContent class="p-6">
        <div class="flex flex-col gap-6">
          <div class="flex flex-col text-center space-y-2">
            <h1 class="text-2xl font-bold">{{ t('auth.labels.reset_password') }}</h1>
            <p class="text-muted-foreground text-sm">
              {{ t('auth.labels.reset_new_subtitle') }}
            </p>
          </div>

          <form class="space-y-4" @submit.prevent="handleSubmit">
            <div class="space-y-2">
              <Label for="new-password">{{ t('auth.labels.reset_new_password') }}</Label>
              <Input
                id="new-password"
                v-model="newPassword"
                type="password"
                placeholder="请输入新密码"
                required
              />
            </div>

            <div class="space-y-2">
              <Label for="confirm-password">{{ t('auth.labels.reset_confirm_password') }}</Label>
              <Input
                id="confirm-password"
                v-model="confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                required
                :class="{ 'border-red-500': formSubmitted && !passwordsMatch }"
              />
              <p v-if="formSubmitted && !passwordsMatch" class="text-sm text-red-500 mt-1">
                {{ t('auth.labels.reset_password_mismatch') }}
              </p>
            </div>

            <Button
              type="submit"
              class="w-full"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? t('auth.labels.resetSubmitting') : t('auth.labels.reset_confirm_btn') }}
            </Button>
          </form>

          <div class="text-center text-sm">
            <a href="/login" class="text-primary underline underline-offset-4 hover:text-primary/90">
              {{ t('auth.labels.reset_back_login') }}
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

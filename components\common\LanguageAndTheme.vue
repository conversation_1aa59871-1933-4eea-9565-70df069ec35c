<script setup lang="ts">

// i18n 国际化
const {locales, locale, setLocale, t} = useI18n()
const currentLanguageName = computed(() => {
    const languageList = locales.value as Array<{
        code: string,
        name: string,
        language: string
    }>
    for (let i = 0; i < languageList.length; i++) {
        if (languageList[i].code === locale.value) {
            return languageList[i].name
        }
    }

    return undefined
})

// 切换语言
const switchLocale = (code: string) => {
    setLocale(code as never)
}

// 暗黑模式
const colorMode = useColorMode()

// 切换主题
const toggleTheme = (theme: string) => {
    colorMode.preference = theme
}

</script>

<template>
  <div>
    <div class="flex items-center justify-between">
      <!-- 移动端语言切换 -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <button class="flex items-center">
            <Icon name="uil:globe" class="h-5 w-5 mr-2"/>
            <span>{{ currentLanguageName }}</span>
            <Icon name="uil:angle-down" class="h-4 w-4 ml-1"/>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" class="w-[160px]">
          <DropdownMenuRadioGroup :model-value="locale" @update:model-value="switchLocale">
            <DropdownMenuRadioItem v-for="localeItem in locales"
                                   :key="localeItem.code"
                                   :value="localeItem.code"
                                   class="cursor-pointer">
              {{ localeItem.name }}
            </DropdownMenuRadioItem>
          </DropdownMenuRadioGroup>
        </DropdownMenuContent>
      </DropdownMenu>

      <!-- 移动端主题切换 -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <button class="flex items-center relative">
            <Icon name="uil:moon" class="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"/>
            <Icon name="uil:sun"
                  class="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"/>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem @click="toggleTheme('light')">
            <Icon name="material-symbols:light-mode"/>
            <span>{{ t("theme.theme_light") }}</span>
          </DropdownMenuItem>
          <DropdownMenuItem @click="toggleTheme('dark')">
            <Icon name="material-symbols:dark-mode-rounded"/>
            <span>{{ t("theme.theme_dark") }}</span>
          </DropdownMenuItem>
          <DropdownMenuItem @click="toggleTheme('system')">
            <Icon name="material-symbols:computer"/>
            <span>{{ t("theme.theme_system") }}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>

<style scoped></style>

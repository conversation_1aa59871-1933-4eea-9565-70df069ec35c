<script setup lang="ts">
import {navigateTo, useRuntimeConfig} from '#app'
import type {AuthSourceConfigItem} from '~/types/site/authSourceConfig'
import {authSourceSettings} from '~/config/authSources'
import {getLocalizedConfigText, resolveLocalePath, tMsg} from '~/utils/i18n'
import logger from '~/utils/logger'
import {authApi} from '~/utils/api/authApi'
import type {ApiResponse} from '~/utils/http'
import {loginRegisterApi} from '~/utils/api/loginRegisterApi'
import {userAccountApi} from '~/utils/api/userAccountApi'
import {useMessage} from '~/composables/useMessage'
import {localStg} from '~/utils/localStorageService'
import {LocalStorageConstant} from '~/utils/constants/localStorage'
import type {LoginResultResponse} from '~/types/api/response/LoginResultResponse'
import {captchaApi} from '~/utils/api/captchaApi'
import {STATUS_CODES} from '~/utils/constants/http'
import {projectConfig} from '~/config/projectConfig'

// 定义步骤类型
type LoginStep = 'initialEmail' | 'passwordOrRegister' | 'verifyCode'

// 定义账户信息查询结果类型
interface AccountStatusInfo {
    emailExists: boolean
    hasPassword?: boolean
    username?: string
}

interface FormInfo {
    mode: number,
    contact?: string
}

const emits = defineEmits<{
    'update-form-mode': [modeValue: FormInfo]
}>()

const runtimeConfig = useRuntimeConfig()
const {message: displayMessage} = useMessage()

// 当前流程步骤
const currentStep = ref<LoginStep>('initialEmail')

// 表单数据
const email = ref('')
const password = ref('')
// 用于邮箱验证码
const verificationCode = ref<string[]>([])

// 通用加载状态，主要用于第一步查询账户
const isLoading = ref(false)
// 用于第二/三步的提交操作
const isSubmitting = ref(false)
const isRequestingCode = ref(false)
const codeCooldown = ref(0)
let codeCooldownTimer: NodeJS.Timeout | null = null

// 账户查询结果
const accountStatus = ref<AccountStatusInfo | null>(null)

// OAuth 相关
const allAvailableAuthSources = ref<AuthSourceConfigItem[]>([])
const showMoreAuthSources = ref(false)
const isLoadingAuthSources = ref(true)
const apiBaseUrl = computed(() => (runtimeConfig.public.apiBase as string))
// 用于原生校验
const emailInputRef = ref<{ $el: HTMLInputElement } | null>(null)

// --- OAuth 登录源加载---
onMounted(async () => {
    isLoadingAuthSources.value = true
    try {
        const response: ApiResponse<string[]> = await authApi.getAuthSourceList()
        let backendAuthSourceKeys: string[] = []
        if (response.code === 200 && response.data) {
            backendAuthSourceKeys = response.data
        } else {
            logger.error('Failed to fetch auth source list from API:', response)
        }

        const configuredSourcesMap = authSourceSettings.reduce((map, item) => {
            map.set(item.key, item)
            return map
        }, new Map<string, AuthSourceConfigItem>())

        const mergedSources: AuthSourceConfigItem[] = []
        if (backendAuthSourceKeys.length > 0) {
            backendAuthSourceKeys.forEach((key) => {
                const configured = configuredSourcesMap.get(key)
                if (configured) {
                    mergedSources.push(configured)
                } else {
                    mergedSources.push({
                        key,
                        name: {en: key.charAt(0).toUpperCase() + key.slice(1), zh: key},
                        icon: 'mdi:link-variant',
                        order: 999,
                        defaultVisible: false
                    })
                }
            })
            allAvailableAuthSources.value = mergedSources.sort((a, b) => a.order - b.order)
        } else {
            allAvailableAuthSources.value = []
        }
    } catch (error) {
        logger.error('Error fetching auth source list:', error)
        allAvailableAuthSources.value = []
    } finally {
        isLoadingAuthSources.value = false
    }
})

const displayedAuthSources = computed(() => {
    return allAvailableAuthSources.value.filter(s => s.defaultVisible !== false)
})

const hiddenAuthSources = computed(() => {
    return allAvailableAuthSources.value.filter(s => s.defaultVisible === false)
})

const hasMoreAuthSources = computed(() => hiddenAuthSources.value.length > 0)

const toggleMoreAuthSources = () => {
    showMoreAuthSources.value = !showMoreAuthSources.value
}

// 清除用户本地数据的辅助函数
const clearUserLocalData = () => {
    localStg.remove(LocalStorageConstant.USER_INFO)
    localStg.remove(LocalStorageConstant.TOKEN)
    logger.info('User local data and token cookie cleared.')
}

// 统一的登录成功/失败处理逻辑
const handleLoginResponse = async (response: ApiResponse<LoginResultResponse | null>) => {
    // 确保在处理响应后重置提交状态
    isSubmitting.value = false
    clearUserLocalData()
    if (response.code === 200 && response.data) {
        if (response.data.userId && response.data.token) {
            localStg.set(LocalStorageConstant.USER_INFO, response.data)
            localStg.set(LocalStorageConstant.TOKEN, response.data.token)
            logger.info('Login successful. User details and token stored.', {details: response.data})
            displayMessage.success(tMsg('auth.login_successful_welcome_back'))
            const redirectUri = useRoute().query.redirectUri as string | undefined
            if (redirectUri) {
                navigateTo(redirectUri, {replace: true})
                return
            }
            await navigateTo(resolveLocalePath(projectConfig.dashboardEndpointUrl.user), {replace: true})
        } else {
            logger.error('Login successful, but fetched user details are incomplete.', {responseData: response.data})
            displayMessage.error(tMsg('auth.login_fetch_user_info_incomplete'), {
                requestId: response.requestId,
                showSupport: true,
                duration: 5000
            })
        }
    } else {
        logger.error('Login failed or API returned an error.', {response})
        displayMessage.error(
            response.message,
            {
                requestId: response.requestId,
                showSupport: true,
                duration: 5000
            }
        )
    }
}

// 第1步：处理 "继续" 按钮点击
const handleContinueWithEmail = async () => {
    if (emailInputRef.value && emailInputRef.value.$el && !emailInputRef.value.$el.reportValidity()) {
        return
    }
    if (!email.value) {
        return
    }

    isLoading.value = true
    const response = await userAccountApi.queryAccountInfoByContact({email: email.value} as any)
    if (response.code === 200 && response.data) {
        const data = response.data as { email: string, passwordStatus: boolean, username?: string }
        accountStatus.value = {
            emailExists: true,
            hasPassword: data.passwordStatus,
            username: data.username
        }
        logger.info('Account status retrieved:', accountStatus.value)
        currentStep.value = 'passwordOrRegister'
        emits('update-form-mode', {
            mode: 1,
            contact: email.value
        })
    } else if (response.code === STATUS_CODES.ACCOUNT_NOT_EXIST) {
        // 账户不存在
        emits('update-form-mode', {
            mode: 2,
            contact: email.value
        })
        accountStatus.value = {emailExists: false}
        logger.info('Account does not exist for email (API code 2103):', email.value)
        currentStep.value = 'passwordOrRegister'
    } else {
        logger.error('Failed to query account info or API returned an error:', response)
        displayMessage.error(response.message, {
            showSupport: true,
            requestId: response.requestId
        })
        accountStatus.value = null
    }
    isLoading.value = false
}

// 第2步：处理密码登录
const handlePasswordLogin = async () => {
    if (!password.value) {
        return
    }
    isSubmitting.value = true
    logger.info('Attempting password login for:', email.value)
    const response = await loginRegisterApi.loginByAccount({
        username: email.value,
        password: password.value
    })
    await handleLoginResponse(response)
    isSubmitting.value = false
}

// 第2步：处理密码注册 (账户不存在时)
const handlePasswordRegister = async () => {
    if (!password.value) {
        return
    }
    isSubmitting.value = true
    logger.info('Attempting password registration for new account:', email.value)
    const response = await loginRegisterApi.registerByAccount({
        email: email.value,
        password: password.value
    })
    if (response.code === 200) {
        displayMessage.success(tMsg('auth.signupSuccessfulPleaseLogin'))
        accountStatus.value = {emailExists: true, hasPassword: true, username: email.value}
        password.value = ''
    } else {
        logger.error('Registration by password failed:', response)
        displayMessage.error(response.message, {
            showSupport: true,
            requestId: response.requestId
        })
    }
    isSubmitting.value = false
}

// 第2步/第3步：请求邮箱验证码 (发送或重发)
const handleRequestVerificationCode = async () => {
    if (!email.value) {
        return
    }
    isRequestingCode.value = true
    logger.info('Requesting email verification code for:', email.value)
    const response = await captchaApi.sendEmailCaptcha({email: email.value})
    if (response.code === 200) {
        displayMessage.success(tMsg('auth.codeSentSuccess'))
        verificationCode.value = []
        codeCooldown.value = 60
        if (codeCooldownTimer) {
            clearInterval(codeCooldownTimer)
        }
        codeCooldownTimer = setInterval(() => {
            codeCooldown.value -= 1
            if (codeCooldown.value <= 0) {
                clearInterval(codeCooldownTimer!)
                codeCooldownTimer = null
            }
        }, 1000)
        // 确保在发送成功后切换到验证码输入步骤
        currentStep.value = 'verifyCode'
    } else {
        displayMessage.error(response.message, {
            showSupport: true,
            requestId: response.requestId
        })
    }
    isRequestingCode.value = false
}

// 第3步：处理验证码登录/注册
const handleVerifyCodeAndLogin = async (completedPin: string[]) => {
    const codeStr = completedPin.join('')
    if (!codeStr || codeStr.length !== 6) {
        return
    }
    isSubmitting.value = true
    logger.info('Attempting email/code login/registration for:', email.value)
    const response = await loginRegisterApi.loginByEmail({
        email: email.value,
        captcha: codeStr
    })
    // loginByEmail应该处理登录和注册（如果账户不存在）
    await handleLoginResponse(response)
}

// 返回第一步
const backToInitialEmailStep = () => {
    currentStep.value = 'initialEmail'
    password.value = ''
    verificationCode.value = []
    emits('update-form-mode', {
        mode: 1,
        contact: ''
    })
}

// 返回第二步 (从验证码界面)
const backToPasswordOrRegisterStep = () => {
    currentStep.value = 'passwordOrRegister'
    // 不需要清除密码，因为用户可能想编辑它
    verificationCode.value = []
    emits('update-form-mode', {
        mode: 1,
        contact: ''
    })
}

// 处理OAuth登录 (从旧代码迁移)
const handleOAuthLogin = async (authSourceKey: string) => {
    const redirectUrl = `${apiBaseUrl.value}/app/auth/authorize/${authSourceKey}`
    logger.info(`Redirecting to OAuth provider for key "${authSourceKey}": ${redirectUrl}`)
    await navigateTo(redirectUrl, {external: true, open: {target: '_self'}})
}

onBeforeUnmount(() => {
    if (codeCooldownTimer) {
        clearInterval(codeCooldownTimer)
    }
})

</script>

<template>
  <div>
    <!-- 第1步: 输入邮箱 -->
    <div v-if="currentStep === 'initialEmail'">
      <form @submit.prevent="handleContinueWithEmail">
        <div class="flex flex-col gap-6">
          <div class="grid gap-3">
            <Label for="initial-email">{{ tMsg('labels.email') }}</Label>
            <Input id="initial-email"
                   ref="emailInputRef"
                   v-model="email"
                   type="email"
                   :placeholder="tMsg('auth.placeholders.email', {}, '<EMAIL>')"
                   required
                   autocomplete="email"/>
          </div>
          <Button type="submit" class="w-full cursor-pointer" :disabled="isLoading">
            {{ isLoading ? tMsg('auth.labels.loadingChecking') : tMsg('auth.labels.continueBtn') }}
          </Button>

          <!-- 第三方登录分隔线 和 更多选项按钮 (仅第一步显示) -->
          <div v-if="!isLoadingAuthSources && allAvailableAuthSources.length > 0"
               class="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
            <div class="bg-card relative z-10 inline-flex items-center gap-x-2 px-2">
              <span class="text-muted-foreground">
                {{ tMsg('auth.labels.loginOrContinue') }}
              </span>
              <Button v-if="hasMoreAuthSources"
                      variant="link"
                      class="text-sm hover:no-underline cursor-pointer px-1 py-0 h-auto"
                      type="button"
                      @click="toggleMoreAuthSources">
                {{
                  showMoreAuthSources ? tMsg('auth.labels.loginShowLessOptions') : tMsg('auth.labels.loginMoreOptions')
                }}
              </Button>
            </div>
          </div>

          <div v-if="isLoadingAuthSources" class="text-center text-sm text-muted-foreground">
            {{ tMsg('auth.labels.loadingAuthOptions') }}
          </div>

          <div v-if="!isLoadingAuthSources && displayedAuthSources.length > 0" class="grid grid-cols-3 gap-4">
            <Button v-for="source in displayedAuthSources"
                    :key="source.key"
                    variant="outline"
                    type="button"
                    class="w-full"
                    @click="handleOAuthLogin(source.key)">
              <Icon :name="source.icon" class="mr-2 h-4 w-4"/>
              {{ getLocalizedConfigText(source.name, source.name.en || source.key) }}
            </Button>
          </div>

          <div v-if="!isLoadingAuthSources && showMoreAuthSources && hiddenAuthSources.length > 0"
               class="grid grid-cols-3 gap-4 pt-2">
            <Button v-for="source in hiddenAuthSources"
                    :key="source.key"
                    variant="outline"
                    type="button"
                    class="w-full"
                    @click="handleOAuthLogin(source.key)">
              <Icon :name="source.icon" class="mr-2 h-4 w-4"/>
              {{ getLocalizedConfigText(source.name, source.name.en || source.key) }}
            </Button>
          </div>
        </div>
      </form>
    </div>

    <!-- 第2步: 密码登录/注册 或 验证码选项 -->
    <div v-else-if="currentStep === 'passwordOrRegister' && accountStatus">
      <div class="flex flex-col gap-6">
        <!-- 场景2.1: 账户存在，且已设置密码 -->
        <template v-if="accountStatus.emailExists && accountStatus.hasPassword">
          <form @submit.prevent="handlePasswordLogin">
            <div class="grid gap-3">
              <div class="flex items-center">
                <Label for="password-login">{{ tMsg('auth.labels.password') }}</Label>
                <Button variant="link"
                        type="button"
                        class="ml-auto text-sm underline-offset-2 hover:underline h-auto px-1 py-0"
                        @click="emits('update-form-mode', { mode: 3, contact: '' })">
                  {{ tMsg('auth.labels.loginForgotPassword') }}
                </Button>
              </div>
              <Input id="password-login"
                     v-model="password"
                     type="password"
                     required
                     autocomplete="current-password"/>
            </div>
            <Button type="submit" class="w-full mt-6 cursor-pointer" :disabled="isSubmitting">
              {{ isSubmitting ? tMsg('auth.labels.resetSubmitting') : tMsg('auth.labels.loginText') }}
            </Button>
          </form>
        </template>

        <!-- 场景2.3: 账户不存在 (需要注册) -->
        <template v-else-if="!accountStatus.emailExists">
          <form @submit.prevent="handlePasswordRegister">
            <div class="grid gap-3 mt-4">
              <Label for="password-register">{{ tMsg('auth.labels.setPasswordForNewAccount') }}</Label>
              <Input id="password-register"
                     v-model="password"
                     type="password"
                     required
                     autocomplete="new-password"/>
              <!-- 可以加一个确认密码 -->
            </div>
            <Button type="submit" class="w-full mt-6 cursor-pointer" :disabled="isSubmitting">
              {{ isSubmitting ? tMsg('auth.labels.resetSubmitting') : tMsg('auth.labels.signupBtn') }}
            </Button>
          </form>
        </template>

        <!-- 场景2.2: 账户存在，但未设置密码 -->
        <!-- 根据您的最新需求，此场景不显示密码框，直接引导至验证码 -->
        <template v-if="accountStatus.emailExists && !accountStatus.hasPassword">
          <p class="text-sm text-muted-foreground text-center">{{
            tMsg('auth.labels.accountExistNoPasswordUseCode')
          }}</p>
        </template>

        <!-- 分隔线和底部按钮容器 -->
        <div class="flex items-center justify-between border-t border-border pt-4 mt-4">
          <Button variant="link"
                  class="h-auto cursor-pointer p-0 text-sm text-muted-foreground hover:text-primary"
                  @click="backToInitialEmailStep">
            {{ tMsg('auth.labels.backToEmailInput') }}
          </Button>

          <Button variant="link"
                  class="h-auto cursor-pointer p-0 text-sm text-muted-foreground hover:text-primary"
                  type="button"
                  :disabled="isRequestingCode || codeCooldown > 0"
                  @click="handleRequestVerificationCode">
            <template v-if="codeCooldown > 0">
              {{ tMsg('auth.labels.resendCodeCooldown', {seconds: codeCooldown}) }}
            </template>
            <template v-else-if="isRequestingCode">
              {{ tMsg('auth.labels.sendingCode') }}
            </template>
            <template v-else>
              {{
                accountStatus.emailExists ? tMsg('auth.labels.loginWithEmailCodeLink') :
                tMsg('auth.labels.registerWithEmailCodeLink')
              }}
            </template>
          </Button>
        </div>
      </div>
    </div>

    <!-- 第3步: 输入验证码 -->
    <div v-else-if="currentStep === 'verifyCode'">
      <div class="flex flex-col gap-6">
        <div class="text-center">
          <p class="font-medium">{{ tMsg('auth.labels.enterCodeSentTo') }}</p>
        </div>
        <div>
          <Label for="pin-input" class="sr-only">{{ tMsg('auth.labels.emailVerificationCode') }}</Label>
          <PinInput id="pin-input"
                    v-model="verificationCode"
                    placeholder="○"
                    class="my-4"
                    @complete="handleVerifyCodeAndLogin">
            <PinInputGroup class="justify-center gap-x-2 sm:gap-x-3">
              <PinInputSlot v-for="i in 6"
                            :key="i"
                            :index="i - 1"
                            class="h-10 w-10 text-base sm:h-12 sm:w-12 sm:text-lg md:h-14 md:w-14 md:text-xl"/>
            </PinInputGroup>
          </PinInput>
        </div>

        <!-- 底部按钮容器 (类似第二步) -->
        <div class="flex items-center justify-between border-t border-border pt-4 mt-4">
          <Button variant="link"
                  class="h-auto cursor-pointer p-0 text-sm text-muted-foreground hover:text-primary"
                  @click="backToPasswordOrRegisterStep">
            {{ tMsg('auth.labels.backBtn') }}
          </Button>

          <Button variant="link"
                  class="h-auto p-0 text-xs text-muted-foreground hover:text-primary sm:text-sm"
                  :disabled="isRequestingCode || codeCooldown > 0"
                  @click="handleRequestVerificationCode">
            <template v-if="codeCooldown > 0">
              {{ tMsg('auth.labels.resendCodeCooldown', {seconds: codeCooldown}) }}
            </template>
            <template v-else-if="isRequestingCode">
              {{ tMsg('auth.labels.sendingCode') }}
            </template>
            <template v-else>
              {{ tMsg('auth.labels.resendCodeBtn') }}
            </template>
          </Button>
        </div>
      </div>
    </div>

  </div>
</template>

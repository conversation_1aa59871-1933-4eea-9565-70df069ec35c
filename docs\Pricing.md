# Pricing 组件使用文档

`Pricing` 是一个用于展示价格方案页面的 Vue 组件，具有高度的可配置性、国际化支持和通过插槽进行定制的能力。

## Props (属性)

`Pricing` 组件的设计哲学是配置驱动优先。因此，它不接受直接的 props 来控制其内容或行为。所有的定制化（文本、价格方案、周期等）都通过专门的配置文件进行管理。

## 数据配置

`Pricing` 组件的内容完全由 `/config/pricingConfig.ts` 文件驱动。该配置文件的结构由 `/types/site/pricingConfig.ts` 中定义的
TypeScript 接口来约束。

### 核心类型和结构：

1. **`I18nTextMap`**: (通常在 `~/types/i18n` 或类似路径下定义，并被 `pricingConfig.ts` 中的类型引用)
   ```typescript
   export interface I18nTextMap {
       en: string;
       zh: string;
       // [lang: string]: string; // 可支持更多语言
   }
   ```
   这是所有需要国际化文本的基础类型。

2. **`ConfigPricingPlan`**: 定义单个价格计划的结构。
    * `title: I18nTextMap`
    * `description: I18nTextMap`
    * `price: string` (价格通常是固定的)
    * `originalPrice?: string` (可选的原价)
    * `isPopular?: boolean` (是否为热门方案)
    * `popularText?: I18nTextMap` (热门方案的标签文本)
    * `features: I18nTextMap[]` (特性列表，每个特性都是一个可翻译对象)
    * `buttonText: I18nTextMap` (购买按钮的文本)
    * `buttonIcon?: string` (购买按钮的图标名称，来自 icones.js.org)

3. **`ConfigPricingPeriod`**: 定义付费周期（如月付、年付）。
    * `key: string` (唯一标识，如 'monthly')
    * `showText: I18nTextMap` (显示给用户的周期文本，如 "按月付费")
    * `periodSuffixText: I18nTextMap` (用于拼接价格后缀的文本，如 "月"，会显示为 "/月")
    * `plans: ConfigPricingPlan[]` (该周期下的所有计划)

4. **`PricingPageConfig`**: 整个价格页面的顶层配置对象。
    * `sectionHeader: I18nTextMap` (页面区域小标题，如 "价格方案")
    * `mainTitle: I18nTextMap` (页面主标题)
    * `descriptionText: I18nTextMap` (页面描述文本)
    * `periods: ConfigPricingPeriod[]` (包含所有付费周期的数组)

组件内部使用 `@/utils/i18n` 中的 `getLocalizedConfigText` 工具函数，根据当前的 i18n 语言环境自动加载和显示对应的文本。

## 插槽 (Slots)

`Pricing` 组件提供了丰富的插槽来自定义其外观和行为的各个方面：

| 插槽名称                    | 描述                                                                 | 作用域数据 (`props`)                                                                                                                               |
| :-------------------------- | :------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------- |
| `before-main-header`        | 在整个价格区域的标题、描述和周期切换器之前插入内容。                                   | `pricingConfig` (完整的原始配置对象), `localizedPeriodsConfig` (已本地化的周期和计划数据)                                                              |
| `main-description`          | 自定义价格方案区域的主描述文本 `<p>` 标签及其内容。                                | `description` (本地化后的主描述文本), `mainTitle` (本地化后的主标题), `sectionHeader` (本地化后的区域小标题)                                          |
| `period-toggle-container`   | 包裹整个周期切换按钮（月付/年付）的 `div` 容器，允许完全自定义切换逻辑和外观。           | `localizedPeriods` (所有本地化后的周期数据), `currentPeriodKey` (当前选中的周期键), `updatePeriod: (key: string) => void` (用于更新周期的函数)         |
| `period-toggle-button`      | (循环内) 自定义每个周期切换按钮。它会替换默认的 `<button>`。                              | `periodItem` (当前迭代的周期对象), `isActive` (布尔值，表示按钮是否激活), `selectPeriod: () => void` (选中此周期的函数)                               |
| `plan-card-header`          | (循环内) 在每个价格方案卡片的顶部，主要内容渲染之前。                                         | `plan` (当前本地化的价格方案对象), `currentPeriodKey` (当前选中的周期键)                                                                                |
| `plan-title-section`        | (循环内) 自定义每个价格方案卡片中包含标题和"最受欢迎"徽章的整个 `div` 区域。             | `plan` (当前本地化的价格方案对象), `currentPeriodKey` (当前选中的周期键)                                                                                |
| `plan-card-after-price`     | (循环内) 在每个价格方案卡片中，价格信息显示之后，PC端购买按钮之前。                             | `plan` (当前方案对象), `currentPeriodKey` (当前周期键), `currentPeriodSuffix` (当前本地化的周期后缀，如 "/月")                                             |
| `plan-buy-button-pc`        | (循环内) 自定义PC端显示的购买按钮。                                                    | `plan` (当前方案对象), `currentPeriodKey` (当前周期键)                                                                                                 |
| `plan-card-before-features` | (循环内) 在每个价格方案卡片中，PC端购买按钮之后 (如果未使用`plan-buy-button-pc`插槽自定义)，特性列表之前。 | `plan` (当前方案对象), `currentPeriodKey` (当前周期键)                                                                                                 |
| `plan-features-list`        | (循环内) 自定义特性列表的整个 `<ul>` 元素及其内容。                                       | `plan` (当前方案对象), `currentPeriodKey` (当前周期键)                                                                                                 |
| `plan-card-after-features`  | (循环内) 在每个价格方案卡片中，特性列表之后 (如果未使用`plan-features-list`插槽自定义)，移动端购买按钮之前。 | `plan` (当前方案对象), `currentPeriodKey` (当前周期键)                                                                                                 |
| `plan-buy-button-mobile`    | (循环内) 自定义移动端显示的购买按钮。                                                  | `plan` (当前方案对象), `currentPeriodKey` (当前周期键)                                                                                                 |
| `after-all-cards`           | 在所有价格方案卡片渲染完毕之后，组件的末尾插入内容。                                           | `currentPeriodKey`, `localizedPeriods`, `currentPlans` (当前周期的所有计划), `typedPricingConfig` (完整的原始配置对象)                                       |

## 基本用法示例

由于组件是配置驱动的，最基本的使用方式非常简单。确保您的 `/config/pricingConfig.ts` 文件已正确配置。

```vue

<template>
		<div>
				<!-- 其他页面内容 -->
				<Pricing/>
				<!-- 其他页面内容 -->
		</div>
</template>

<script setup lang="ts">
		// Pricing 组件会自动导入 (如果使用了 Nuxt 的 components auto-import)
		// import Pricing from '~/components/market/price/Pricing.vue' // 或者根据需要手动导入
</script>
```

## 使用自定义插槽示例

以下示例展示了如何使用一些插槽来定制 `Pricing` 组件：

```vue

<template>
		<Pricing>
				<template #before-main-header="{ pricingConfig }">
						<div
										class="bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 p-3 text-center rounded-t-md mb-0">
								<p class="font-bold">🎉 特别促销活动进行中! (来自 Slot: before-main-header) 🎉</p>
								<p class="text-xs">原始配置主标题: {{ pricingConfig.mainTitle.en }}</p>
						</div>
				</template>
				
				<template #period-toggle-button="{ periodItem, isActive, selectPeriod }">
						<button
										class="px-6 py-2.5 rounded-full text-sm font-bold transition-transform transform hover:scale-105 focus:outline-none ring-offset-2 ring-offset-background focus:ring-2"
										:class="isActive
          ? 'bg-primary text-primary-foreground shadow-xl ring-primary/70'
          : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'"
										@click="selectPeriod"
						>
								<Icon v-if="isActive" name="heroicons:calendar-days-solid" class="mr-2 h-5 w-5"/>
								<span :class="{ 'font-extrabold': isActive }">{{ periodItem.showText }}</span>
						</button>
				</template>
				
				<template #plan-title-section="{ plan }">
						<div class="border-b pb-3 mb-3 text-center">
								<h3 class="text-2xl font-black tracking-tight"
												:class="plan.isPopular ? 'text-amber-500' : 'text-foreground'">
										{{ plan.title }}
								</h3>
								<span v-if="plan.isPopular && plan.popularText"
														class="mt-1 inline-block bg-amber-400 text-amber-900 text-xs font-bold px-2.5 py-0.5 rounded-full uppercase tracking-wider">
          {{ plan.popularText }} ✨
        </span>
						</div>
				</template>
				
				<template #plan-buy-button-pc="{ plan }">
						<Button :variant="plan.isPopular ? 'default' : 'outline'" size="lg" class="w-full mt-8 group">
								<Icon v-if="plan.buttonIcon" :name="plan.buttonIcon"
														class="mr-2 h-5 w-5 group-hover:rotate-[360deg] transition-transform duration-500"/>
								{{ plan.buttonText }} (PC 定制)
						</Button>
				</template>
				
				<template #plan-features-list="{ plan }">
						<p class="text-sm font-semibold text-muted-foreground my-4 text-center">我们提供的服务:</p>
						<ul class="space-y-2.5 flex-grow">
								<li v-for="(feature, fIndex) in plan.features" :key="fIndex"
												class="flex items-start p-2 rounded-md hover:bg-muted">
										<Icon name="heroicons:check-circle" class="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-px"/>
										<span class="text-sm">{{ feature }}</span>
								</li>
						</ul>
				</template>
				
				<template #after-all-cards="{ currentPlans, currentPeriodKey }">
						<div class="mt-16 text-center p-6 bg-gradient-to-r from-slate-900 to-slate-800 rounded-lg shadow-xl">
								<h4 class="text-2xl font-bold text-white">当前 <span class="text-primary">{{ currentPeriodKey }}</span> 计划
								</h4>
								<p class="text-slate-300 mt-2">共选择了 {{ currentPlans.length }} 个出色的方案来满足您的需求。</p>
								<Button class="mt-6 bg-white text-slate-900 hover:bg-slate-200">
										有疑问？立即咨询！
								</Button>
						</div>
				</template>
		</Pricing>
</template>

<script setup lang="ts">
		// 如果 Pricing 组件和 Button/Icon 不是全局或自动导入的，需要在此处导入
		// import Pricing from '~/components/market/price/Pricing.vue';
		// import { Button } from '@/components/ui/button';
		// import { Icon } from '#components'; 
</script>
```

确保在您的 Nuxt 项目中正确设置了组件的自动导入，或者根据需要手动导入 `Pricing` 组件以及可能在插槽中使用的其他组件（如
`Button`, `Icon`）。 
export interface FileInfo {

    /**
     * 唯一id
     */
    id: number;

    /**
     * 此文件的名称
     */
    fileName: string;

    /**
     * 此文件的大小 字节为单位
     */
    size: number;

    /**
     * 此文件的简介
     */
    summary?: string;

    /**
     * 文件的url
     */
    url: string;

    /**
     * 文件存储的模式
     */
    storageMode: number;

    /**
     * 上传此文件的时间
     */
    createTime: string;

    /**
     * 类型 文件后缀
     */
    type: string;

    /**
     * 文件路径部分的uri
     */
    filePathUri: string;

    /**
     * 客户端上传时传递的key
     */
    filePrimaryKey: string;
}
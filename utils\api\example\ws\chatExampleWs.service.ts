/**
 * 聊天相关的WebSocket服务实现
 *
 * 本文件提供了一个具体的聊天服务类 `ChatWsService`，它继承自 `BaseWsApiService`。
 * 它封装了所有与聊天业务相关的API。
 */

// ============================ 类型定义 ============================
import {BaseWsApiService} from '~/utils/http/BaseWsApiService.class'

/**
 * 查询历史消息 - 请求参数类型
 * 注意：在实际项目中，以下类型应遵循规范，放到 /types/api/params 目录下
 */
export interface QueryHistoryMsgParams {
    contactId: string | number
    limit: number
    anchorId?: string | number
}

/**
 * 历史消息 - 响应数据类型
 * 注意：在实际项目中，以下类型应遵循规范，放到 /types/api/response 目录下
 */
export interface HistoryMsg {
    id: string
    content: string
    sender: 'user' | 'agent'
    timestamp: number
}

export class ChatWsService extends BaseWsApiService {
    constructor() {
        // 在这里为该服务传入固定的、与业务相关的配置，如serverUri
        super({serverUri: '/customer/agent/chat'})
    }

    /**
     * 发送一个查询历史消息的请求。
     * @param params 请求参数
     */
    public queryHistoryMsgList(params: QueryHistoryMsgParams): void {
        this.send(params, 'QUERY_HISTORY_MSG')
    }

    /**
     * 发送一条新消息
     * @param message 消息内容
     */
    public sendNewMessage(message: string): void {
        this.send({content: message}, 'SEND_NEW_MESSAGE')
    }
}

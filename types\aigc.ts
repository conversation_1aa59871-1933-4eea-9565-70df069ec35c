export interface MCPRequest<T> {
    jsonrpc: string | null;
    // sessionId 客户端自己传 没传服务器自动生成
    id: string | null;

    // mcp请求方法
    method: string;

    // mcp请求参数
    params: T;
}

export interface MCPResponse<T> {
    jsonrpc: string | null;

    // 来自于请求
    id: string;

    // mcp响应结果
    result: T | null;

    // 异常结果
    error: {
        code: number
        message: string
        data?: unknown
    } | null;
}

export interface ToolCallParameter<T> {

    // 工具名称
    name: string;

    // 工具参数 需要是对象
    arguments: T;
}

export interface ToolCallResult<T> {
    // 工具调用是否发生异常
    isError: boolean | null;

    // 工具调用返回的结构化数据
    structuredContent: T | null;

    // 工具调用返回的非结构化数据
    content: {
        type: string
        text: string
    }[] | null;
}

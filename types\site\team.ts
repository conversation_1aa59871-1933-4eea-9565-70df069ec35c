import type {I18nTextMap} from '~/types/i18n'

/**
 * 社交链接对象结构
 * name 字段是普通字符串，因为用户指明社交部分不需要i18n
 */
export interface SocialLink {
    /** 例如 'X', 'LinkedIn', 'GitHub' */
    name: string
    url: string
    /** 例如 'lucide:twitter', 'lucide:linkedin' (Icones.js.org 名称) */
    icon: string
}

/**
 * 单个团队成员的配置结构
 * name, role, description 将使用 I18nTextMap 进行国际化
 */
export interface TeamMemberConfig {
    /** 头像图片URL */
    imageUrl: string
    name: I18nTextMap
    role: I18nTextMap
    /** 详细介绍，可选 */
    description?: I18nTextMap
    /** 社交链接，可选 */
    socials?: SocialLink[]
}

/**
 * 团队配置文件的顶层结构
 */
export interface TeamConfig {
    members: TeamMemberConfig[]
    /** 可选的默认标题 */
    title?: I18nTextMap

    /** 可选的默认描述 */
    description?: I18nTextMap
}
import {useClipboard} from '@vueuse/core'
import {message} from './useMessage'
import {tMsg} from '~/utils/i18n'

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param showTip 是否显示提示，默认为true
 * @returns Promise<boolean> 复制是否成功
 */
export function copyToClipboard(text: string, showTip: boolean = true): Promise<boolean> {
    if (!text) {
        if (showTip) {
            message.warning(tMsg('clipboard.emptyContent'))
        }
        return Promise.resolve(false)
    }

    const {copy, isSupported} = useClipboard({
        // 兼容旧浏览器
        legacy: true
    })

    return new Promise((resolve) => {
        if (!isSupported.value) {
            if (showTip) {
                message.error(tMsg('clipboard.browserUnSupport'))
            }
            resolve(false)
            return
        }

        copy(text)
            .then(() => {
                if (showTip) {
                    message.success(tMsg('clipboard.copySuccess'))
                }
                resolve(true)
            })
            .catch(() => {
                if (showTip) {
                    message.error(tMsg('clipboard.copyFailure'))
                }
                resolve(false)
            })
    })
}

/**
 * 使用剪贴板 Hook
 * @returns 剪贴板相关方法
 */
export function useAppClipboard() {
    return {
        copyToClipboard
    }
}

export default useAppClipboard

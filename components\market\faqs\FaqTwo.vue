<script setup lang="ts">
import SupportForm from '@/components/common/SupportForm.vue'
import faqConfig from '@/config/faqs'
import {getLocalizedConfigText} from '@/utils/i18n/utils'
import type {FaqConfig} from '~/types/site/faq'

// 为导入的JSON配置应用类型
const faqConfigTyped = faqConfig as FaqConfig

const isSupportFormOpen = ref(false)
</script>

<template>
  <div class="py-16">
    <!-- Slot before main content -->
    <slot name="before-main-content" :faq-config="faqConfigTyped"/>
    <div class="mx-auto max-w-7xl">
      <!-- Slot for header (title and description) -->
      <slot name="header"
            :title="getLocalizedConfigText(faqConfigTyped.title)"
            :description="getLocalizedConfigText(faqConfigTyped.description)">
        <div class="mx-auto max-w-4xl text-center mb-4 md:mb-8">
          <h2 class="mb-4 md:mb-6 text-2xl font-bold leading-10 tracking-tight text-foreground">
            {{ getLocalizedConfigText(faqConfigTyped.title) }}
          </h2>
          <p class="text-base leading-7 text-muted-foreground">
            {{ getLocalizedConfigText(faqConfigTyped.description) }}
          </p>
        </div>
      </slot>
      <div>
        <!-- Slot before the FAQ list (dl element) -->
        <slot name="before-faq-list"/>
        <dl class="grid grid-cols-1 gap-x-8 gap-y-6 md:gap-y-10 md:grid-cols-2 lg:gap-y-16">
          <template v-for="(faq, index) in faqConfigTyped.items" :key="index">
            <!-- Slot for each FAQ item -->
            <slot name="faq-item"
                  :faq-item="faq"
                  :localized-question="getLocalizedConfigText(faq.content.question)"
                  :localized-answer="getLocalizedConfigText(faq.content.answer)"
                  :index="index">
              <div>
                <dt class="text-base font-semibold leading-7 text-foreground mb-2 md:mb-4">
                  {{ getLocalizedConfigText(faq.content.question) }}
                </dt>
                <dd class="text-base leading-7 text-muted-foreground">
                  {{ getLocalizedConfigText(faq.content.answer) }}
                </dd>
              </div>
            </slot>
          </template>
        </dl>
      </div>
    </div>
    <!-- Slot after main content -->
    <slot name="after-main-content" :faq-config="faqConfigTyped"/>
  </div>
  <SupportForm v-model:is-open="isSupportFormOpen"/>
</template>
import {type ApiResponse, Post} from '~/utils/http'
import logger from '~/utils/logger'
import type {LoginRegister} from '~/types/api'
import {tMsg} from '~/utils/i18n'
import type {LoginResultResponse} from '~/types/api/response/LoginResultResponse'

const API_BASE_URL = '/app'

/**
 * 验证参数是否为空或未定义
 * @param value 需要验证的值
 * @returns 如果值为空/未定义/空字符串则返回 true
 */
function isNullOrEmpty(value: unknown): boolean {
    return value === undefined || value === null || value === ''
}

/**
 * 验证必填参数
 * @param params 参数对象
 * @param requiredFields 必填字段数组
 * @returns 验证结果，包含是否有效和错误消息
 */
function validateRequiredParams<T>(params: T, requiredFields: string[]): { isValid: boolean; errorMessage?: string } {
    if (!params) {
        return {isValid: false, errorMessage: '参数对象不能为空'}
    }

    for (const field of requiredFields) {
        if (isNullOrEmpty((params as Record<string, unknown>)[field])) {
            return {
                isValid: false,
                errorMessage: `${field} 是必填参数`
            }
        }
    }

    return {isValid: true}
}

function createLoginRegisterApi() {

    /**
     * 玩家手机号验证码登录
     * 必填参数: phone, captcha
     */
    function loginByPhone(params: LoginRegister): Promise<ApiResponse<LoginResultResponse>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['phone', 'captcha'])
        if (!validation.isValid) {
            logger.error('loginByPhone 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: tMsg('api.request_params_invalid')
            })
        }

        logger.info('Calling loginByPhone', {
            phone: params.phone,
            captcha: params.captcha
        })
        return Post<LoginResultResponse>(`${API_BASE_URL}/loginByPhone`, params)
    }

    /**
     * 玩家邮箱验证码登录
     * 必填参数: email, captcha
     */
    function loginByEmail(params: LoginRegister): Promise<ApiResponse<LoginResultResponse>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['email', 'captcha'])
        if (!validation.isValid) {
            logger.error('loginByEmail 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: tMsg('api.request_params_invalid')
            })
        }

        logger.info('Calling loginByEmail', {
            email: params.email,
            captcha: params.captcha
        })
        return Post<LoginResultResponse>(`${API_BASE_URL}/loginByEmail`, params)
    }

    /**
     * 玩家通过account和password登录
     * 必填参数: password，可选 username, phone,email
     */
    function loginByAccount(params: LoginRegister): Promise<ApiResponse<LoginResultResponse>> {
        const validation = validateRequiredParams(params, ['password'])
        if (!validation.isValid) {
            logger.error('loginByAccount 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: tMsg('api.request_params_invalid')
            })
        }

        logger.info('Calling loginByAccount', {
            username: params.username
        })
        return Post<LoginResultResponse>(`${API_BASE_URL}/login`, params)
    }

    /**
     * 玩家-退出登录
     * 必填参数: 无请求体参数，token通过header传递
     */
    function logout(): Promise<ApiResponse<null>> {
        logger.info('Calling logout')
        return Post<null>(`${API_BASE_URL}/logout`, {})
    }

    /**
     * 根据玩家手机号，验证码注册账户
     * 必填参数: phone, captcha
     */
    function registerByPhone(params: LoginRegister): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['phone', 'captcha'])
        if (!validation.isValid) {
            logger.error('registerByPhone 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: tMsg('api.request_params_invalid')
            })
        }

        logger.info('Calling registerByPhone', {
            phone: params.phone,
            captcha: params.captcha
        })
        return Post<null>(`${API_BASE_URL}/registerByPhone`, params)
    }

    /**
     * 根据玩家邮箱，验证码注册账户
     * 必填参数: email, captcha
     */
    function registerByEmail(params: LoginRegister): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['email', 'captcha'])
        if (!validation.isValid) {
            logger.error('registerByEmail 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: tMsg('api.request_params_invalid')
            })
        }

        logger.info('Calling registerByEmail', {
            email: params.email,
            captcha: params.captcha
        })
        return Post<null>(`${API_BASE_URL}/registerByEmail`, params)
    }

    /**
     * 通过账号，密码注册
     * 必填参数: username(account), password
     */
    function registerByAccount(params: LoginRegister): Promise<ApiResponse<null>> {
        const validation = validateRequiredParams(params, ['password'])
        if (!validation.isValid) {
            logger.error('registerByAccount 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: tMsg('api.request_params_invalid')
            })
        }

        logger.info('Calling registerByAccount', {
            username: params.username
        })
        return Post<null>(`${API_BASE_URL}/registerByAccount`, params)
    }

    // 返回合并的API对象
    return {
        loginByPhone,
        loginByEmail,
        loginByAccount,
        logout,
        registerByPhone,
        registerByEmail,
        registerByAccount
    }
}

// 导出用户API
export const loginRegisterApi = createLoginRegisterApi()

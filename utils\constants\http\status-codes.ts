/**
 * HTTP状态码常量
 *
 * 常用HTTP状态码和自定义业务状态码
 */
export const STATUS_CODES = {
    // 标准HTTP状态码
    OK: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503,

    // 自定义业务状态码
    // 操作成功
    SERVICE_SUCCESS: 200,

    // 登录过期
    SERVICE_TOKEN_EXPIRED: 720,

    ACCOUNT_NOT_EXIST: 2103,

    // ticket不存在
    TICKET_NOT_FOUND: 830
} as const

// 导出类型
export type StatusCode = keyof typeof STATUS_CODES

/**
 * 检查状态码是否表示成功
 * @param code 状态码
 * @returns 是否成功
 */
export function isSuccess(code: number): boolean {
    return code === STATUS_CODES.SERVICE_SUCCESS
}

/**
 * 检查是否为登录过期错误
 * @param code 状态码
 * @returns 是否登录过期
 */
export function isTokenExpired(code: number): boolean {
    return code === STATUS_CODES.SERVICE_TOKEN_EXPIRED
}

import {message} from '~/composables/useMessage'
import {getToken} from './auth'
import {CONTENT_TYPES, isSuccess, REQUEST_HEADERS, STATUS_CODES} from '~/utils/constants/http'
import type {ApiResponse, RequestOptions, ResponseTransformer} from './types'
import {isBlob, isFile} from '~/utils/typeof'
import qs from 'qs'
import {apiLogger} from '~/utils/logger'
import {RESPONSE_HEADERS} from '~/utils/constants/http/response-headers'
// 导入i18n工具函数
import {getCurrentLanguage, resolveLocalePath, tMsg} from '~/utils/i18n/utils'
import type {UseFetchOptions} from '#app'
import {projectConfig} from '~/config/projectConfig'

/**
 * 创建请求头
 * @param finalHeaders 最终的请求头对象
 * @param ignoreToken
 * @returns 合并后的请求头
 */
export function createHeaders(finalHeaders: Record<string, string> = {}, ignoreToken: boolean = false): HeadersInit {
    const headers: Record<string, string> = {
        // 项目名称（必须）
        [REQUEST_HEADERS.PROJECT_NAME]: projectConfig.projectName,
        ...finalHeaders
    }

    // 添加认证令牌（如果存在且未提供）
    if (!ignoreToken) {
        const token = getToken()
        if (token) {
            headers[REQUEST_HEADERS.TOKEN] = token
        }
    }

    // 获取当前i18n语言，确保格式为en-US（如果未提供）
    if (!headers[REQUEST_HEADERS.ACCEPT_LANGUAGE]) {
        headers[REQUEST_HEADERS.ACCEPT_LANGUAGE] = getCurrentLanguage()
    }

    return headers
}

/**
 * 格式化代码值，确保是数字类型
 * @param code 代码值（可能是字符串或数字）
 * @returns 数字格式的代码
 */
function formatCode(code: unknown): number {
    if (typeof code === 'number') {
        return code
    }
    if (typeof code === 'string' && !isNaN(Number(code))) {
        return Number(code)
    }
    // 默认返回服务器错误
    return STATUS_CODES.INTERNAL_SERVER_ERROR
}

/**
 * 显示错误消息
 * @param errorMessage 错误消息
 * @param requestId 请求ID
 * @param code 错误码
 * @param error 原始错误对象
 */
function showErrorMessage(
    errorMessage: string,
    requestId?: string,
    code?: number,
    error?: unknown
): void {
    // 总是记录到日志中
    apiLogger.error(errorMessage, error, {
        requestId,
        code
    })

    // 如果需要，显示给用户
    if (projectConfig.enableShowRequestErrorMsg) {
        const routeType = routePathType()

        // 仅用户后台和管理后台展示 其他页面由开发者控制
        if (routeType === 1 || routeType === 2) {
            message.error(errorMessage, {
                duration: 3000,
                requestId,
                showSupport: true,
                position: 'top-right'
            })
        }
    }
}

/**
 * 处理响应数据和错误
 * @param response API响应
 * @returns 处理后的响应数据
 */
export async function handleResponse<T>(
    response: ApiResponse<T>, requestParam?: UseFetchOptions<any>
): Promise<ApiResponse<T>> {
    const {code: rawCode, message: msg, requestId} = response
    // 格式化code为数字
    const code = formatCode(rawCode)

    // 处理成功响应
    if (isSuccess(code)) {
        return response
    }

    showErrorMessage(msg, requestId, code)

    // 处理登录过期 - 只在code为720时刷新token
    if (code === STATUS_CODES.SERVICE_TOKEN_EXPIRED) {
        message.error(msg)
        navigateTo(resolveLocalePath(projectConfig.loginEndpointUrl.user))
    } else if (code === STATUS_CODES.TICKET_NOT_FOUND) {
        // 需要ticket 但是尚未传递
        logger.info('Try to apply new ticket...')

    }

    // 返回带有请求ID的响应
    return response
}

/**
 * 获取响应头
 * @param context
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getResponseHeaders(context: any): Map<string, string> {
    const headers = new Map<string, string>()

    if (context && context.response && context.response.headers) {
        if (typeof context.response.headers.entries === 'function') {
            // 如果headers有entries方法（标准Headers对象）
            for (const [key, value] of context.response.headers.entries()) {
                headers.set(key, value)
            }
        } else if (typeof context.response.headers.forEach === 'function') {
            // 某些Headers实现使用forEach
            context.response.headers.forEach((value: string, key: string) => {
                headers.set(key, value)
            })
        } else if (context.response.headers instanceof Map) {
            // 如果已经是Map
            return context.response.headers as Map<string, string>
        } else {
            // 如果是普通对象
            for (const key in context.response.headers) {
                if (Object.prototype.hasOwnProperty.call(context.response.headers, key)) {
                    headers.set(key, context.response.headers[key])
                }
            }
        }
    }

    return headers
}

/**
 * 从响应上下文中获取请求ID
 * @param context 响应上下文或可能包含响应信息的对象
 * @returns 请求ID
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getRequestIdFromResponse(context: any): string | undefined {
    // 由于不同API返回的响应结构不同，这里使用any类型
    // 处理常规响应上下文
    if (context && context.response && context.response.headers) {
        return context.response.headers.get(RESPONSE_HEADERS.REQUEST_ID) || undefined
    }

    return undefined
}

/**
 * 创建错误响应
 * @param error 捕获的错误
 * @returns 格式化的错误响应
 */
export function createErrorResponse(
    error: Record<string, unknown> | Error | unknown
): ApiResponse<null> {
    // 如果已经是HttpError格式，直接返回
    if (error && typeof error === 'object' && 'code' in error
        && (typeof error.code === 'number' || typeof error.code === 'string')
        && 'message' in error) {
        return {
            code: formatCode(error.code),
            message: error.message as string
        }
    }

    // 检查error.data是否存在并且符合我们需要的格式
    if (error && typeof error === 'object' && 'data' in error
        && error.data && typeof error.data === 'object'
        && 'code' in error.data && 'message' in error.data) {
        return {
            code: formatCode(error.data.code),
            message: error.data.message as string
        }
    }

    // 处理网络错误
    if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
            code: STATUS_CODES.SERVICE_UNAVAILABLE,
            message: tMsg('http.service_unavailable')
        }
    }

    if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string' && error.message.includes('fetch')) {
        return {
            code: STATUS_CODES.SERVICE_UNAVAILABLE,
            message: tMsg('http.service_unavailable')
        }
    }

    // 其他未知错误
    return {
        code: STATUS_CODES.INTERNAL_SERVER_ERROR,
        message: error instanceof Error ? error.message : tMsg('http.unknown_error')
    }
}

// 简化的HTTP方法类型，只包含四种基本方法
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE'

/**
 * 根据内容类型转换请求数据
 * @param contentType 内容类型
 * @param data 请求数据
 * @returns 处理后的请求数据
 */
export async function transformRequestData(
    contentType: string,
    data?: Record<string, unknown>
): Promise<string | FormData | Record<string, unknown>> {
    if (!data) {
        return {}
    }

    if (contentType === CONTENT_TYPES.JSON) {
        return JSON.stringify(data)
    }
    if (contentType === CONTENT_TYPES.FORM) {
        return qs.stringify(data)
    }

    return data
}

/**
 * 处理单个文件或文件数组
 * @param formData FormData对象
 * @param key 字段名
 * @param file 文件或文件数组
 */
async function transformFile(formData: FormData, key: string, file: File | File[] | Blob | Blob[]): Promise<void> {
    if (Array.isArray(file)) {
        // 多文件
        await Promise.all(
            file.map(item => {
                formData.append(key, item)
                return true
            })
        )
    } else {
        // 单文件
        formData.append(key, file)
    }
}

/**
 * 处理FormData文件上传
 * @param data 包含文件的数据对象
 * @returns 处理后的FormData对象
 */
export async function handleFormData(data?: Record<string, unknown>): Promise<FormData> {
    const formData = new FormData()
    if (!data) {
        return formData
    }
    const entries = Object.entries(data)

    for (const [key, value] of entries) {
        if (value === undefined || value === null) {
            continue
        }

        // 检查是否为文件类型
        let hasFile = false

        // 单个文件检查
        if (isFile(value) || isBlob(value)) {
            hasFile = true
            await transformFile(formData, key, value as File | Blob)
        }
        // 文件数组检查
        else if (Array.isArray(value) && value.length > 0) {
            if (isFile(value[0]) || isBlob(value[0])) {
                hasFile = true
                await transformFile(formData, key, value as File[] | Blob[])
            }
        }

        // 处理非文件类型
        if (!hasFile) {
            // 处理对象类型
            if (typeof value === 'object' && !Array.isArray(value)) {
                formData.append(key, JSON.stringify(value))
            }
            // 处理数组类型
            else if (Array.isArray(value)) {
                value.forEach(item => {
                    formData.append(key, typeof item === 'object' ? JSON.stringify(item) : String(item))
                })
            }
            // 处理基本类型
            else {
                formData.append(key, String(value))
            }
        }
    }

    return formData
}

/**
 * 合并数据对象
 * @param mainData 主数据
 * @param additionalData 附加数据
 * @returns 合并后的数据（主数据优先）
 */
export function mergeData(
    mainData: Record<string, unknown> = {},
    additionalData: Record<string, unknown> = {}
): Record<string, unknown> {
    // 如果其中一个为空，直接返回另一个
    if (!mainData || Object.keys(mainData).length === 0) {
        return {...additionalData}
    }
    if (!additionalData || Object.keys(additionalData).length === 0) {
        return {...mainData}
    }

    // 合并两个对象，主数据优先
    return {...additionalData, ...mainData}
}

/**
 * 根据HTTP方法自动确定合适的Content-Type
 * @param method HTTP方法
 * @param customContentType 自定义Content-Type
 * @returns 适合该方法的Content-Type
 */
function getContentTypeByMethod(method: HttpMethod, customContentType?: string): string {
    if (customContentType) {
        return customContentType
    }

    // GET和DELETE默认使用FORM，POST和PUT默认使用JSON
    if (method === 'GET' || method === 'DELETE') {
        return CONTENT_TYPES.FORM
    }
    return CONTENT_TYPES.JSON
}

/**
 * 处理请求参数
 * @param method HTTP方法
 * @param data 主数据
 * @param options 请求选项
 * @returns 处理后的请求参数
 */
interface ProcessedRequestParams {
    body?: string | FormData | Record<string, unknown> | null
    query?: Record<string, unknown>
    finalHeaders: Record<string, string>
}

async function processRequestParams(
    method: HttpMethod,
    data?: Record<string, unknown>,
    options: RequestOptions = {}
): Promise<ProcessedRequestParams> {
    const {headers = {}, additionalFormData} = options

    // 复制请求头以避免修改原始对象
    const customHeaders = {...headers}

    // 确定Content-Type
    const contentType = getContentTypeByMethod(
        method,
        customHeaders[REQUEST_HEADERS.CONTENT_TYPE]
    )

    // 如果没有设置Content-Type，则添加默认值
    if (!customHeaders[REQUEST_HEADERS.CONTENT_TYPE]) {
        customHeaders[REQUEST_HEADERS.CONTENT_TYPE] = contentType
    }

    // 处理表单合并 - 只在contentType为FORM时才合并数据
    const hasAdditionalFormData = !!(additionalFormData && Object.keys(additionalFormData).length > 0)

    let body: string | FormData | Record<string, unknown> | null = null
    let query = undefined

    // 处理GET和DELETE请求的查询参数
    if (method === 'GET' || method === 'DELETE') {
        // 根据contentType确定合并逻辑
        let mergedData = data
        if (contentType === CONTENT_TYPES.FORM && hasAdditionalFormData) {
            // 仅在表单格式时合并数据
            mergedData = data ? mergeData(data, additionalFormData) : additionalFormData
        }
        query = mergedData
    }
    // 处理POST和PUT请求
    else if (method === 'POST' || method === 'PUT') {
        // 处理请求体数据
        if (contentType === CONTENT_TYPES.JSON) {
            // JSON格式
            body = await transformRequestData(contentType, data) as string
        } else if (contentType === CONTENT_TYPES.MULTIPART) {
            // Multipart格式处理
            body = await handleFormData(data)
        }

        // 如果有额外的FormData,设置为查询参数
        if (hasAdditionalFormData) {
            query = additionalFormData
        }
    }

    return {
        body,
        query,
        finalHeaders: customHeaders
    }
}

/**
 * 尝试解析JSON字符串
 * @param data 可能是JSON字符串的数据
 * @returns 解析后的对象或原始数据
 */
function tryParseJSON(data: unknown): unknown {
    if (typeof data === 'string') {
        try {
            return JSON.parse(data)
        } catch {
            // 解析失败，返回原始数据
            return data
        }
    }
    return data
}

/**
 * 验证响应格式是否符合标准API响应格式
 * @param data 响应数据
 * @param responseTransformer 可选的响应转换器
 * @returns 标准化的API响应
 */
export function validateResponseFormat<T>(
    data: unknown,
    responseTransformer?: ResponseTransformer
): ApiResponse<T> {
    // 如果提供了响应转换器，优先使用它处理响应
    if (responseTransformer) {
        try {
            return responseTransformer(data) as ApiResponse<T>
        } catch (error) {
            apiLogger.error('Response transformer execution failed', error)
            // 转换失败，回退到标准处理
        }
    }

    // 尝试将字符串解析为JSON
    const parsedData = tryParseJSON(data)
    // 检查是否为标准响应格式 {code, message, data}
    if (parsedData
        && typeof parsedData === 'object'
        && 'code' in parsedData
        && (typeof parsedData.code === 'number' || typeof parsedData.code === 'string')
        && 'message' in parsedData
    ) {
        // 确保code是数字格式，创建新对象防止修改原对象
        return {
            code: formatCode(parsedData.code),
            message: parsedData.message as string,
            ...(('data' in parsedData) ? {data: parsedData.data as T} : {})
        }
    }

    // 非标准格式，返回错误响应
    return {
        code: STATUS_CODES.INTERNAL_SERVER_ERROR,
        message: tMsg('http.response_format_unexpected')
    }
}

/**
 * 获取完整的请求URL
 * @param uri 请求URI
 * @returns 完整的请求URL
 */
function getRequestUrl(uri: string): string {
    // 如果是完整URL则直接返回
    if (uri.startsWith('http') || uri.startsWith('https')) {
        return uri
    }

    const config = useRuntimeConfig()
    // 获取API基础URL
    const apiBase = config.public.apiBase as string
    if (!apiBase) {
        throw new Error(tMsg('http.api_base_not_configured'))
    }

    // 标准化URI和基础URL
    const normalizedUri = uri.startsWith('/') ? uri.slice(1) : uri
    const normalizedBase = apiBase.endsWith('/') ? apiBase.slice(0, -1) : apiBase

    // 拼接完整URL
    return `${normalizedBase}/${normalizedUri}`
}

/**
 * 发起HTTP请求
 * @param url 请求URL
 * @param method HTTP方法
 * @param data 请求数据
 * @param options 请求选项
 * @returns 响应数据
 */
export async function request<T>(
    url: string,
    method: HttpMethod,
    data?: Record<string, unknown>,
    options: RequestOptions = {}
): Promise<ApiResponse<T>> {
    const {timeout = projectConfig.defaultTimeout} = options
    try {
        url = getRequestUrl(url)
        apiLogger.debug(`Starting ${method} request: ${url}`, {
            data,
            options
        })

        // 处理请求参数
        const {body, query, finalHeaders} = await processRequestParams(method, data, options)

        // 创建最终的请求头
        const requestHeaders = createHeaders(finalHeaders, options.ignoreToken)

        // 使用useFetch发送请求
        const fetchMethod = method.toLowerCase() as 'get' | 'post' | 'put' | 'delete'
        let requestId: string | undefined
        let responseHeaders: Map<string, string> = new Map<string, string>()
        let responseCallbackCompleted = false

        const requestParam: UseFetchOptions<any> = {
            method: fetchMethod,
            headers: requestHeaders,
            body,
            query,
            timeout,
            // 禁用缓存，确保每次请求都向服务器发送
            key: Date.now() + Math.random().toString(),
            // 可选：也可以设置以下选项来禁用缓存
            cache: 'no-cache',
            onResponse(context) {
                responseHeaders = getResponseHeaders(context)
                // 从响应中获取requestId
                requestId = responseHeaders.get(RESPONSE_HEADERS.REQUEST_ID) || undefined
                responseCallbackCompleted = true
            }
        }
        const fetchResult = await useFetch<ApiResponse<T>>(url, requestParam)
        // 确保onResponse回调已经执行完成
        // 由于异步特性，可能需要等待onResponse回调执行完成
        if (!responseCallbackCompleted && import.meta.client) {
            await new Promise(resolve => setTimeout(resolve, 0))
        }

        const {data: responseData, error} = fetchResult

        // 处理网络错误或请求失败
        if (error.value) {
            apiLogger.error(`Request failed: ${url}`, error.value, {
                method,
                url,
                requestId
            })

            // 创建统一的错误响应
            const errorResponse = createErrorResponse(error.value)
            errorResponse.requestId = requestId
            errorResponse.headers = responseHeaders
            // 统一使用handleResponse处理错误响应
            return await handleResponse(errorResponse as ApiResponse<T>, requestParam)
        }

        // 确保返回的数据格式符合预期
        if (!responseData.value) {
            const noResponseError = new Error(tMsg('http.no_response_data'))
            apiLogger.error(`No response data received: ${url}`, noResponseError, {
                method,
                url,
                requestId
            })

            const errorResponse = createErrorResponse(noResponseError)
            errorResponse.requestId = requestId
            return await handleResponse(errorResponse as ApiResponse<T>, requestParam)
        }

        // 验证响应格式并标准化
        const validatedResponse = validateResponseFormat<T>(responseData.value, options.responseTransformer)
        validatedResponse.requestId = requestId

        // 记录响应
        if (!isSuccess(formatCode(validatedResponse.code))) {
            apiLogger.warn(`Response contains error code: ${validatedResponse.code}`, {
                url,
                method,
                response: validatedResponse,
                requestId
            })
        } else {
            apiLogger.debug(`Request successful: ${url}`, {
                method,
                url,
                requestId
            })
        }

        // 处理业务逻辑错误
        return await handleResponse(validatedResponse, requestParam)
    } catch (error) {
        // 记录未预期的错误
        apiLogger.error(`Exception during request: ${url}`, error, {
            method,
            url
        })

        // 创建统一的错误响应
        const errorResponse = createErrorResponse(error)
        // 统一使用handleResponse处理错误响应
        return await handleResponse(errorResponse as ApiResponse<T>, undefined)
    }
}

/**
 * 发起懒加载HTTP请求
 * @param url 请求URL
 * @param method HTTP方法
 * @param data 请求数据
 * @param options 请求选项
 * @returns useLazyFetch返回的AsyncData对象
 */
export function lazyRequest<T>(
    url: string,
    method: HttpMethod,
    data?: Record<string, unknown>,
    options: RequestOptions = {}
) {
    const {timeout = projectConfig.defaultTimeout} = options

    try {
        url = getRequestUrl(url)
        apiLogger.debug(`Starting lazy ${method} request: ${url}`, {data, options})

        // 处理参数并立即获取结果
        const processResult = processRequestParams(method, data, options)

        // 使用处理好的参数配置useLazyFetch
        return processResult.then(({body, query, finalHeaders}) => {
            // 创建最终的请求头
            const requestHeaders = createHeaders(finalHeaders)

            // 调用useLazyFetch
            return useLazyFetch<ApiResponse<T>>(url, {
                method: method.toLowerCase() as 'get' | 'post' | 'put' | 'delete',
                headers: requestHeaders,
                body,
                query,
                timeout,
                // 禁用缓存，确保每次请求都向服务器发送
                key: Date.now() + Math.random().toString(),
                cache: 'no-cache',
                transform: (responseData) => {
                    // 验证响应格式
                    return validateResponseFormat<T>(responseData, options.responseTransformer)
                },
                onResponseError: ({response}) => {
                    const requestId = getRequestIdFromResponse(response)
                    const statusCode = response.status
                    const errorMessage = response.statusText || tMsg('http.request_failed')

                    // 仅记录日志
                    apiLogger.error(`Lazy request failed (silent): ${url}`, {
                        statusCode: response.status,
                        errorMessage: response.statusText || 'Request failed',
                        requestId,
                        method,
                        url
                    })

                    // 显示错误消息
                    showErrorMessage(errorMessage, requestId, statusCode)
                }
            })
        })
    } catch (error) {
        // 处理预处理阶段的错误
        const errorMessage = error instanceof Error ? error.message : tMsg('http.request_preparation_error')

        // 记录错误
        apiLogger.error(`Lazy request preprocessing error: ${url}`, {
            error,
            method,
            url,
            errorMessage
        })

        showErrorMessage(errorMessage, undefined, undefined, error)

        // 返回一个已拒绝的Promise
        throw error
    }
}

/**
 * 批量请求
 * @param requests 请求列表
 * @returns 所有请求的响应
 */
export async function batch<T>(
    requests: Array<{
        url: string
        method: HttpMethod
        data?: Record<string, unknown>
        options?: RequestOptions
    }>
): Promise<T> {
    const promises = requests.map(({url, method, data, options}) =>
        request(url, method, data, options))
    return await Promise.all(promises) as unknown as T
}

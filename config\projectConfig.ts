export const projectConfig = {
    // 项目名称，用于API请求头
    projectName: 'MyProject',

    // 默认请求超时时间(毫秒)
    defaultTimeout: 30000,

    // 默认语言
    defaultLocale: {
        code: 'en',
        language: 'en_US'
    },

    // 如果关闭 那么在请求失败时 不会展示接口的响应message
    enableShowRequestErrorMsg: false,

    // 管理后台登录页面地址 不能以/admin开头
    dashboardEndpointUrl: {
        user: '/dashboard',
        admin: '/admin/dashboard'
    },
    loginEndpointUrl: {
        user: '/login',
        admin: '/login-admin'
    },
    captchaVerify: {
        // 是否使用dialog
        useDialog: false
    }
}

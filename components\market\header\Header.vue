<script setup lang="ts">
import navLinkData from '~/config/navLinks'
import siteConfigData from '~/config/site'
import {getLocalizedConfigText} from '~/utils/i18n'
import {LogOut} from 'lucide-vue-next'
import LoginDialog from '~/components/auth/LoginDialog.vue'
import {localStg} from '~/utils/localStorageService'
import {LocalStorageConstant} from '~/utils/constants/localStorage'
import {executeLogout} from '~/utils/auth'
import LanguageAndTheme from '~/components/common/LanguageAndTheme.vue'
import type {LoginResultResponse} from '~/types/api/response/LoginResultResponse'

// 定义 props
const props = defineProps({
    displayMode: {
        type: String as () => 'sticky' | 'scroll' | 'static',
        default: 'sticky'
    }
})

// 定义导航链接数组
const navLinks = ref(navLinkData)
const siteConfig = ref(siteConfigData)

// 移动端菜单状态
const isMobileMenuOpen = ref(false)

// 登录弹窗状态
const isLoginDialogOpen = ref(false)

// scroll 模式下的状态
const lastScrollY = ref(0)
// 初始可见
const isHeaderVisible = ref(true)
// 滚动阈值,可以根据header高度调整
const scrollThreshold = ref(60)

// i18n 国际化
const {locales, locale, setLocale, t} = useI18n()
const currentLanguageName = computed(() => {
    const languageList = locales.value as Array<{
        code: string,
        name: string,
        language: string
    }>
    for (let i = 0; i < languageList.length; i++) {
        if (languageList[i].code === locale.value) {
            return languageList[i].name
        }
    }

    return undefined
})

// 切换语言
const switchLocale = (code: string) => {
    setLocale(code as never)
}

// 暗黑模式
const colorMode = useColorMode()

// 切换主题
const toggleTheme = (theme: string) => {
    colorMode.preference = theme
}

// 用户信息
const loggedInUser = ref<LoginResultResponse | null>(null)

// 计算用户是否已登录
const isAuthenticated = computed(() => !!loggedInUser.value?.userId)

// 打开登录弹窗
const openLoginDialog = () => {
    isLoginDialogOpen.value = true
}

// 处理登录成功
const handleLoginSuccess = () => {
    // 从 localStorage 读取最新的用户信息
    const userInfo = localStg.get<LoginResultResponse>(LocalStorageConstant.USER_INFO)
    if (userInfo && userInfo.userId) {
        loggedInUser.value = userInfo
    } else {
        loggedInUser.value = null
    }
    isLoginDialogOpen.value = false
}

// 关闭移动端侧边栏
const closeMobileMenu = () => {
    isMobileMenuOpen.value = false
}

// 从配置中获取网站标题的本地化文本
const localizedSiteTitle = computed(() => getLocalizedConfigText(siteConfig.value.title, 'Site Title'))

// 滚动事件处理函数
const handleScroll = () => {
    if (typeof window === 'undefined') {
        return
    }

    if (props.displayMode !== 'scroll') {
        isHeaderVisible.value = true
        return
    }

    const currentScrollY = window.scrollY

    // 滚动到顶部或接近顶部时，始终显示
    if (currentScrollY <= scrollThreshold.value) {
        isHeaderVisible.value = true
        // 向下滚动
    } else if (currentScrollY > lastScrollY.value) {
        isHeaderVisible.value = false
    } else {
        // 向上滚动
        isHeaderVisible.value = true
    }
    lastScrollY.value = Math.max(0, currentScrollY)
}

// 组件挂载和卸载时处理滚动事件监听
onMounted(() => {
    if (typeof window !== 'undefined') {
        window.addEventListener('scroll', handleScroll)
        // 初始化 lastScrollY
        lastScrollY.value = window.scrollY
    }
    // 组件挂载时检查 localStorage
    const userInfo = localStg.get<LoginResultResponse>(LocalStorageConstant.USER_INFO)
    if (userInfo && userInfo.userId) {
        loggedInUser.value = userInfo
    }
})

onUnmounted(() => {
    if (typeof window !== 'undefined') {
        window.removeEventListener('scroll', handleScroll)
    }
})

// 登出
const handleSignOut = async () => {
    await executeLogout(() => {
        loggedInUser.value = null
    })
}

// 计算 header 的动态类
const headerClasses = computed(() => {
    const classes: string[] = []
    if (props.displayMode === 'scroll') {
        // 对于 fixed 定位的 scroll 模式，header 本身需要 max-w-7xl 和 mx-auto 来居中
        classes.push('max-w-7xl', 'mx-auto')
    } else {
        // sticky 和 static 模式下，header 是 w-full，内部 container 负责居中
        classes.push('w-full')
    }

    if (props.displayMode === 'sticky') {
        classes.push('sticky', 'top-0', 'z-40', 'bg-background/80', 'backdrop-blur-sm')
    } else if (props.displayMode === 'scroll') {
        classes.push('fixed', 'top-0', 'left-0', 'right-0', 'z-40', 'bg-background/80', 'backdrop-blur-sm', 'transition-transform', 'duration-300', 'ease-in-out')
        if (isHeaderVisible.value) {
            classes.push('translate-y-0')
        } else {
            classes.push('-translate-y-full')
        }
    } else {
        // static 模式下，可能不需要 z-index 和背景模糊效果
        classes.push('relative', 'bg-background')
    }
    return classes.join(' ')
})
</script>

<template>
  <header id="header" :class="headerClasses">
    <div class="container flex h-12 items-center justify-between px-4 md:px-6 mx-auto">
      <!-- 左侧导航部分：Logo + 导航链接 -->
      <slot name="navbar"/>

      <!-- 右侧功能区 -->
      <div class="flex items-center space-x-2 h-full">
        <!-- 语言切换下拉菜单 - 只在非移动端显示 -->
        <div class="hidden md:block">
          <slot name="pc-switch-locale">
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <button
                  class="rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground flex items-center h-10">
                  <Icon name="uil:globe" class="h-5 w-5"/>
                  <span class="ml-1 hidden sm:inline-block text-sm">{{ currentLanguageName }}</span>
                  <Icon name="uil:angle-down" class="h-4 w-4 ml-1"/>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center">
                <DropdownMenuRadioGroup :model-value="locale" @update:model-value="switchLocale">
                  <DropdownMenuRadioItem v-for="localeItem in locales"
                                         :key="localeItem.code"
                                         :value="localeItem.code"
                                         class="cursor-pointer">
                    {{ localeItem.name }}
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </slot>
        </div>

        <!-- 主题切换按钮 - 只在非移动端显示 -->
        <div class="hidden md:block">
          <slot name="pc-switch-theme">
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <button
                  class="rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground h-10 flex items-center justify-center">
                  <Icon name="uil:moon"
                        class="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"/>
                  <Icon name="uil:sun"
                        class="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"/>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem @click="toggleTheme('light')">
                  <Icon name="material-symbols:light-mode"/>
                  <span>{{ t("theme.theme_light") }}</span>
                </DropdownMenuItem>
                <DropdownMenuItem @click="toggleTheme('dark')">
                  <Icon name="material-symbols:dark-mode-rounded"/>
                  <span>{{ t("theme.theme_dark") }}</span>
                </DropdownMenuItem>
                <DropdownMenuItem @click="toggleTheme('system')">
                  <Icon name="material-symbols:computer"/>
                  <span>{{ t("theme.theme_system") }}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </slot>
        </div>

        <!-- 用户登录/头像按钮 - PC端 -->
        <div class="hidden md:block">
          <slot name="pc-extra-area"
                :login-status="isAuthenticated"
                :current-user="loggedInUser"
                :handle-sign-out-function="handleSignOut"
                :open-login-dialog-function="openLoginDialog">
            <!-- 未登录状态 -->
            <button v-if="!isAuthenticated"
                    class="rounded-md px-4 py-2 bg-primary text-white h-10 flex items-center justify-center"
                    @click="openLoginDialog">
              {{ t("nav.sign_in") }}
            </button>

            <!-- 已登录状态 -->
            <DropdownMenu v-else-if="loggedInUser">
              <DropdownMenuTrigger as-child>
                <button class="flex h-10 items-center">
                  <Avatar class="h-8 w-8">
                    <AvatarImage v-if="loggedInUser.avatar"
                                 :src="loggedInUser.avatar"
                                 :alt="loggedInUser.nickname"/>
                    <AvatarFallback class="bg-muted-foreground/10">
                      {{ (loggedInUser.username || '').slice(0, 2).toUpperCase() }}
                    </AvatarFallback>
                  </Avatar>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" class="w-56">
                <div class="p-2">
                  <div class="font-semibold">{{ loggedInUser.username || 'null' }}</div>
                  <div class="text-xs text-muted-foreground">{{ loggedInUser.email }}
                  </div>
                </div>
                <DropdownMenuSeparator/>
                <DropdownMenuItem as-child>
                  <NuxtLink :to="$localePath({ path: '/dashboard' })" class="flex w-full cursor-default">
                    {{ t("dashboard") }}
                  </NuxtLink>
                </DropdownMenuItem>
                <DropdownMenuSeparator/>
                <DropdownMenuItem class="text-red-500 cursor-pointer" @click="handleSignOut">
                  <LogOut class="mr-2 h-4 w-4"/>
                  {{ t("auth.sign_out") }}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </slot>
        </div>

        <!-- 移动端菜单按钮 -->
        <div class="md:hidden h-10 flex items-center">
          <Sheet v-model:open="isMobileMenuOpen">
            <SheetTrigger>
              <slot name="mobile-trigger-sheet">
                <button
                  class="rounded-md p-2 bg-amber-600 text-white hover:bg-amber-700 h-full flex items-center justify-center">
                  <Icon name="uil:bars" class="h-5 w-5"/>
                </button>
              </slot>
            </SheetTrigger>
            <SheetContent side="right" class="p-0 w-[75%] max-w-[350px]">
              <slot name="mobile-sheet-content">
                <div class="flex flex-col h-full">
                  <!-- 头部品牌 -->
                  <slot name="mobile-sheet-header" :site-config="siteConfig">
                    <SheetHeader class="p-4 border-b">
                      <div class="flex items-center space-x-2">
                        <img v-if="siteConfig.logo"
                             :src="siteConfig.logo"
                             alt="Logo"
                             class="w-8 h-8 rounded-sm">
                        <NuxtLink :to="$localePath({ path: '/' })" class="flex items-center" @click="closeMobileMenu">
                          <SheetTitle class="text-lg ml-2">{{ localizedSiteTitle }}</SheetTitle>
                        </NuxtLink>
                      </div>
                    </SheetHeader>
                  </slot>

                  <!-- 导航项目 -->
                  <div class="flex-1 overflow-auto py-4 flex flex-col justify-between">
                    <slot name="mobile-nav-links" :nav-links="navLinks" :close-mobile-menu-function="closeMobileMenu">
                      <div class="flex flex-col space-y-4 px-4">
                        <!-- 主导航链接 - 移动端侧边栏中显示 -->
                        <NuxtLink v-for="(link, index) in navLinks"
                                  :key="index"
                                  :to="$localePath({ path: link.href })"
                                  class="flex items-center h-10 px-2 rounded hover:bg-muted"
                                  @click="closeMobileMenu">
                          <Icon v-if="link.icon" :name="link.icon" class="h-4 w-4 mr-2"/>
                          <span>{{ getLocalizedConfigText(link.title) }}</span>
                        </NuxtLink>
                      </div>
                    </slot>

                    <!-- 底部功能区 -->
                    <div class="mt-4 px-4 pt-4 border-t">
                      <slot name="mobile-bottom-content"
                            :open-login-dialog-function="openLoginDialog"
                            :login-status="isAuthenticated">
                        <slot name="mobile-extra-area"
                              :login-status="isAuthenticated"
                              :current-user="loggedInUser"
                              :handle-sign-out-function="handleSignOut"
                              :open-login-dialog-function="openLoginDialog">
                          <!-- 登录按钮/用户信息 -->
                          <button v-if="!isAuthenticated"
                                  class="w-full bg-amber-600 text-white py-2 rounded-md mb-6 hover:bg-amber-700"
                                  @click="openLoginDialog">
                            {{ t("nav.sign_in") }}
                          </button>
                          <!-- 已登录状态 - 展示用户信息 -->
                          <div v-else-if="loggedInUser" class="mb-6">
                            <DropdownMenu>
                              <DropdownMenuTrigger as-child>
                                <div class="flex items-center space-x-3 mb-3">
                                  <Avatar class="h-10 w-10">
                                    <AvatarImage v-if="loggedInUser.avatar"
                                                 :src="loggedInUser.avatar"
                                                 :alt="loggedInUser.nickname"/>
                                    <AvatarFallback class="bg-muted-foreground/10">
                                      {{
                                        (loggedInUser.nickname || '').slice(0, 2).toUpperCase()
                                      }}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <div class="font-medium">{{ loggedInUser.nickname }}
                                    </div>
                                    <div class="text-xs text-muted-foreground">{{ loggedInUser.email }}
                                    </div>
                                  </div>
                                </div>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" class="w-56">
                                <div class="p-2">
                                  <div class="font-semibold">{{ loggedInUser.nickname || t('user.nickname_fallback') }}
                                  </div>
                                  <div class="text-xs text-muted-foreground">{{ loggedInUser.email }}
                                  </div>
                                </div>
                                <DropdownMenuSeparator/>
                                <DropdownMenuItem as-child>
                                  <NuxtLink :to="$localePath({ path: '/dashboard' })"
                                            class="flex w-full cursor-default"
                                            @click="closeMobileMenu">
                                    {{ t("dashboard") }}
                                  </NuxtLink>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator/>
                                <DropdownMenuItem class="text-red-500 cursor-pointer" @click="handleSignOut">
                                  <LogOut class="mr-2 h-4 w-4"/>
                                  {{ t("auth.sign_out") }}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </slot>
                        <language-and-theme/>
                      </slot>
                    </div>

                  </div>
                </div>
              </slot>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  </header>

  <!-- 登录弹窗 -->
  <LoginDialog v-model:is-open="isLoginDialogOpen" @login-success="handleLoginSuccess"/>
</template>

import type {I18nTextMap} from '~/types/i18n' // 确保此路径正确，能够导入I18nTextMap

/**
 * @description 单个认证源的配置项结构
 * 遵循配置驱动文本的i18n规范
 */
export interface AuthSourceConfigItem {
    /**
     * @description 认证源的唯一标识符 (例如: 'google', 'github')
     * 与后端 /app/auth/authSourceList 返回的字符串对应
     */
    key: string;

    /**
     * @description 认证源的显示名称，支持国际化
     * 结构为 { en: "English Name", zh: "中文名称", ... }
     */
    name: I18nTextMap;

    /**
     * @description Icones.js.org 上的图标名称 (例如: 'logos:google-icon')
     */
    icon: string;

    /**
     * @description 在登录页面显示的顺序，数字越小越靠前
     */
    order: number;

    /**
     * @description 是否默认显示在主登录区域 (true) 或折叠在"更多"中 (false)
     * @default true
     */
    defaultVisible?: boolean;
}
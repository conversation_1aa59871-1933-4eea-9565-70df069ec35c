import logger from './logger' // 导入logger

// 临时的加密函数占位符 - 后续请替换为真实的加密实现
function encrypto(data: any): string {
    // 提示: 这是占位符，仅进行JSON序列化。请实现真实的加密逻辑。
    logger.warn('localStorageService: Using placeholder encrypto function.')
    try {
        return JSON.stringify(data)
    } catch (e) {
        logger.error('Error stringifying data for localStorage:', e)
        return ''
    }
}

// 临时的解密函数占位符 - 后续请替换为真实的解密实现
function decrypto(json: string | null): any {
    // 提示: 这是占位符，仅进行JSON解析。请实现真实的解密逻辑。
    logger.warn('localStorageService: Using placeholder decrypto function.')
    if (!json) {
        return null
    }
    try {
        return JSON.parse(json)
    } catch (e) {
        logger.error('Error parsing data from localStorage:', e)
        return null
    }
}

interface StorageData<T = any> {
    value: T;
    expire: number | null;
}

function createLocalStorageService() {
    /** 默认缓存时间为7天 (单位：秒) */
    const DEFAULT_CACHE_TIME_SECONDS = 60 * 60 * 24 * 7

    /**
     * 设置localStorage项
     * @param key 键名
     * @param value 值
     * @param expire 过期时间 (单位：秒)，如果为null则永不过期
     */
    function set<T = any>(key: string, value: T, expire: number | null = DEFAULT_CACHE_TIME_SECONDS) {
        // 确保在客户端环境下执行
        if (typeof window === 'undefined') {
            logger.warn('localStorageService.set called on server side. Operation skipped.', {key})
            return
        }
        const storageData: StorageData<T> = {
            value,
            expire: expire !== null ? new Date().getTime() + expire * 1000 : null
        }
        const jsonToStore = encrypto(storageData)
        window.localStorage.setItem(key, jsonToStore)
    }

    /**
     * 获取localStorage项
     * @param key 键名
     * @returns 存储的值，如果不存在、已过期或解析失败则返回null
     */
    function get<T = any>(key: string): T | null {
        // 确保在客户端环境下执行
        if (typeof window === 'undefined') {
            logger.warn('localStorageService.get called on server side. Returning null.', {key})
            return null
        }
        const json = window.localStorage.getItem(key)
        if (json) {
            let storageData: StorageData<T> | null = null
            try {
                storageData = decrypto(json) as StorageData<T>
            } catch (e) {
                logger.error('localStorageService: Failed to decrypto (catch block in get)', e, {storageKey: key})
            }

            if (storageData) {
                const {value, expire: expirationTime} = storageData
                // 如果在有效期内，则直接返回
                if (expirationTime === null || expirationTime >= Date.now()) {
                    return value
                }
            }
            // 如果数据不存在、解析失败、或已过期，则移除该项
            remove(key)
            return null
        }
        return null
    }

    /**
     * 移除localStorage项
     * @param key 键名
     */
    function remove(key: string) {
        // 确保在客户端环境下执行
        if (typeof window === 'undefined') {
            logger.warn('localStorageService.remove called on server side. Operation skipped.', {key})
            return
        }
        window.localStorage.removeItem(key)
    }

    /**
     * 清空所有localStorage项
     */
    function clear() {
        // 确保在客户端环境下执行
        if (typeof window === 'undefined') {
            logger.warn('localStorageService.clear called on server side. Operation skipped.')
            return
        }
        window.localStorage.clear()
    }

    return {
        set,
        get,
        remove,
        clear
    }
}

export const localStg = createLocalStorageService()
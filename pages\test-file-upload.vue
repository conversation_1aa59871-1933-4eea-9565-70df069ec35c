<template>
  <div class="container mx-auto p-6 max-w-4xl">
    <h1 class="text-3xl font-bold mb-6">文件上传测试页面</h1>
    
    <!-- 文件选择区域 -->
    <div class="mb-8">
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <input
          ref="fileInput"
          type="file"
          multiple
          class="hidden"
          @change="handleFileSelect"
        >
        <button
          class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
          @click="fileInput?.click()"
        >
          选择文件
        </button>
        <p class="mt-2 text-gray-500">支持多文件选择</p>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="selectedFiles.length > 0" class="mb-8">
      <h2 class="text-xl font-semibold mb-4">选中的文件</h2>
      <div class="space-y-4">
        <div
          v-for="(fileInfo, index) in selectedFiles"
          :key="index"
          class="border rounded-lg p-4"
        >
          <div class="flex justify-between items-start mb-2">
            <div>
              <h3 class="font-medium">{{ fileInfo.file.name }}</h3>
              <p class="text-sm text-gray-500">
                大小: {{ computeFileSize(fileInfo.file.size) }}
              </p>
              <p class="text-sm text-gray-500">
                类型: {{ getFileTypeDescription(fileInfo.file) }}
              </p>
            </div>
            <div class="text-right">
              <button
                :disabled="fileInfo.isCalculating"
                class="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm mr-2"
                @click="calculateHash(index)"
              >
                {{ fileInfo.isCalculating ? '计算中...' : '计算MD5' }}
              </button>
              <button
                :disabled="fileInfo.isUploading || !fileInfo.hash"
                class="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm"
                @click="startChunkUpload(index)"
              >
                {{ fileInfo.isUploading ? '上传中...' : '分片上传' }}
              </button>
            </div>
          </div>
          
          <!-- 哈希计算进度 -->
          <div v-if="fileInfo.isCalculating" class="mb-2">
            <div class="flex justify-between text-sm mb-1">
              <span>MD5计算进度</span>
              <span>{{ fileInfo.hashProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-green-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: fileInfo.hashProgress + '%' }"
              />
            </div>
          </div>

          <!-- 上传进度 -->
          <div v-if="fileInfo.isUploading" class="mb-2">
            <div class="flex justify-between text-sm mb-1">
              <span>上传进度</span>
              <span>{{ fileInfo.uploadProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-purple-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: fileInfo.uploadProgress + '%' }"
              />
            </div>
          </div>

          <!-- 结果显示 -->
          <div v-if="fileInfo.hash" class="mt-2">
            <p class="text-sm">
              <span class="font-medium">MD5:</span>
              <span class="font-mono text-xs bg-gray-100 px-2 py-1 rounded">{{ fileInfo.hash }}</span>
            </p>
            <p v-if="fileInfo.hashDuration" class="text-sm text-gray-500">
              计算耗时: {{ fileInfo.hashDuration }}ms
            </p>
          </div>

          <div v-if="fileInfo.uploadResult" class="mt-2 p-2 bg-green-50 rounded">
            <p class="text-sm text-green-700">上传成功！</p>
          </div>

          <div v-if="fileInfo.error" class="mt-2 p-2 bg-red-50 rounded">
            <p class="text-sm text-red-700">错误: {{ fileInfo.error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedFiles.length > 1" class="mb-8">
      <h2 class="text-xl font-semibold mb-4">批量操作</h2>
      <div class="space-x-4">
        <button
          :disabled="isBatchCalculating"
          class="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
          @click="calculateAllHashes"
        >
          {{ isBatchCalculating ? '批量计算中...' : '批量计算MD5' }}
        </button>
        <button
          :disabled="!allHashesCalculated"
          class="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
          @click="findDuplicates"
        >
          查找重复文件
        </button>
      </div>
      
      <!-- 批量进度 -->
      <div v-if="isBatchCalculating" class="mt-4">
        <div class="flex justify-between text-sm mb-1">
          <span>批量计算进度</span>
          <span>{{ batchProgress }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div
            class="bg-blue-500 h-2 rounded-full transition-all duration-300"
            :style="{ width: batchProgress + '%' }"
          />
        </div>
      </div>
    </div>

    <!-- 重复文件结果 -->
    <div v-if="duplicateFiles.size > 0" class="mb-8">
      <h2 class="text-xl font-semibold mb-4">重复文件检测结果</h2>
      <div class="space-y-4">
        <div
          v-for="[hash, files] of duplicateFiles"
          :key="hash"
          class="border rounded-lg p-4 bg-yellow-50"
        >
          <h3 class="font-medium mb-2">重复组 (MD5: {{ hash.substring(0, 8) }}...)</h3>
          <ul class="space-y-1">
            <li v-for="file in files" :key="file.name" class="text-sm">
              • {{ file.name }} ({{ computeFileSize(file.size) }})
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  calculateFileHash,
  formatFileSize,
  isImageFile,
  isVideoFile,
  isAudioFile,
  type FileHashResult
} from '~/utils/typeof'
import { fileUploadUtils, type CheckAndMergeParam, type ChunkUploadParam } from '~/utils/fileUpload'

// 页面元数据
definePageMeta({
  title: '文件上传测试',
  description: '测试文件哈希计算和分片上传功能'
})

interface FileInfo {
    file: File
    hash?: string
    hashDuration?: number
    isCalculating: boolean
    hashProgress: number
    isUploading: boolean
    uploadProgress: number
    uploadResult?: any
    error?: string
}

const fileInput = ref<HTMLInputElement>()
const selectedFiles = ref<FileInfo[]>([])
const isBatchCalculating = ref(false)
const batchProgress = ref(0)
const duplicateFiles = ref<Map<string, File[]>>(new Map())

// 计算属性
const allHashesCalculated = computed(() => {
  return selectedFiles.value.length > 0
           && selectedFiles.value.every(f => f.hash && !f.isCalculating)
})

// 文件选择处理
function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  
  selectedFiles.value = files.map(file => ({
    file,
    isCalculating: false,
    hashProgress: 0,
    isUploading: false,
    uploadProgress: 0
  }))
  
  // 清空重复文件结果
  duplicateFiles.value.clear()
}

// 计算单个文件哈希
async function calculateHash(index: number) {
  const fileInfo = selectedFiles.value[index]
  if (!fileInfo || fileInfo.isCalculating) {return}
  
  fileInfo.isCalculating = true
  fileInfo.hashProgress = 0
  fileInfo.error = undefined
  
  try {
    const result = await calculateFileHash(fileInfo.file, {
      onProgress: (progress) => {
        fileInfo.hashProgress = progress
      }
    })
    
    fileInfo.hash = result.hash
    fileInfo.hashDuration = result.duration
  } catch (error) {
    fileInfo.error = `哈希计算失败: ${error}`
  } finally {
    fileInfo.isCalculating = false
  }
}

// 批量计算哈希
async function calculateAllHashes() {
  if (isBatchCalculating.value) {return}
  
  isBatchCalculating.value = true
  batchProgress.value = 0
  
  try {
    for (let i = 0; i < selectedFiles.value.length; i++) {
      if (!selectedFiles.value[i].hash) {
        await calculateHash(i)
      }
      batchProgress.value = Math.round(((i + 1) / selectedFiles.value.length) * 100)
    }
  } finally {
    isBatchCalculating.value = false
  }
}

// 查找重复文件
function findDuplicates() {
  const hashMap = new Map<string, File[]>()
  
  selectedFiles.value.forEach(fileInfo => {
    if (fileInfo.hash) {
      if (!hashMap.has(fileInfo.hash)) {
        hashMap.set(fileInfo.hash, [])
      }
            hashMap.get(fileInfo.hash)!.push(fileInfo.file)
    }
  })
  
  // 只保留有重复的文件
  const duplicates = new Map<string, File[]>()
  hashMap.forEach((files, hash) => {
    if (files.length > 1) {
      duplicates.set(hash, files)
    }
  })
  
  duplicateFiles.value = duplicates
}

// 开始分片上传
async function startChunkUpload(index: number) {
  const fileInfo = selectedFiles.value[index]
  if (!fileInfo || !fileInfo.hash || fileInfo.isUploading) {return}
  
  fileInfo.isUploading = true
  fileInfo.uploadProgress = 0
  fileInfo.error = undefined
  
  try {
    // 模拟的检查和合并函数
    const mockCheckAndMerge = async (param: CheckAndMergeParam) => {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      return {
        code: 200,
        data: {
          finishStatus: false,
          chunkUploadStatus: undefined // 表示没有已上传的分片
        }
      }
    }
    
    // 模拟的分片上传函数
    const mockChunkUpload = async (param: ChunkUploadParam) => {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 200))
      return { success: true }
    }
    
    // 更新进度函数
    const updateProgress = (uid: string, progress: number) => {
      fileInfo.uploadProgress = Math.round(progress)
    }
    
    // 成功处理函数
    const handleSuccess = (fileInfoResult: any, file: File) => {
      fileInfo.uploadResult = fileInfoResult
      fileInfo.isUploading = false
    }
    
    // 失败处理函数
    const handleFailure = (result: any, file: File) => {
      fileInfo.error = '上传失败'
      fileInfo.isUploading = false
    }
    
    // 添加uid属性
    const fileWithUid = Object.assign(fileInfo.file, { uid: `file-${index}` })
    
    await fileUploadUtils.chunkUpload(
      fileWithUid,
      { fileName: fileInfo.file.name, fileSize: fileInfo.file.size },
      1024 * 1024, // 1MB分片
      mockCheckAndMerge,
      mockChunkUpload,
      updateProgress,
      3, // 最大并发数
      handleSuccess,
      handleFailure
    )
  } catch (error) {
    fileInfo.error = `上传失败: ${error}`
    fileInfo.isUploading = false
  }
}

// 工具函数
function computeFileSize(size: number): string {
  return formatFileSize(size)
}

function getFileTypeDescription(file: File): string {
  if (isImageFile(file)) {return '图片文件'}
  if (isVideoFile(file)) {return '视频文件'}
  if (isAudioFile(file)) {return '音频文件'}
  return '其他文件'
}
</script>

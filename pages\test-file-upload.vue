<template>
  <div class="container mx-auto p-6 max-w-4xl">
    <h1 class="text-3xl font-bold mb-6">文件上传测试页面</h1>

    <!-- 文件选择区域 -->
    <div class="mb-8">
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <input ref="fileInput"
               type="file"
               multiple
               class="hidden"
               @change="handleFileSelect">
        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg" @click="fileInput?.click()">
          选择文件
        </button>
        <p class="mt-2 text-gray-500">支持多文件选择</p>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="selectedFiles.length > 0" class="mb-8">
      <h2 class="text-xl font-semibold mb-4">选中的文件</h2>
      <div class="space-y-4">
        <div v-for="(fileInfo, index) in selectedFiles" :key="index" class="border rounded-lg p-4">
          <div class="flex justify-between items-start mb-2">
            <div>
              <h3 class="font-medium">{{ fileInfo.file.name }}</h3>
              <p class="text-sm text-gray-500">
                大小: {{ computeFileSize(fileInfo.file.size) }}
              </p>
              <p class="text-sm text-gray-500">
                类型: {{ getFileTypeDescription(fileInfo.file) }}
              </p>
            </div>
            <div class="text-right">
              <button :disabled="fileInfo.isCalculating"
                      class="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm mr-2"
                      @click="calculateHash(index)">
                {{ fileInfo.isCalculating ? '计算中...' : '计算MD5' }}
              </button>
              <button :disabled="fileInfo.isUploading || !fileInfo.hash"
                      class="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm"
                      @click="startChunkUpload(index)">
                {{ fileInfo.isUploading ? '上传中...' : '分片上传' }}
              </button>
            </div>
          </div>

          <!-- 哈希计算状态 -->
          <div v-if="fileInfo.isCalculating" class="mb-2">
            <div class="flex items-center text-sm text-blue-600">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2" />
              <span>正在计算MD5哈希...</span>
            </div>
          </div>

          <!-- 上传进度 -->
          <div v-if="fileInfo.isUploading" class="mb-2">
            <div class="flex justify-between text-sm mb-1">
              <span>上传进度</span>
              <span>{{ fileInfo.uploadProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-purple-500 h-2 rounded-full transition-all duration-300"
                   :style="{ width: fileInfo.uploadProgress + '%' }" />
            </div>
          </div>

          <!-- 结果显示 -->
          <div v-if="fileInfo.hash" class="mt-2">
            <p class="text-sm">
              <span class="font-medium">MD5:</span>
              <span class="font-mono text-xs bg-gray-100 px-2 py-1 rounded">{{ fileInfo.hash }}</span>
            </p>
          </div>

          <div v-if="fileInfo.uploadResult" class="mt-2 p-2 bg-green-50 rounded">
            <p class="text-sm text-green-700">上传成功！</p>
          </div>

          <div v-if="fileInfo.error" class="mt-2 p-2 bg-red-50 rounded">
            <p class="text-sm text-red-700">错误: {{ fileInfo.error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedFiles.length > 1" class="mb-8">
      <h2 class="text-xl font-semibold mb-4">批量操作</h2>
      <div class="space-x-4">
        <button :disabled="isBatchCalculating"
                class="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
                @click="calculateAllHashes">
          {{ isBatchCalculating ? '批量计算中...' : '批量计算MD5' }}
        </button>
        <button :disabled="!allHashesCalculated"
                class="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
                @click="findDuplicates">
          查找重复文件
        </button>
      </div>

      <!-- 批量进度 -->
      <div v-if="isBatchCalculating" class="mt-4">
        <div class="flex justify-between text-sm mb-1">
          <span>批量计算进度</span>
          <span>{{ batchProgress }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-blue-500 h-2 rounded-full transition-all duration-300"
               :style="{ width: batchProgress + '%' }" />
        </div>
      </div>
    </div>

    <!-- 重复文件结果 -->
    <div v-if="duplicateFiles.size > 0" class="mb-8">
      <h2 class="text-xl font-semibold mb-4">重复文件检测结果</h2>
      <div class="space-y-4">
        <div v-for="[hash, files] of duplicateFiles" :key="hash" class="border rounded-lg p-4 bg-yellow-50">
          <h3 class="font-medium mb-2">重复组 (MD5: {{ hash.substring(0, 8) }}...)</h3>
          <ul class="space-y-1">
            <li v-for="file in files" :key="file.name" class="text-sm">
              • {{ file.name }} ({{ computeFileSize(file.size) }})
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { calculateFileHash, formatFileSize, isAudioFile, isImageFile, isVideoFile } from '~/utils/typeof'
import { fileUploadUtils } from '~/utils/fileUpload'
import type { CheckAndMergeParams } from '~/types/api/params/CheckAndMergeParams'
import type { ChunkUploadParams } from '~/types/api/params/ChunkUploadParams'

// 页面元数据
definePageMeta({
  title: '文件上传测试',
  description: '测试文件哈希计算和分片上传功能'
})

interface LocalFileInfo {
  file: File
  hash?: string
  isCalculating: boolean
  isUploading: boolean
  uploadProgress: number
  uploadResult?: any
  error?: string
}

const fileInput = ref<HTMLInputElement>()
const selectedFiles = ref<LocalFileInfo[]>([])
const isBatchCalculating = ref(false)
const batchProgress = ref(0)
const duplicateFiles = ref<Map<string, File[]>>(new Map())

// 计算属性
const allHashesCalculated = computed(() => {
  return selectedFiles.value.length > 0
    && selectedFiles.value.every(f => f.hash && !f.isCalculating)
})

// 文件选择处理
function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])

  selectedFiles.value = files.map(file => ({
    file,
    isCalculating: false,
    isUploading: false,
    uploadProgress: 0
  }))

  // 清空重复文件结果
  duplicateFiles.value.clear()
}

// 计算单个文件哈希
async function calculateHash(index: number) {
  const fileInfo = selectedFiles.value[index]
  if (!fileInfo || fileInfo.isCalculating) { return }

  fileInfo.isCalculating = true
  fileInfo.error = undefined

  try {
    const hash = await calculateFileHash(fileInfo.file)
    fileInfo.hash = hash
  } catch (error) {
    fileInfo.error = `哈希计算失败: ${error}`
  } finally {
    fileInfo.isCalculating = false
  }
}

// 批量计算哈希
async function calculateAllHashes() {
  if (isBatchCalculating.value) { return }

  isBatchCalculating.value = true
  batchProgress.value = 0

  try {
    for (let i = 0; i < selectedFiles.value.length; i++) {
      if (!selectedFiles.value[i].hash) {
        await calculateHash(i)
      }
      batchProgress.value = Math.round(((i + 1) / selectedFiles.value.length) * 100)
    }
  } finally {
    isBatchCalculating.value = false
  }
}

// 查找重复文件
function findDuplicates() {
  const hashMap = new Map<string, File[]>()

  selectedFiles.value.forEach(fileInfo => {
    if (fileInfo.hash) {
      if (!hashMap.has(fileInfo.hash)) {
        hashMap.set(fileInfo.hash, [])
      }
      hashMap.get(fileInfo.hash)!.push(fileInfo.file)
    }
  })

  // 只保留有重复的文件
  const duplicates = new Map<string, File[]>()
  hashMap.forEach((files, hash) => {
    if (files.length > 1) {
      duplicates.set(hash, files)
    }
  })

  duplicateFiles.value = duplicates
}

// 开始分片上传
async function startChunkUpload(index: number) {
  const fileInfo = selectedFiles.value[index]
  if (!fileInfo || !fileInfo.hash || fileInfo.isUploading) { return }

  fileInfo.isUploading = true
  fileInfo.uploadProgress = 0
  fileInfo.error = undefined

  try {
    // 模拟的检查和合并函数
    const mockCheckAndMerge = async (param: CheckAndMergeParams) => {
      console.log('检查文件状态:', param)
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200))

      // 随机模拟是否已存在文件
      const isFileExists = Math.random() < 0.1 // 10%概率文件已存在

      if (isFileExists) {
        return {
          code: 200,
          data: {
            finishStatus: true,
            fileInfo: {
              url: `https://example.com/files/${param.md5}`,
              filename: param.fileName
            }
          }
        }
      }

      return {
        code: 200,
        data: {
          finishStatus: false,
          chunkUploadStatus: undefined // 表示没有已上传的分片
        }
      }
    }

    // 模拟的分片上传函数
    const mockChunkUpload = async (param: ChunkUploadParams) => {
      console.log(`上传分片 ${param.currentChunkNumber + 1}/${param.totalChunkCount}`)
      // 模拟网络延迟，分片越大延迟越长
      const delay = 100 + Math.random() * 300
      await new Promise(resolve => setTimeout(resolve, delay))

      // 模拟偶尔的上传失败
      if (Math.random() < 0.05) { // 5%概率失败
        throw new Error('网络错误，分片上传失败')
      }

      return {
        success: true,
        chunkNumber: param.currentChunkNumber
      }
    }

    // 更新进度函数
    const updateProgress = (_uid: string, progress: number) => {
      fileInfo.uploadProgress = Math.round(progress)
    }

    // 成功处理函数
    const handleSuccess = (fileInfoResult: any, _file: File) => {
      fileInfo.uploadResult = fileInfoResult
      fileInfo.isUploading = false
      console.log('文件上传成功:', fileInfoResult)
    }

    // 失败处理函数
    const handleFailure = (result: any, _file: File) => {
      fileInfo.error = `上传失败: ${result?.message || '未知错误'}`
      fileInfo.isUploading = false
      console.error('文件上传失败:', result)
    }

    // 添加uid属性
    const fileWithUid = Object.assign(fileInfo.file, { uid: `file-${index}` })

    await fileUploadUtils.chunkUpload(
      fileWithUid,
      { fileName: fileInfo.file.name, fileSize: fileInfo.file.size },
      1024 * 1024, // 1MB分片
      mockCheckAndMerge,
      mockChunkUpload,
      updateProgress,
      3, // 最大并发数
      handleSuccess,
      handleFailure
    )
  } catch (error) {
    fileInfo.error = `上传失败: ${error}`
    fileInfo.isUploading = false
  }
}

// 工具函数
function computeFileSize(size: number): string {
  return formatFileSize(size)
}

function getFileTypeDescription(file: File): string {
  if (isImageFile(file)) { return '图片文件' }
  if (isVideoFile(file)) { return '视频文件' }
  if (isAudioFile(file)) { return '音频文件' }
  return '其他文件'
}
</script>

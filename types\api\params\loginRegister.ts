/**
 * 统一的登录注册参数接口，对应Java中的LoginRegister类。
 * 所有字段都是可选的，调用API时按需填充。
 */
export interface LoginRegister {
 /**
  * 用户名
  */
    username?: string;

    /**
     * 密码
     */
    password?: string;

    /**
     * 验证码
     */
    captcha?: string;

    /**
     * 手机号
     */
    phone?: string;

    /**
     * 邮箱
     */
    email?: string;

    /**
     * 项目名
     */
    projectName?: string;

    /**
     * 语言
     */
    systemLanguage?: string;
}
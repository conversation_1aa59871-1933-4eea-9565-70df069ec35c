<template>
  <div class="p-6 border rounded-lg shadow">
    <h2 class="text-xl font-semibold mb-4">
      {{ getLocalizedConfigText(configData.pageTitle, 'Default Page Title') }}
    </h2>
    <p class="text-muted-foreground mb-6">
      {{ getLocalizedConfigText(configData.welcomeMessage, 'Default Welcome Message') }}
    </p>

    <h3 class="text-lg font-medium mb-3">
      Features:
    </h3>
    <ul class="space-y-4">
      <li v-for="feature in configData.features" :key="feature.id" class="flex items-start space-x-3">
        <Icon :name="feature.icon || 'heroicons:check-circle'" class="w-5 h-5 text-primary flex-shrink-0 mt-1"/>
        <div>
          <p class="font-medium text-foreground">
            {{ getLocalizedConfigText(feature.name, 'Default Feature Name') }}
          </p>
          <p class="text-sm text-muted-foreground">
            {{ getLocalizedConfigText(feature.description, 'Default Feature Description') }}
          </p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import {Icon} from '#components'
import {getLocalizedConfigText} from '@/utils/i18n/utils'
import type {I18nExampleConfig} from '~/types/site/i18nExample'
import {i18nExampleConfig} from '@/config/i18nExampleConfig'

// 应用类型，确保类型安全
const configData = i18nExampleConfig as I18nExampleConfig

// 注意：
// 1. getLocalizedConfigText 第二个参数是可选的回退文本，在找不到对应语言或默认语言时显示。
// 2. 假设 <Icon> 组件已全局注册或可通过 #components 自动导入。
</script>
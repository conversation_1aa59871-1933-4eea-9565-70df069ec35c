<script setup lang="ts">
import {cn} from '@/lib/utils'
import {AccordionContent, type AccordionContentProps} from 'reka-ui'
import {computed, type HTMLAttributes} from 'vue'

const props = defineProps<AccordionContentProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = computed(() => {
    const {class: _, ...delegated} = props

    return delegated
})
</script>

<template>
  <AccordionContent
    data-slot="accordion-content"
    v-bind="delegatedProps"
    class="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm"
  >
    <div :class="cn('pt-0 pb-4', props.class)">
      <slot/>
    </div>
  </AccordionContent>
</template>

<script setup lang="ts">
// 导入shadcn-vue组件
import Card from '@/components/ui/card/Card.vue'
import CardContent from '@/components/ui/card/CardContent.vue'
import {getLocalizedConfigText, resolveLocalePath} from '~/utils/i18n'
import dashboardProfileLinks from '~/config/dashboardProfileLinks'
import type {NavLink} from '~/types/site/navLinks'
import type {DashboardBreadcrumbItem} from '~/types/ui'

// 获取当前路由以确定激活状态
const route = useRoute()
const dashboardBreadcrumbsRef = useState<DashboardBreadcrumbItem[]>('dashboardBreadcrumbs')

// 判断导航项是否激活
const isActive = (href: string) => {
    return route.path === href
}

function handleLinkClick(item: NavLink) {
    const newCrumbs: DashboardBreadcrumbItem[] = []

    newCrumbs.push({text: getLocalizedConfigText(item.title), href: resolveLocalePath(item.href)})
    dashboardBreadcrumbsRef.value = newCrumbs
}
</script>

<template>
  <div class="flex flex-col md:flex-row gap-8">
    <!-- 导航菜单 - 在移动端水平滚动，桌面端垂直显示 -->
    <div class="w-full md:w-1/7">
      <nav class="flex flex-row md:flex-col gap-2 overflow-x-auto whitespace-nowrap">
        <NuxtLink v-for="item in dashboardProfileLinks"
                  :key="item.href"
                  :to="resolveLocalePath(item.href)"
                  :class="[
                    'py-2 pr-2 rounded-md transition-colors flex-shrink-0',
                    isActive(item.href) ? 'text-primary font-medium' : 'text-foreground hover:text-primary'
                  ]"
                  @click="handleLinkClick(item)">
          {{ getLocalizedConfigText(item.title) }}
        </NuxtLink>
      </nav>
    </div>

    <!-- 右侧内容区域 -->
    <div class="w-full md:w-3/4">
      <Card>
        <CardContent>
          <NuxtPage/>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
# PageNavbar 组件使用文档

`PageNavbar` 是一个用于在 `Header` 组件内部或类似场景下，展示网站Logo、品牌名称以及主要导航链接的组件。它专注于导航栏核心元素的布局和国际化显示。

## Props (属性)

`PageNavbar` 组件目前没有定义自己独立的 props。其行为和内容主要通过依赖注入的配置 (如 `siteConfig`, `navLinks`) 和插槽进行定制。

## 数据配置

`PageNavbar` 组件的内容（如网站标题、Logo、导航链接）通常依赖于以下配置文件，这些配置由父组件（如 `Header.vue`
）或全局状态提供，并在组件内部响应式地使用：

- `~/config/navLinks.ts` (或 `.json`): 定义导航链接的数组，每个链接包含 `href`, `title` (I18nTextMap), `icon` (可选)。
- `~/config/site.ts` (或 `.json`): 定义网站的基本配置，如 `title` (I18nTextMap), `logo` (图片URL)。

组件内部会使用 `getLocalizedConfigText` 工具函数和 `$localePath` 辅助函数根据当前i18n语言环境自动加载对应的文本和生成本地化路径。

## 插槽 (Slots)

`PageNavbar` 组件提供了以下插槽以实现灵活的定制：

- **`nav-logo` (作用域插槽)**:
    - **描述**: 自定义导航栏最左侧的Logo和网站标题区域。
    - **作用域数据**:
        - `siteConfig`: `object` - 来自 `~/config/site.ts` 的网站配置对象。

- **`before-nav`**:
    - **描述**: 在主要导航链接 (`<nav>`) 开始之前，但在Logo区域之后插入内容。通常用于PC端视图。
    - **作用域数据**: 无。

- **`nav-show` (作用域插槽)**:
    - **描述**: 自定义主要的导航链接列表的显示。默认情况下，它会遍历 `navLinks` 配置并使用 `NuxtLink` 和 `Icon` (如果提供)
      来渲染链接。此插槽通常用于PC端视图。
    - **作用域数据**:
        - `navLinks`: `array` - 来自 `~/config/navLinks.ts` 的导航链接数组 (已准备好供模板使用，文本已本地化)。

- **`after-nav`**:
    - **描述**: 在主要导航链接 (`<nav>`) 结束之后插入内容。通常用于PC端视图。
    - **作用域数据**: 无。

## 基本用法示例

`PageNavbar` 通常作为 `Header` 组件 `navbar` 插槽的一部分来使用。

```vue
<!-- 在 Header.vue 或类似的父组件模板中 -->
<template>
  <Header>
    <template #navbar>
      <PageNavbar />
    </template>
  </Header>
</template>

<script setup lang="ts">
// import Header from '~/components/market/header/Header.vue';
// PageNavbar 会被自动导入
// import PageNavbar from '~/components/page/PageNavbar.vue'; 
</script>
```

## 使用自定义插槽示例

```vue
<!-- 在 Header.vue 或类似的父组件模板中，填充 PageNavbar 的插槽 -->
<template>
  <Header>
    <template #navbar>
      <PageNavbar>
        <template #nav-logo="{ siteConfig }">
          <NuxtLink :to="$localePath({ path: '/' })" class="flex items-center h-full text-purple-600 dark:text-purple-400">
            <img v-if="siteConfig.logo" 
                 :src="siteConfig.logo" 
                 alt="Custom Logo" 
                 class="w-8 h-8 mr-2 rounded-md border-2 border-purple-500">
            <span class="font-bold text-xl tracking-tight">{{ getLocalizedConfigText(siteConfig.title, 'My Site') }} (定制Logo区)</span>
          </NuxtLink>
        </template>

        <template #before-nav>
          <div class="hidden md:flex items-center h-full ml-3 mr-1">
            <span class="px-2 py-0.5 bg-pink-500 text-white text-xs rounded-full animate-pulse">
              插槽: 前置内容
            </span>
          </div>
        </template>

        <template #nav-show="{ navLinks }">
          <nav class="hidden md:flex md:space-x-5 lg:space-x-8 h-full items-center border-x-2 border-dashed border-gray-300 dark:border-gray-700 px-4">
            <NuxtLink v-for="(link, index) in navLinks" 
                      :key="index" 
                      :to="$localePath({ path: link.href })"
                      class="flex items-center h-auto py-2 text-sm font-medium hover:text-pink-500 dark:hover:text-pink-400 transition-all duration-150 border-b-2 border-transparent hover:border-pink-500">
              <Icon v-if="link.icon" :name="link.icon" class="h-4 w-4 mr-1.5" />
              <span>{{ getLocalizedConfigText(link.title) }} ✨</span>
            </NuxtLink>
          </nav>
        </template>

        <template #after-nav>
          <div class="hidden md:flex items-center h-full ml-3">
            <button class="px-3 py-1.5 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 focus:ring-2 focus:ring-green-400 focus:ring-offset-2">
              <Icon name="lucide:gift" class="h-4 w-4 mr-1 inline-block" />
              插槽: 后置按钮
            </button>
          </div>
        </template>
      </PageNavbar>
    </template>
  </Header>
</template>

<script setup lang="ts">
// import Header from '~/components/market/header/Header.vue';
// PageNavbar, Icon, NuxtLink 会自动导入
// import { getLocalizedConfigText } from '~/utils/i18n'; // 如果在插槽内使用
</script>
```

**注意**:

- 上述插槽示例中的CSS类名主要基于Tailwind CSS。
- 在插槽内容中如果使用到如 `Icon`, `NuxtLink`, `getLocalizedConfigText`, `$localePath` 等，请确保它们在 `index.vue` (
  或使用这些插槽的父组件) 的 `<script setup>` 中已正确导入或全局可用。 
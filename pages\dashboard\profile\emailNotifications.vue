<script setup lang="ts">
import {ref} from 'vue'
import Label from '@/components/ui/label/Label.vue'
import Button from '@/components/ui/button/Button.vue'
import Switch from '@/components/ui/switch/Switch.vue'
import {Separator} from '@/components/ui/separator'

const notificationSettings = ref({
    email: true,
    push: false,
    sms: true,
    promotional: false,
    security: true,
    newsletter: false,
    updates: true,
    comments: false
})

// 保存通知设置
const saveNotificationSettings = () => {
    // 实现保存通知设置逻辑
    alert('通知设置已更新')
}
</script>

<template>
  <div>
    <h2 class="text-2xl font-bold mb-6">邮件通知设置</h2>
    <p class="text-muted-foreground mb-8">配置您希望接收的通知类型</p>

    <form @submit.prevent="saveNotificationSettings" class="space-y-6">
      <!-- 通知偏好设置 -->
      <div>
        <h3 class="text-lg font-semibold mb-4">通知类型</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <Label for="email-notifications" class="font-medium">电子邮件通知</Label>
              <p class="text-sm text-muted-foreground">接收系统相关的电子邮件通知</p>
            </div>
            <Switch id="email-notifications" v-model="notificationSettings.email"/>
          </div>
          <Separator/>
          <div class="flex items-center justify-between">
            <div>
              <Label for="push-notifications" class="font-medium">推送通知</Label>
              <p class="text-sm text-muted-foreground">接收实时推送通知</p>
            </div>
            <Switch id="push-notifications" v-model="notificationSettings.push"/>
          </div>
          <Separator/>
          <div class="flex items-center justify-between">
            <div>
              <Label for="sms-notifications" class="font-medium">短信通知</Label>
              <p class="text-sm text-muted-foreground">接收重要事件的短信提醒</p>
            </div>
            <Switch id="sms-notifications" v-model="notificationSettings.sms"/>
          </div>
        </div>
      </div>

      <!-- 营销通信设置 -->
      <div class="pt-4">
        <h3 class="text-lg font-semibold mb-4">营销通信</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <Label for="promotional-notifications" class="font-medium">促销信息</Label>
              <p class="text-sm text-muted-foreground">接收特别优惠和促销活动的通知</p>
            </div>
            <Switch id="promotional-notifications" v-model="notificationSettings.promotional"/>
          </div>
          <Separator/>
          <div class="flex items-center justify-between">
            <div>
              <Label for="newsletter" class="font-medium">电子通讯</Label>
              <p class="text-sm text-muted-foreground">订阅我们的月度通讯</p>
            </div>
            <Switch id="newsletter" v-model="notificationSettings.newsletter"/>
          </div>
          <Separator/>
          <div class="flex items-center justify-between">
            <div>
              <Label for="updates" class="font-medium">产品更新</Label>
              <p class="text-sm text-muted-foreground">接收产品功能更新和改进的通知</p>
            </div>
            <Switch id="updates" v-model="notificationSettings.updates"/>
          </div>
        </div>
      </div>

      <!-- 安全相关通知 -->
      <div class="pt-4">
        <h3 class="text-lg font-semibold mb-4">安全通知</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <Label for="security-notifications" class="font-medium">安全提醒</Label>
              <p class="text-sm text-muted-foreground">接收关于账户安全的重要通知（此项不可关闭）</p>
            </div>
            <Switch id="security-notifications" v-model="notificationSettings.security" disabled/>
          </div>
        </div>
      </div>

      <Button type="submit" class="mt-8">保存设置</Button>
    </form>
  </div>
</template>
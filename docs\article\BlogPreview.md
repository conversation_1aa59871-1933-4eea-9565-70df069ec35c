# BlogPreview 组件使用文档

`BlogPreview` 是一个用于展示博客文章预览列表的 Vue 组件。它通常用于首页或相关推荐部分，能够自动获取最新的文章，并支持为 PC
和移动端配置不同的显示布局（网格或滚动）和行数。

## Props (属性)

以下是 `BlogPreview` 组件可接受的 props 列表：

| Prop 名称  | 类型                       | 描述                                                                                                                                        | 默认值 (PC / Mobile)                      |
|:---------|:-------------------------|:------------------------------------------------------------------------------------------------------------------------------------------|:---------------------------------------|
| `pc`     | `Partial<DisplayConfig>` | PC 端的显示配置。`DisplayConfig` 对象包含：<br>- `rows`: `number` (在 overlay 滚动模式下显示的行数)<br>- `displayStyle`: `'default'` (网格布局) 或 `'overlay'` (滚动布局) | `{ rows: 2, displayStyle: 'default' }` |
| `mobile` | `Partial<DisplayConfig>` | 移动端的显示配置。`DisplayConfig` 对象包含：<br>- `rows`: `number` (在 overlay 滚动模式下显示的行数)<br>- `displayStyle`: `'default'` (网格布局) 或 `'overlay'` (滚动布局)  | `{ rows: 1, displayStyle: 'overlay' }` |

*注：`Partial<DisplayConfig>` 意味着你可以只提供 `DisplayConfig` 中的部分或全部属性进行覆盖。*

**DisplayConfig 类型定义:**

```typescript
interface DisplayConfig {
    rows: number;
    displayStyle: 'default' | 'overlay';
}
```

## 数据获取与依赖

- **文章数据**: 组件内部通过 `postApi.page()` 方法异步获取最新的文章列表 (`ArticleResponse[]`)。
- **设备检测**: 组件会自动检测用户设备是 PC 还是移动端（基于 768px 视口宽度分界点），并应用相应的 `pc` 或 `mobile` 配置。
- **内部组件**:
    - 使用 `ArticleCard` 组件渲染列表中的每一篇文章。
    - 在 `'overlay'` 布局模式下，使用 `AutoScrollContainer` 组件实现内容的自动滚动效果。
- **国际化**: 组件默认标题和副标题通过 `tMsg('blogPreview.title')` 和 `tMsg('blogPreview.subtitle')` 获取，支持国际化。

## 布局模式

`BlogPreview` 支持两种主要的布局模式，通过 `pc.displayStyle` 和 `mobile.displayStyle` 配置：

- **`'default'`**: 传统的网格布局。文章卡片在多列中显示。
- **`'overlay'`**: 自动滚动布局。文章卡片会在一个或多个（通过 `rows` 配置）水平滚动的容器中展示，此模式利用
  `AutoScrollContainer` 组件实现。

## 插槽 (Slots)

`BlogPreview` 组件提供了丰富的插槽，允许对其各个部分进行灵活的定制：

| 插槽名称                   | 作用域数据                      | 描述                                                                                                                                      |
|:-----------------------|:---------------------------|:----------------------------------------------------------------------------------------------------------------------------------------|
| `before-layout`        | 无                          | 在组件最外层布局元素 (`<div class="blog-preview">`) 内部，但在所有其他内容之前插入。                                                                              |
| `header`               | 无                          | 自定义组件的头部区域。默认情况下，它会显示一个居中的标题和副标题。如果提供此插槽，将替换整个默认头部。                                                                                     |
| `loading`              | 无                          | 自定义组件在加载文章数据时显示的骨架屏或加载指示器。默认实现包含标题、副标题和文章卡片的骨架屏。                                                                                        |
| `before-articles`      | 无                          | 在文章列表渲染之前、且数据已加载且列表不为空时插入内容。                                                                                                            |
| `article-cover`        | `article: ArticleResponse` | 传递给内部每个 `ArticleCard` 组件的 `article-cover` 插槽。用于自定义单个文章封面的显示。                                                                            |
| `article-detail`       | `article: ArticleResponse` | 传递给内部每个 `ArticleCard` 组件的 `article-detail` 插槽。用于自定义单个文章详细信息的主体区域，若提供此插槽，会替换 `ArticleCard` 内部的 `article-attr` 和 `article-content` 的默认结构。 |
| `article-attr`         | `article: ArticleResponse` | 传递给内部每个 `ArticleCard` 组件的 `article-attr` 插槽。用于自定义单个文章属性（如发布时间）的显示。                                                                      |
| `article-content`      | `article: ArticleResponse` | 传递给内部每个 `ArticleCard` 组件的 `article-content` 插槽。用于自定义单个文章主要内容（如标题、摘要）的显示。                                                                |
| `after-single-article` | `article: ArticleResponse` | 传递给内部每个 `ArticleCard` 组件的 `after-article` 插槽。用于在单个文章卡片内容渲染完毕后，但在卡片闭合前插入额外内容。                                                            |
| `after-articles`       | 无                          | 在文章列表渲染之后、且数据已加载且列表不为空时插入内容。                                                                                                            |
| `empty-posts`          | 无                          | 当获取的文章列表为空时显示的内容。组件本身不提供默认的空状态显示，建议通过此插槽实现。                                                                                             |
| `after-layout`         | 无                          | 在组件最外层布局元素 (`<div class="blog-preview">`) 内部，但在所有其他内容之后，组件闭合标签之前插入。                                                                     |

## 基本用法示例

```vue

<template>
	<BlogPreview/>
</template>

<script setup lang="ts">
	// BlogPreview 组件通常会自动导入 (Nuxt 3项目特性)
	// import BlogPreview from '~/components/article/BlogPreview.vue';
</script>
```

此示例将使用默认的 PC 和 Mobile 配置渲染最新的文章列表。

## 自定义插槽用法示例

```vue

<template>
	<BlogPreview
			:pc="{ displayStyle: 'overlay', rows: 2 }"
			:mobile="{ displayStyle: 'overlay', rows: 1 }"
	>
		<template #header>
			<div class="my-custom-header text-center py-8 bg-primary/10">
				<h2 class="text-4xl font-bold text-primary">我们的最新洞见</h2>
				<p class="text-lg text-muted-foreground mt-2">探索行业趋势与深度分析</p>
			</div>
		</template>
		
		<template #article-cover="{ article }">
			<div class="aspect-video bg-muted rounded-t-lg overflow-hidden border-b-2 border-primary">
				<img
						v-if="article.cover"
						:src="article.cover"
						:alt="article.title"
						class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
				/>
				<div v-else class="w-full h-full flex items-center justify-center text-sm text-muted-foreground">
					暂无封面
				</div>
			</div>
		</template>
		
		<template #article-content="{ article }">
			<h3 class="text-lg font-semibold text-foreground mb-1 leading-tight group-hover:text-primary transition-colors">
				<NuxtLink :to="resolveLocalePath(`/posts/${article.slug}`)" class="line-clamp-2">
					{{ article.title }}
				</NuxtLink>
			</h3>
			<p class="text-xs text-muted-foreground mb-2">
				自定义摘要: {{ stripMarkdown(article.content).substring(0, 60) }}...
			</p>
		</template>
		
		<template #after-single-article="{ article }">
			<div class="mt-auto pt-2 border-t border-border/50">
				<NuxtLink
						:to="resolveLocalePath(`/posts/${article.slug}`)"
						class="text-xs text-primary hover:underline"
				>
					阅读更多 &rarr;
				</NuxtLink>
			</div>
		</template>
		
		<template #empty-posts>
			<div class="text-center py-12">
				<p class="text-xl text-muted-foreground">暂无最新文章，敬请期待！</p>
			</div>
		</template>
	</BlogPreview>
</template>

<script setup lang="ts">
	// BlogPreview 组件通常会自动导入
	import {resolveLocalePath} from '~/utils/i18n/utils'; // 假设 ArticleCard 内部不传递
	import {stripMarkdown} from '~/utils/stringUtils'; // 假设 ArticleCard 内部不传递
</script>
```

**注意**:

- 上述示例中的CSS类名主要基于Tailwind CSS。实际项目中请根据您的样式系统调整。
- 在自定义插槽（如 `article-content`）中如果需要使用 `resolveLocalePath` 或 `stripMarkdown` 等工具函数，且这些函数未在
  `ArticleCard` 内部通过作用域插槽暴露，则可能需要在父组件（即使用 `BlogPreview` 的组件）中自行导入，如示例所示。
- `BlogPreview` 会将 `article-cover`, `article-detail`, `article-attr`, `article-content`, `after-single-article`
  这些插槽的内容直接传递给其内部使用的 `ArticleCard` 组件对应的插槽。 
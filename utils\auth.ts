import logger from '~/utils/logger'
import {loginRegister<PERSON><PERSON>} from '~/utils/api/loginRegisterApi'
import {localStg} from '~/utils/localStorageService'
import {LocalStorageConstant} from '~/utils/constants/localStorage'
import {resolveLocalePath} from '~/utils/i18n'
import {navigateTo} from '#app'

export async function executeLogout(callback?: () => void): Promise<void> {
    logger.info('Attempting to sign out.')
    const response = await loginRegisterApi.logout()

    if (callback) {
        callback()
    }
    localStg.remove(LocalStorageConstant.USER_INFO)
    localStg.remove(LocalStorageConstant.TOKEN)
    if (response.code === 200) {
        logger.info('Successfully logged out from server.')

        const loginPath = resolveLocalePath('/login')
        navigateTo(loginPath, {replace: true})
    } else {
        logger.error('Server logout failed with code:', {code: response.code, message: response.message})
        useMessage().message.error(response.message)
    }
}

export function executeClearUserData() {
    if (!import.meta.client) {
        return
    }

    localStg.remove(LocalStorageConstant.USER_INFO)
    localStg.remove(LocalStorageConstant.TOKEN)
}
/**
 * i18n国际化工具函数实现
 * 提供获取国际化翻译值的工具函数，适用于各种上下文环境
 */

// 导入必要的Nuxt函数
import {useNuxtApp} from '#app'
// 更新导入路径
import type {I18nTextMap} from '~/types/i18n'
import {projectConfig} from '~/config/projectConfig'

type TranslateParams = Record<string, unknown> | unknown[] | number

/**
 * 获取国际化翻译文本
 * 提供统一的方式获取i18n翻译值，避免重复编写获取逻辑
 * 支持参数传递、复数形式等高级特性
 *
 * @param key 翻译键名
 * @param params 可选参数，支持对象参数、数组参数或数字(用于复数)
 * @param fallback 未找到翻译时的默认值
 * @returns 翻译后的文本
 */
export function tMsg(
    key: string,
    params?: TranslateParams,
    fallback?: string
): string {
    try {
        // 尝试使用 nuxt 运行时配置获取 i18n
        const nuxtApp = useNuxtApp()
        if (nuxtApp && nuxtApp.$i18n) {
            if (params !== undefined) {
                // @ts-expect-error - vue-i18n的类型可能与我们的定义不完全匹配
                return nuxtApp.$i18n.t(key, params)
            }
            return nuxtApp.$i18n.t(key)
        }
    } catch (e) {
        logger.error(`无法获取i18n翻译: ${key}`, e)
    }

    // 如果无法获取翻译，返回默认值或键名本身
    return fallback || key
}

/**
 * 获取当前语言Code
 *
 * @returns 当前语言代码
 */
export function getCurrentLocale(): string {
    try {
        const nuxtApp = useNuxtApp()
        if (nuxtApp && nuxtApp.$i18n) {
            return nuxtApp.$i18n.locale.value
        }
    } catch (e) {
        logger.warn('无法获取当前语言', e)
    }

    return projectConfig.defaultLocale.code
}

/**
 * 获取当前语言 返回的是en-US这种格式
 *
 * @returns 当前语言
 */
export function getCurrentLanguage(): string {
    try {
        const nuxtApp = useNuxtApp()
        if (nuxtApp && nuxtApp.$i18n) {
            return nuxtApp.$i18n.localeProperties.value.language as string
        }
    } catch (e) {
        logger.warn('无法获取当前语言', e)
    }

    return projectConfig.defaultLocale.language
}

/**
 * 检查是否存在指定翻译键
 *
 * @param key 翻译键名
 * @param locale 可选语言代码，默认使用当前语言
 * @returns 是否存在该翻译键
 */
export function hasTranslation(key: string, locale?: string): boolean {
    try {
        const nuxtApp = useNuxtApp()
        if (nuxtApp && nuxtApp.$i18n) {
            const targetLocale = locale || nuxtApp.$i18n.locale.value
            // @ts-expect-error - vue-i18n的类型可能与我们的定义不完全匹配
            return nuxtApp.$i18n.te(key, targetLocale)
        }
    } catch (e) {
        logger.warn(`检查翻译键存在性失败: ${key}`, e)
    }
    return false
}

/**
 * 处理复数翻译的便捷函数
 *
 * @param key 翻译键名
 * @param count 数量
 * @param namedParams 其他命名参数
 * @returns 翻译后的文本
 */
export function tPlural(
    key: string,
    count: number,
    namedParams?: Record<string, unknown>
): string {
    const params = {
        ...(namedParams || {}),
        count
    }
    return tMsg(key, params)
}

// --- Start: Added functions for localized config text ---

// 定义通用的多语言文本对象类型，键为语言代码字符串
// type LocaleTextObject<T = string> = {
//     // 使用 string 作为键，允许任何语言代码，并支持回退
//     [locale: string]: T | undefined
// }

// 从项目配置中读取默认的回退语言
const FALLBACK_LOCALE: string = projectConfig.defaultLocale.code

/**
 * 从配置对象中获取当前语言环境的文本。
 * 如果提供了 params，则会尝试使用 i18n 的 rt (resolve translation) 函数处理占位符。
 * 处理类型安全和回退逻辑。
 *
 * @param textMap 包含i18n文本映射的对象 (I18nTextMap)。
 * @param fallbackText 如果找不到当前语言或默认语言的文本，则返回此值。
 * @param params 可选参数，用于替换文本中的占位符，结构同 tMsg。
 * @returns 本地化后的文本，如果提供了params且处理成功，则包含插值，否则为原始文本或回退文本。
 */
export function getLocalizedConfigText(
    textMap: I18nTextMap | null | undefined,
    fallbackText: string = '',
    params?: TranslateParams
): string {
    if (!textMap) {
        return fallbackText
    }

    const currentLocale = getCurrentLocale()
    let baseText: string | undefined

    // 尝试获取当前语言的文本
    if (currentLocale in textMap && typeof textMap[currentLocale] === 'string') {
        baseText = textMap[currentLocale]
    }

    // 如果当前语言文本不存在或不是字符串，尝试获取回退语言的文本
    if (baseText === undefined && FALLBACK_LOCALE in textMap && typeof textMap[FALLBACK_LOCALE] === 'string') {
        baseText = textMap[FALLBACK_LOCALE]
    }

    // 如果都找不到，尝试获取对象中的第一个字符串类型的值
    if (baseText === undefined) {
        const firstAvailableValue = Object.values(textMap).find(value => typeof value === 'string')
        if (typeof firstAvailableValue === 'string') {
            baseText = firstAvailableValue
        }
    }

    // 如果最终没有找到合适的文本，则使用 fallbackText
    const resolvedText = baseText ?? fallbackText

    // 如果提供了 params，则尝试使用 i18n.rt 进行插值
    if (params !== undefined) {
        try {
            const nuxtApp = useNuxtApp()
            // 尝试直接使用 nuxtApp.$i18n.rt
            if (nuxtApp && nuxtApp.$i18n && typeof nuxtApp.$i18n.rt === 'function') {
                // @ts-expect-error - nuxtApp.$i18n.rt 的类型可能不完全匹配或 TranslateParams 有细微差异，但功能上兼容
                return nuxtApp.$i18n.rt(resolvedText, params)
            }
            if (nuxtApp && nuxtApp.$i18n) {
                // rt 函数不可用，记录警告
                logger.warn(
                    `i18n.rt function not available for processing params in getLocalizedConfigText. Text: '${resolvedText}'`,
                    {providedParams: params}
                )
            }
            // 如果 nuxtApp 或 $i18n 或 rt 不可用，将回退到返回 resolvedText
        } catch (e) {
            // 在处理 params 时发生错误，记录警告并回退
            logger.warn(`Error processing params in getLocalizedConfigText: ${String(e)}`, {
                initialMap: textMap,
                selectedText: resolvedText,
                params
            })
            // 出错时，回退到返回原始解析的文本
            return resolvedText
        }
    }

    // 如果没有 params，或者 params 处理失败，返回原始的 resolvedText
    return resolvedText
}

/**
 * 解析路径的本地化版本。
 * 封装了 nuxtApp.$localePath 的调用，提供了错误处理和回退机制。
 *
 * @param path 要进行本地化的原始路径 (例如 '/about')。
 * @returns 本地化后的路径字符串，如果处理失败则返回原始路径。
 */
export function resolveLocalePath(path: string): string {
    const locale = getCurrentLocale()
    try {
        const nuxtApp = useNuxtApp()
        if (nuxtApp && typeof nuxtApp.$localePath === 'function') {
            // @ts-expect-error - $localePath 的参数类型定义可能与直接传递 string 和可选 string 不完全匹配，但功能上兼容
            return nuxtApp.$localePath(path, locale)
        }
        logger.warn(`nuxtApp.$localePath is not available. Returning original path: ${path}`)
        // 回退到原始路径
        return path
    } catch (e) {
        logger.warn(`Error resolving locale path for '${path}' (locale: ${locale ?? 'current'}): ${String(e)}. Returning original path.`)
        // 回退到原始路径
        return path
    }
}

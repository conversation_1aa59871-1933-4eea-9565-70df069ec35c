/**
 * 平台用户信息
 */
export interface PlatformUserinfo {
    // 用户ID
    userId: string;
    // 显示名称
    displayName: string | null;
    // 昵称
    nickname: string | null
    // 头像URL
    avatar: string | null
    // 是否为平台账号
    platformAccount?: boolean
}

/**
 * 联系人信息
 */
export interface Contact {
    contactId: string;
    count: number;
    lastMessage: string
    lastSendTime: string;
    group: 'all' | 'unpaid' | 'no-order'
    conversationId: string
    msgId: number;
    platformMsgId: string;
    contactDetail: PlatformUserinfo;
    cursorData: any;
    platformId: string;
    onlineStatus: boolean;
    lastMessageMeta: string;
    starred: boolean;
    ownerAccountId: string;
}

//
// 用户原始的 `Message` 接口已重命名为 `ChatMessage`
// 以便更好地将其区分为组件的“视图模型”。
// ChatMessage 继承自 BackendMessage，并添加了客户端特有的字段。
//
export interface ChatMessage extends BackendMessage {
    // 消息类型，直接使用后端的枚举
    type: MessageCategory
    // 消息内容的简单字符串表示 (用于通知、预览等)
    content: string
    // 发送时间 (时间戳，用于客户端排序和显示)
    sendTimeValue: number

    // --- 以下是客户端特有字段 ---

    // 客户端解析后的数据，用于渲染
    clientData?: string | ProductData | EmojiData | UnknownMessage | FileData | InviteOrderMessage | ProductOrderMessage | LocationMessage

    // 上传进度 (0-100)
    uploadProgress?: number
    // 回复的消息
    replyTo?: ChatMessage
    // 客户端消息状态
    status?: 'sending' | 'sent' | 'failed'

    errorResponseMessage?: string,

    errorRetryAction: (message: ChatMessage) => void
}

//
// 后端相关类型，与Java类镜像。
//
/**
 * 后端消息大类枚举
 */
export enum MessageCategory {
    // 文本类型
    TEXT = 'TEXT',
    // 文件类型
    FILE = 'FILE',
    // 邀请下单
    INVITE_ORDER = 'INVITE_ORDER',
    // 订单咨询
    ORDER_CONSULT = 'ORDER_CONSULT',
    // 地理位置
    LOCATION = 'LOCATION',
    // 商品咨询
    PRODUCT_CONSULT = 'PRODUCT_CONSULT',
    UNKNOWN = 'UNKNOWN'
}

export enum PlatformType {
    QIAN_NIU = 'QIAN_NIU',
    PIN_DUO_DUO = 'PIN_DUO_DUO',
    JD = 'JD',
    XIAN_YU = 'XIAN_YU'
}

export const platformNameMapping: Record<PlatformType, { name: string; icon: string | null }> = {
    [PlatformType.QIAN_NIU]: {name: '淘宝', icon: 'text-amber-500'},
    [PlatformType.PIN_DUO_DUO]: {name: '拼多多', icon: 'text-blue-500'},
    [PlatformType.JD]: {name: '京东', icon: 'text-cyan-500'},
    [PlatformType.XIAN_YU]: {name: '闲鱼', icon: 'text-green-500'}
}

/**
 * 后端消息大类枚举
 */
export enum OrderStatus {
    // 等待买家付款
    WAIT_BUYER_PAY = 'WAIT_BUYER_PAY',
    // 买家已付款 (待发货)
    BUYER_PAID = 'BUYER_PAID',
    // 卖家已发货
    SELLER_SHIPPED = 'SELLER_SHIPPED',
    // 交易成功
    TRADE_SUCCESS = 'TRADE_SUCCESS',
    // 交易关闭
    TRADE_CLOSED = 'TRADE_CLOSED',
    // 未完成的订单
    UNFINISHED = 'UNFINISHED',
    // 退款中的订单
    REFUNDING = 'REFUNDING',
    // 定金已付
    DEPOSIT_PAID = 'DEPOSIT_PAID',
    // 异常订单
    ABNORMAL = 'ABNORMAL'
}

/**
 * 商品数据结构
 */
export interface ProductData {
    // 商品Id
    id: number
    title: string
    productUrl: string
    picture: string
    itemList: ProductItem[]
}

export interface ProductItem {
    skuId: number
    skuTitle: string
    originalPrice: number
    purchasePrice: number
    count: number
}

/**
 * 邀请下单消息
 */
export interface InviteOrderMessage extends ProductData {
    product: ProductData
    count: number
}

/**
 * 商品订单相关消息
 */
export interface ProductOrderMessage extends ProductData {
    // 商品数据
    product: ProductData
    // 订单id
    orderId: string
    // 创建日期
    createTime: Date
    // 订单状态
    status: OrderStatus
    // 订单详情地址
    orderDetailUrl: string
    // 订单总价格
    totalPrice: number
    // 订单数量
    count: number
    // 此订单是否真实存在
    orderExistStatus: boolean
}

/**
 * 后端消息内容类型枚举 (对应 MessageTypeEnum.java)
 */
export enum MessageTypeEnum {
    TEXT = 'TEXT',
    EMOJI = 'EMOJI',
    UNKNOWN = 'UNKNOWN',
    FILE = 'FILE',
    INVITE_ORDER = 'INVITE_ORDER',
    ORDER_QUESTION = 'ORDER_QUESTION',
    LOCATION = 'LOCATION',
    PRODUCT_CONSULT = 'PRODUCT_CONSULT'
}

/**
 * 表情数据结构
 */
export interface EmojiData {
    meaning: string
    url: string
    shortCut: string
}

/**
 * 未知类型消息
 */
export interface UnknownMessage {
    type: string
}

/**
 * 位置消息
 */
export interface LocationMessage {
    location: string
}

/**
 * 文件数据结构
 */
export interface FileData {
    fileType: 'IMAGE' | 'VIDEO' | 'AUDIO' | 'BINARY_FILE'
    filename: string
    size: number
    url: string
    thumbnail: string | null
}

/**
 * 表示一条消息气泡中的一个部分。
 * 对于文本消息，一个气泡可以包含多个部分（如文本和表情混合）。
 */
export interface MessageDetail {
    /**
     * 对应后端的 MessageTypeEnum
     */
    type: MessageTypeEnum | string
    /**
     * 该部分的数据负载
     */
    data: string | ProductData | EmojiData | UnknownMessage | FileData | InviteOrderMessage | ProductOrderMessage | LocationMessage

    messageContent: string
}

/**
 * 表示从后端接收到的一个完整的消息气泡
 */
export interface BackendMessage {
    id: number | null
    // 平台消息ID，唯一不为空
    platformMessageId: string
    // 会话ID
    conversationId: string
    // 发送时间 (格式: yyyy-MM-dd HH:mm:ss)
    sendTime: string
    // 发送方
    sender: PlatformUserinfo
    // 接收方
    receiver: PlatformUserinfo
    // 消息大类
    category: MessageCategory
    // 消息内容列表
    list: MessageDetail[]
    // 额外数据
    extData: Record<string, any> | null;
    // 游标数据
    cursorData: any
    platformId: string;
}

//
// 来自后端的的分页结构
//
export interface PageData<T = unknown> {
    // 结果列表
    result: T[]
    // 附加数据
    additionalData?: {
        // 是否有更多数据
        hasMore?: boolean
        // 用于下一次查询的光标
        cursor?: string
    }
}

/**
 * 表情数据结构
 */
export interface EmojiDetail {
    meaning: string
    src: string
    shortCut: string
}

export interface EmojiInfo {
    label: string
    children: EmojiDetail[]
}

/**
 * 右键菜单项接口定义
 */
export interface ContextMenuItem {
    // 菜单项文本
    label: string
    // 菜单项图标 (可选)
    icon?: string
    // 是否为危险操作（红色显示）
    danger?: boolean
    // 是否禁用
    disabled?: boolean
    // 点击后执行的动作
    action: () => void
}

/**
 * 右键菜单配置的键类型
 * 格式: `${PlatformType}_${MessageCategory}_${发送者类型}`
 * 发送者类型: 'SELF' (自己发送) | 'OTHER' (他人发送)
 */
export type ContextMenuKey = `${PlatformType}_${MessageCategory}_${'SELF' | 'OTHER'}`

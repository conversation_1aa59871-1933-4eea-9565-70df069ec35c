import type {I18nExampleConfig} from '@/types/site/i18nExample'

/**
 * 示例配置，包含多语言文本
 */
export const i18nExampleConfig: I18nExampleConfig = {
    pageTitle: {
        en: 'Example Page Title',
        zh: '示例页面标题'
    },
    welcomeMessage: {
        en: 'Welcome to our example configuration!',
        zh: '欢迎来到我们的示例配置！'
    },
    features: [
        {
            id: 'feature-1',
            // 补充示例 icon
            icon: 'lucide:star',
            name: {
                en: 'Amazing Feature One',
                zh: '神奇功能一'
            },
            description: {
                en: 'This feature does something incredible.',
                zh: '此功能可以做一些不可思议的事情。'
            }
        },
        {
            id: 'feature-2',
            // 补充示例 icon
            icon: 'lucide:zap',
            name: {
                en: 'Super Fast Feature Two',
                zh: '超快功能二'
            },
            description: {
                en: 'Experience blazing speed with this feature.',
                zh: '通过此功能体验闪电般的速度。'
            }
        }
    ]
}

export default i18nExampleConfig
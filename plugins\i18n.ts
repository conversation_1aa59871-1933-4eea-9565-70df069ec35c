export default defineNuxtPlugin(nuxtApp => {
    // called right after a new locale has been set
    nuxtApp.hook('i18n:localeSwitched', () => {
        const {localeProperties} = (nuxtApp['$i18n'] as {
      localeProperties: ComputedRef<{
        code: string;
        name: string;
        language: string;
      }>;
    })

        const latestLanguage = localeProperties.value.language
        console.log(`当前最新的语言 ${latestLanguage}`)
    })
})
import type {PageParams} from '~/utils/http'
import type {TestimonialResponse} from '~/types/api'

/**
 * 获取评论列表的请求参数
 * 需要同时满足分页参数和可选的评论过滤参数
 */
export interface TestimonialListParams extends PageParams, Partial<TestimonialResponse> {
    // 这里继承了 PageParams (pageNum, pageSize, sort...) 和 Partial<TestimonialResponse>
    // 因此，可以直接在 TestimonialListParams 类型的对象上传入 pageNum, pageSize, sort
    // 以及可选的过滤字段，如 name, account 等 (如果 API 支持)
}

// 如果有创建或更新操作，也在这里定义相应的 Params 类型
// export interface TestimonialCreateParams { ... }
// export interface TestimonialUpdateParams { ... }
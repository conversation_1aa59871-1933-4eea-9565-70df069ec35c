<script setup lang="ts">
import {type Component, computed, defineAsyncComponent} from 'vue'
import type {ChatMessage, MessageCategory} from '@/types/chat'

// 定义组件的props
const props = defineProps<{
    // 要渲染的消息对象
    message: ChatMessage
}>()

// 异步组件映射，用于根据消息类型动态加载不同的消息组件
const messageComponents: Record<MessageCategory, Component> = {
    // 文本消息组件
    TEXT: defineAsyncComponent(() => import('./TextMessage.vue')),
    // 商品消息组件
    PRODUCT_CONSULT: defineAsyncComponent(() => import('./ProductMessage.vue')),
    // 文件消息组件
    FILE: defineAsyncComponent(() => import('./FileMessage.vue')),
    UNKNOWN: defineAsyncComponent(() => import('./UnknownMessage.vue')),
    INVITE_ORDER: defineAsyncComponent(() => import('./InviteOrderMessage.vue')),
    ORDER_CONSULT: defineAsyncComponent(() => import('./ProductOrderMessage.vue')),
    LOCATION: defineAsyncComponent(() => import('./LocationMessage.vue'))
}

// 计算属性，根据消息类型返回对应的组件
const componentType = computed(() => {
    // @ts-expect-error - 如果找不到对应类型的组件，则默认使用文本组件
    return messageComponents[props.message.type] || messageComponents.text
})

const debugMsg = (msg) => {
    // console.log('debug', msg)
}
</script>

<template>
  <div class="text-sm">
    <!-- 主消息内容 -->
    <component :is="messageComponents[props.message.type]" :message="props.message" @click="debugMsg(message)"/>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import Input from '@/components/ui/input/Input.vue'
import Label from '@/components/ui/label/Label.vue'
import Button from '@/components/ui/button/Button.vue'

const passwordForm = ref({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
})

// 更改密码
const changePassword = () => {
    // 验证密码
    if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
        alert('新密码和确认密码不一致')
        return
    }
    // 实现密码更新逻辑
    alert('密码已更新')
    passwordForm.value = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    }
}
</script>

<template>
  <div>
    <h2 class="text-2xl font-bold mb-6">密码设置</h2>
    <p class="text-muted-foreground mb-8">修改您的登录密码</p>

    <form @submit.prevent="changePassword" class="space-y-4">
      <div class="space-y-2">
        <Label for="current-password">当前密码</Label>
        <Input id="current-password" type="password" v-model="passwordForm.currentPassword"
               placeholder="输入当前密码"/>
      </div>
      <div class="space-y-2">
        <Label for="new-password">新密码</Label>
        <Input id="new-password" type="password" v-model="passwordForm.newPassword" placeholder="输入新密码"/>
      </div>
      <div class="space-y-2">
        <Label for="confirm-password">确认新密码</Label>
        <Input id="confirm-password" type="password" v-model="passwordForm.confirmPassword"
               placeholder="再次输入新密码"/>
      </div>
      <Button type="submit">更改密码</Button>
    </form>
  </div>
</template>
import {createCrudApi} from '~/utils/api'
import type {UserResponse} from '~/types/api/response/admin/UserResponse'

/**
 * <AUTHOR> <br/>
 * @description 用户信息 <br/>
 * @date 2025-05-16 18:24:14
 */
function createUserApi() {
    const baseUrl = '/user'

    const userCrudApi = createCrudApi<UserResponse>(baseUrl)
    return {
        ...userCrudApi
    }
}

// 导出用户API
export const userApi = createUserApi()
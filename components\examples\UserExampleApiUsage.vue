<template>
  <div>
    <!-- 根据需求，使用接口的数据 -->
  </div>
</template>

<script setup lang="ts">
import {userExampleApi} from '~/utils/api/userExampleApi';
import {logger} from '~/utils/logger';

async function apiCallExample() {
    
    // 日志记录
    logger.info('xxx', {context data})

    // 根据需要传递参数 不需要try catch(底层封装已做)
    const response = await userExampleApi.get(xx)
    
    if (response.code === 200) {
        // 根据需求执行成功响应
        
        // .............
    } else {
        // 执行失败响应 如日志记录等
        // ..............
    }
}

// 接口调用例子 其他接口也是类似
apiCallExample()

</script>
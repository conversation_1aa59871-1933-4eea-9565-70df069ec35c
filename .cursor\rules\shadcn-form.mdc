---
description:
globs:
alwaysApply: false
---

Shadcn的Form表单官方使用例子

```
<script setup lang="ts">
import {vAutoAnimate} from '@formkit/auto-animate/vue'
import {toTypedSchema} from '@vee-validate/zod'
import {useForm} from 'vee-validate'
import * as z from 'zod'

import {Button} from '@/components/ui/button'
import {FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage} from '@/components/ui/form'
import {Input} from '@/components/ui/input'

const formSchema = toTypedSchema(z.object({
    username: z.string().min(2).max(50)
}))

const {isFieldDirty, handleSubmit} = useForm({
    validationSchema: formSchema
})

const onSubmit = handleSubmit((values) => {

})
</script>

<template>
  <form class="w-2/3 space-y-6" @submit="onSubmit">
    <FormField v-slot="{ componentField }" name="username" :validate-on-blur="!isFieldDirty">
      <FormItem v-auto-animate>
        <FormLabel>Username</FormLabel>
        <FormControl>
          <Input type="text" placeholder="shadcn" v-bind="componentField" />
        </FormControl>
        <FormDescription>
          This is your public display name.
        </FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>
    <Button type="submit">
      Submit
    </Button>
  </form>
</template>
```

import type {I18nTextMap} from '~/types/i18n'

/**
 * 网站配置信息
 */
export interface SiteConfig {
    /**
     * 网站 Logo 图片的 URL 地址
     */
    logo?: string
    /**
     * 网站图标的颜色 (例如 '#RRGGBB')
     */
    iconColor?: string
    /**
     * 网站标题 (国际化)
     */
    title: I18nTextMap
    /**
     * 网站描述 (国际化)
     */
    description?: I18nTextMap
    /**
     * 自动滚动速度配置
     */
    autoScrollSpeed?: {
        /**
         * PC端的滚动速度
         */
        pc: number
        /**
         * 移动端的滚动速度
         */
        mobile: number
    }
    // keywords?: I18nTextMap // 如果需要关键词，可以取消注释
}
<script setup lang="ts">
import {ref} from 'vue'
import Label from '@/components/ui/label/Label.vue'
import Button from '@/components/ui/button/Button.vue'
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select'
import {Switch} from '@/components/ui/switch'

// 一般设置选项
const settings = ref({
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    darkMode: true,
    twoFactorAuth: false
})

// 语言选项
const languages = [
    {value: 'zh-CN', label: '简体中文'},
    {value: 'en-US', label: '英语 (美国)'},
    {value: 'ja-JP', label: '日语'},
    {value: 'ko-KR', label: '韩语'}
]

// 时区选项
const timezones = [
    {value: 'Asia/Shanghai', label: '(GMT+8:00) 北京, 上海'},
    {value: 'Asia/Tokyo', label: '(GMT+9:00) 东京'},
    {value: 'America/New_York', label: '(GMT-5:00) 纽约'},
    {value: 'Europe/London', label: '(GMT+0:00) 伦敦'}
]

// 保存设置
const saveSettings = () => {
    alert('一般设置已更新')
}
</script>

<template>
  <div>
    <h2 class="text-2xl font-bold mb-6">一般设置</h2>
    <p class="text-muted-foreground mb-8">管理您的账户基本设置</p>

    <form @submit.prevent="saveSettings" class="space-y-6">
      <!-- 语言设置 -->
      <div class="grid gap-2">
        <Label for="language">语言</Label>
        <Select v-model="settings.language">
          <SelectTrigger id="language">
            <SelectValue placeholder="选择语言"/>
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="lang in languages" :key="lang.value" :value="lang.value">
              {{ lang.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 时区设置 -->
      <div class="grid gap-2">
        <Label for="timezone">时区</Label>
        <Select v-model="settings.timezone">
          <SelectTrigger id="timezone">
            <SelectValue placeholder="选择时区"/>
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="tz in timezones" :key="tz.value" :value="tz.value">
              {{ tz.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 外观设置 -->
      <div>
        <h3 class="text-lg font-semibold mb-4">外观</h3>
        <div class="flex items-center space-x-2">
          <Switch id="dark-mode" v-model="settings.darkMode"/>
          <Label for="dark-mode">启用暗黑模式</Label>
        </div>
      </div>

      <!-- 安全设置 -->
      <div>
        <h3 class="text-lg font-semibold mb-4">安全</h3>
        <div class="flex items-center space-x-2">
          <Switch id="two-factor-auth" v-model="settings.twoFactorAuth"/>
          <Label for="two-factor-auth">启用双重验证</Label>
        </div>
        <p class="text-sm text-muted-foreground mt-1 ml-7">启用后，登录时将需要额外的验证步骤</p>
      </div>

      <Button type="submit" class="mt-8">保存设置</Button>
    </form>
  </div>
</template>
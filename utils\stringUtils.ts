/**
 * 移除字符串中的常见Markdown标记
 * @param markdownText 包含Markdown的文本
 * @returns 清理后的纯文本
 */
export const stripMarkdown = (markdownText: string): string => {
    if (!markdownText) {
        return ''
    }

    let text = markdownText

    // 移除或替换Markdown标记
    // 标题 (移除 #)
    text = text.replace(/^#+\s*/gm, '')
    // 加粗/斜体 (移除 * 和 _)
    text = text.replace(/(\*\*|__|\*|_)/g, '')
    // 删除线 (移除 ~~)
    text = text.replace(/~~/g, '')
    // 链接 ([文字](链接) -> 文字)
    text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // 图片 (![描述](链接) -> 描述 或 空)
    text = text.replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1')
    // 行内代码 (`code` -> code)
    text = text.replace(/`([^`]+)`/g, '$1')
    // 代码块 (移除 ```)
    text = text.replace(/```[\s\S]*?```/g, '')
    // 列表标记 (移除 *, -, +, 1.)
    text = text.replace(/^(\*|-|\+)\s+/gm, '')
    text = text.replace(/^\d+\.\s+/gm, '')
    // 引用块 (移除 >)
    text = text.replace(/^>\s*/gm, '')
    // 水平线 (移除 ---, ***, ___)
    text = text.replace(/^(---|\*\*\*|___)\s*$/gm, '')
    // 移除HTML标签以防万一
    text = text.replace(/<[^>]+>/g, '')
    // 移除多余的换行符合并空格
    // 最多保留一个换行
    text = text.replace(/\n{2,}/g, '\n')
    // 合并多个空格为一个
    text = text.replace(/\s{2,}/g, ' ')

    return text.trim()
}
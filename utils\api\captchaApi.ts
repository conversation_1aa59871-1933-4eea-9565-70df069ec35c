import type {ApiResponse, RequestOptions} from '@/utils/http/types'
import {logger} from '@/utils/logger'
import type {CaptchaParams} from '@/types/api/params/CaptchaParams'
import {Post} from '~/utils/http'
import {applyTicket} from '~/utils/securityUtils'

/**
 * 创建验证码相关API
 */
function createCaptchaApi() {
    const API_PREFIX = '/app'

    /**
     * 通过手机号发送验证码
     * @param params 包含手机号 { phone: string }
     */
    function sendSmsCaptcha(params: CaptchaParams): Promise<ApiResponse<null>> {
        logger.info('API 调用：sendSmsCaptcha', params)
        return Post<null>(`${API_PREFIX}/sendSms`, params)
    }

    /**
     * 通过用户名发送短信验证码 (后端会根据用户名查找手机号)
     * @param params 包含用户名 { username: string }
     */
    function sendSmsCaptchaByAccount(params: CaptchaParams): Promise<ApiResponse<null>> {
        logger.info('API 调用：sendSmsCaptchaByAccount', params)
        return Post<null>(`${API_PREFIX}/sendCaptchaByAccount`, params)
    }

    /**
     * 验证手机验证码
     * @param params 包含手机号和验证码 { phone: string, captcha: string }
     */
    function checkPhoneCaptcha(params: CaptchaParams): Promise<ApiResponse<null>> {
        logger.info('API 调用：checkPhoneCaptcha', params)
        return Post<null>(`${API_PREFIX}/checkPhoneCaptcha`, params)
    }

    /**
     * 通过邮箱发送验证码
     * @param params 包含邮箱地址 { email: string }
     */
    function sendEmailCaptcha(params: CaptchaParams): Promise<ApiResponse<null>> {
        logger.info('API 调用：sendEmailCaptcha', params)
        const option: RequestOptions = {}
        return applyTicket('EMAIL', params.email, option).then(() => {
            return Post<null>(`${API_PREFIX}/sendEmailSms`, params, option)
        })
    }

    /**
     * 通过用户名发送邮件验证码 (后端会根据用户名查找邮箱)
     * @param params 包含用户名 { username: string }
     */
    function sendEmailCaptchaByAccount(params: CaptchaParams): Promise<ApiResponse<null>> {
        logger.info('API 调用：sendEmailCaptchaByAccount', params)
        return Post<null>(`${API_PREFIX}/sendEmailCaptchaByAccount`, params)
    }

    /**
     * 验证邮箱验证码
     * @param params 包含邮箱地址和验证码 { email: string, captcha: string }
     */
    function checkEmailCaptcha(params: CaptchaParams): Promise<ApiResponse<null>> {
        logger.info('API 调用：checkEmailCaptcha', params)
        return Post<null>(`${API_PREFIX}/checkEmailCaptcha`, params)
    }

    return {
        sendSmsCaptcha,
        sendSmsCaptchaByAccount,
        checkPhoneCaptcha,
        sendEmailCaptcha,
        sendEmailCaptchaByAccount,
        checkEmailCaptcha
    }
}

// 导出验证码API
export const captchaApi = createCaptchaApi()

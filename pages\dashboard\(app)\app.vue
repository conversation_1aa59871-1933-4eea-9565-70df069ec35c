<script setup lang="ts">
import Newsletter from '~/components/market/newsletter/Newsletter.vue'
import FaqTwo from '~/components/market/faqs/FaqTwo.vue'
import BlogPreview from '~/components/article/BlogPreview.vue'
import LogoCloudTwo from '~/components/market/logo-cloud/LogoCloudTwo.vue'
import LogoCloudOne from '~/components/market/logo-cloud/LogoCloudOne.vue'
import Hero from '~/components/market/hero/Hero.vue'
import Testimonial from '~/components/market/testimonials/Testimonial.vue'
import BentoGrid from '~/components/market/bento/BentoGrid.vue'
import Footer from '~/components/market/footer/Footer.vue'
import Team from '~/components/market/team/Team.vue'
import Pricing from '~/components/market/price/Pricing.vue'
import FaqOne from '~/components/market/faqs/FaqOne.vue'
</script>

<template>
  <div>
    <Hero/>

    <Pricing/>

    <BentoGrid mobile-layout="overlay"/>

    <FaqOne :expand-all="false" content-position="left"/>
    <FaqTwo/>

    <LogoCloudOne mobile-display-mode="scroll"/>

    <LogoCloudTwo mobile-display-mode="scroll">
      <template #action>
        <div class="mt-10 flex items-center gap-x-6">
          <NuxtLink to="/">
            <Button size="lg">
              创建账户
            </Button>
          </NuxtLink>
          <NuxtLink to="/">
            <Button variant="link" class="px-0 text-foreground">
              联系我们
              <Icon name="lucide:arrow-right" class="ml-2 h-4 w-4"/>
            </Button>
          </NuxtLink>
        </div>
      </template>
    </LogoCloudTwo>

    <Team layout="vertical"
          avatar-shape="circle"
          :show-social-icons="true"
          member-info-layout="horizontal"
          :items-per-row-sm="4"
          show-detailed-description/>
    <Newsletter layout="vertical" content-align="center"/>

    <!--新增 TeamOne 示例 -->

    <Testimonial :pc="{ displayStyle: 'default', rows: 3 }" :mobile="{ displayStyle: 'overlay', rows: 4 }"/>
    <BlogPreview :pc="{ displayStyle: 'default' }" :mobile="{ displayStyle: 'overlay', rows: 2 }"/>
    <Footer :show-site-details="true"
            site-details-layout="left"
            :show-social-links-in-site-details="true"
            :show-social-links-in-copyright="true"/>

  </div>
</template>

<style scoped>

</style>
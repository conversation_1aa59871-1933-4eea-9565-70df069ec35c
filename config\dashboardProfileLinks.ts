import type {NavLinksConfig} from '~/types/site/navLinks'

// 导航链接配置
const dashboardProfileLinks: NavLinksConfig = [
    {
        href: '/dashboard/profile',
        icon: 'material-symbols:person-2-rounded',
        title: {
            en: 'General',
            zh: '基本信息'
        }
    },
    {
        href: '/dashboard/profile/billing',
        icon: 'material-symbols:credit-card-outline',
        title: {
            en: 'Billing',
            zh: '账单'
        }
    },
    {
        href: '/dashboard/profile/editProfile',
        icon: 'material-symbols:manage-accounts',
        title: {
            en: 'Edit Profile',
            zh: '编辑'
        }
    },
    {
        href: '/dashboard/profile/emailNotifications',
        icon: 'material-symbols:notification-sound',
        title: {
            en: 'Notifications',
            zh: '通知'
        }
    },
    {
        href: '/dashboard/profile/password',
        icon: 'material-symbols:security',
        title: {
            en: 'Password',
            zh: '密码'
        }
    },
    {
        href: '/dashboard/profile/socialProfiles',
        icon: 'material-symbols:plug-connect',
        title: {
            en: 'Socials',
            zh: '社交'
        }
    }
]

export default dashboardProfileLinks
<script setup lang="ts">
// 定义props
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    message: {
        type: String,
        required: true
    },
    visible: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: 'default',
        validator: (value: string) => {
            return ['default', 'success', 'warning', 'info', 'error'].includes(value)
        }
    },
    confirmText: {
        type: String,
        default: ''
    },
    cancelText: {
        type: String,
        default: ''
    },
    dangerouslyUseHTMLString: {
        type: Boolean,
        default: false
    }
})

// 定义事件
const emit = defineEmits(['confirm', 'cancel'])

// 本地状态
const isOpen = ref(props.visible)

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
    isOpen.value = newVal
})

// 默认文本
const defaultTexts = {
    titles: {
        default: '提示',
        success: '成功',
        warning: '警告',
        info: '信息',
        error: '错误'
    },
    buttons: {
        confirm: '确定',
        cancel: '取消'
    }
}

// 计算最终显示的标题和按钮文本
const displayTitle = computed(() => {
    if (props.title) {
        return props.title
    }
    return defaultTexts.titles[props.type as keyof typeof defaultTexts.titles] || defaultTexts.titles.default
})

const displayConfirmText = computed(() => {
    return props.confirmText || defaultTexts.buttons.confirm
})

const displayCancelText = computed(() => {
    return props.cancelText || defaultTexts.buttons.cancel
})

// 计算类型对应的图标和颜色
const typeInfo = computed(() => {
    const types = {
        default: {
            icon: null,
            buttonClass: 'bg-primary hover:bg-primary/90',
            iconColor: 'text-primary'
        },
        success: {
            icon: '✓',
            buttonClass: 'bg-green-600 hover:bg-green-700',
            iconColor: 'text-green-600'
        },
        warning: {
            icon: '⚠',
            buttonClass: 'bg-amber-500 hover:bg-amber-600',
            iconColor: 'text-amber-500'
        },
        info: {
            icon: 'ℹ',
            buttonClass: 'bg-blue-600 hover:bg-blue-700',
            iconColor: 'text-blue-600'
        },
        error: {
            icon: '✗',
            buttonClass: 'bg-destructive hover:bg-destructive/90',
            iconColor: 'text-destructive'
        }
    }
    return types[props.type as keyof typeof types] || types.default
})

// 操作方法
const handleConfirm = () => {
    emit('confirm')
}

const handleCancel = () => {
    emit('cancel')
}
</script>

<template>
  <AlertDialog :open="isOpen" @update:open="isOpen = $event">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle class="flex items-center">
          <span v-if="typeInfo.icon" class="mr-2 text-xl" :class="typeInfo.iconColor">{{ typeInfo.icon }}</span>
          {{ displayTitle }}
        </AlertDialogTitle>
        <AlertDialogDescription v-if="dangerouslyUseHTMLString" v-html="message"/>
        <AlertDialogDescription v-else>{{ message }}</AlertDialogDescription>
      </AlertDialogHeader>

      <AlertDialogFooter>
        <Button variant="outline" @click="handleCancel">{{ displayCancelText }}</Button>
        <Button :class="typeInfo.buttonClass" @click="handleConfirm">{{ displayConfirmText }}</Button>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>
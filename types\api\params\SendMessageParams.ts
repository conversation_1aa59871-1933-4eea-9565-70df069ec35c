import type {MessageCategory, MessageDetail, PlatformUserinfo} from '~/types/chat'

/**
 * 发送消息的请求参数
 */
export interface SendMessageParams {
    platformId: string
    data: {
        // 会话ID
        conversationId: string;
        // 消息类型
        category: MessageCategory;

        openReply: boolean | null

        list: MessageDetail[],
        sender: PlatformUserinfo,
        receiver: PlatformUserinfo,
        extData: Record<string, any> | null
        cursorData: any | null
    }
    platformUserinfo: PlatformUserinfo
}

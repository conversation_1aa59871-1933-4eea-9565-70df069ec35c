# Newsletter 组件使用文档

`Newsletter` 组件用于创建一个通讯订阅模块，允许用户输入邮箱地址进行订阅。它支持不同的布局、内容对齐方式，并可以通过配置文件和插槽进行高度定制。

## Props (属性)

以下是 `Newsletter` 组件可接受的 props 列表：

| Prop 名称             | 类型                                 | 描述                                                    | 可选值                            | 默认值            |
|:--------------------|:-----------------------------------|:------------------------------------------------------|:-------------------------------|:---------------|
| `layout`            | `'horizontal'` \| `'vertical'`     | 控制整个组件的整体布局：`horizontal` 为内容和表单左右结构，`vertical` 为上下结构。 | `'horizontal'`, `'vertical'`   | `'horizontal'` |
| `contentAlign`      | `'start'` \| `'center'` \| `'end'` | 控制标题、描述文本的对齐方式。                                       | `'start'`, `'center'`, `'end'` | `'start'`      |
| `showPrivacyPolicy` | `boolean`                          | 是否显示隐私政策声明文本。                                         | `true`, `false`                | `true`         |
| `showTitle`         | `boolean`                          | 是否显示组件的标题。                                            | `true`, `false`                | `true`         |
| `showDescription`   | `boolean`                          | 是否显示组件的描述文本。                                          | `true`, `false`                | `true`         |

## 数据配置

`Newsletter` 组件的默认文本内容（如标题、描述、输入框占位符、按钮文本和隐私声明）主要由 `/config/newsletterConfig.ts` (或相应的
`.json` 文件) 驱动。

在该配置文件中，通常包含以下字段，且多为 `I18nTextMap` 类型，以支持国际化：

- `titleHtml`: 组件的默认主标题，支持 HTML 内容。
- `descriptionText`: 组件的默认描述文本。
- `emailPlaceholder`: 邮箱输入框的占位提示文本。
- `subscribeButtonText`: 订阅按钮的默认文本。
- `subscribingButtonText`: 正在订阅时按钮显示的文本。
- `privacyNoticeHtml`: 隐私政策声明文本，支持 HTML。此文本模板可以包含 `{privacyPolicyUrl}` 占位符，组件内部会使用
  `resolveLocalePath('/')` (或其他目标路径) 替换它。

组件内部使用 `getLocalizedConfigText` 工具函数，根据当前的 i18n 语言环境自动加载并显示相应的文本。

## 插槽 (Slots)

`Newsletter` 组件提供了以下插槽以实现灵活的定制：

- **`before_layout`**: 在组件主布局容器 (`<div class="w-full ...">`) 内部，但在主要内容网格 (`<div :class="[...]">`)
  之前插入内容。无作用域数据。

- **`title`**: 用于自定义或替换默认的标题区域 (`<h2>`)。如果使用此插槽，`showTitle` prop 的效果将被覆盖。无作用域数据。

- **`description`**: 用于自定义或替换默认的描述文本区域 (`<p>`)。如果使用此插槽，`showDescription` prop 的效果将被覆盖。无作用域数据。

- **`form`**: 用于自定义或替换整个表单部分，包括邮箱输入框 (`<Input>`) 和订阅按钮 (`<Button>`)。无作用域数据。

- **`privacy-notice`**: 用于自定义或替换默认的隐私政策声明文本区域 (`<p>`)。如果使用此插槽，`showPrivacyPolicy` prop
  的效果将被覆盖。无作用域数据。

- **`after_layout`**: 在组件主布局容器 (`<div class="w-full ...">`) 内部，但在主要内容网格 (`<div :class="[...]">`)
  之后插入内容。无作用域数据。

## 基本用法示例

```vue
<template>
  <div class="container mx-auto py-12">
    <Newsletter
      layout="vertical"
      content-align="center"
      :show-privacy-policy="true"
    />
  </div>
</template>

<script setup lang="ts">
// Newsletter 组件通常会自动导入 (Nuxt 3项目特性)
// import Newsletter from '~/components/market/newsletter/Newsletter.vue';
</script>
```

## 使用自定义插槽示例

```vue
<template>
  <div class="container mx-auto py-12 bg-slate-100 dark:bg-slate-800 rounded-xl">
    <Newsletter
      layout="horizontal"
      content-align="start"
    >
      <template #before_layout>
        <div class="p-4 mb-6 bg-blue-50 dark:bg-blue-900 rounded-md text-center">
          <p class="text-sm text-blue-700 dark:text-blue-300">✨ 独家内容即将呈现 ✨</p>
        </div>
      </template>

      <template #title>
        <h2 class="text-4xl font-bold tracking-tight text-blue-600 dark:text-blue-400 mb-3">
          订阅我们的独家快讯!
        </h2>
      </template>

      <template #description>
        <p class="text-gray-700 dark:text-gray-300 text-lg">
          获取最新的产品更新、特别优惠和行业洞察，直接发送到您的邮箱。绝无垃圾邮件。
        </p>
      </template>

      <template #form>
        <div class="flex w-full max-w-lg items-center gap-x-3 mt-6">
          <Input
            id="custom-email-input"
            v-model="customEmail"
            type="email"
            placeholder="请输入您的邮箱地址"
            aria-label="自定义邮箱输入框"
            class="flex-1 py-3 px-4 text-base"
            :disabled="isSubmittingCustom"
          />
          <Button
            type="submit"
            class="text-base px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white"
            :disabled="isSubmittingCustom"
            @click="customSubscribe"
          >
            <Icon v-if="!isSubmittingCustom" name="heroicons:paper-airplane" class="h-5 w-5 mr-2"/>
            <Icon v-if="isSubmittingCustom" name="svg-spinners:180-ring-with-bg" class="h-5 w-5 mr-2"/>
            {{ isSubmittingCustom ? '处理中...' : '立即订阅' }}
          </Button>
        </div>
      </template>

      <template #privacy-notice>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-5">
          点击"立即订阅"，即表示您同意我们的
          <NuxtLink :to="resolveLocalePath('/privacy-policy')" class="text-blue-600 hover:underline">
            隐私政策
          </NuxtLink>
          和
          <NuxtLink :to="resolveLocalePath('/terms-of-service')" class="text-blue-600 hover:underline">
            服务条款
          </NuxtLink>。
        </p>
      </template>

      <template #after_layout>
        <div class="mt-8 pt-6 border-t border-gray-300 dark:border-gray-700 text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            已经是订阅者了？ <NuxtLink :to="resolveLocalePath('/login')" class="text-blue-600 hover:underline">登录</NuxtLink> 查看您的偏好。
          </p>
        </div>
      </template>
    </Newsletter>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Input } from '@/components/ui/input'; // 假设 Input 和 Button 从这里导入
import { Button } from '@/components/ui/button';
import { Icon } from '#components'; // Nuxt Icon
import { resolveLocalePath, tMsg } from '@/utils/i18n'; // 假设的 i18n 工具
import { message } from '@/composables/useMessage'; // 假设的消息提示

// Newsletter 组件通常会自动导入 (Nuxt 3项目特性)
// import Newsletter from '~/components/market/newsletter/Newsletter.vue';

const customEmail = ref('');
const isSubmittingCustom = ref(false);

const customSubscribe = async () => {
  if (!customEmail.value.includes('@')) { // 简易校验
    message.error('请输入有效的邮箱地址');
    return;
  }
  isSubmittingCustom.value = true;
  // 模拟 API 调用
  await new Promise(resolve => setTimeout(resolve, 1500));
  message.success(`感谢订阅，${customEmail.value}！`);
  customEmail.value = '';
  isSubmittingCustom.value = false;
};
</script>
```

**注意**: 上述插槽示例中的CSS类名主要基于Tailwind CSS。实际项目中请根据您的样式系统和具体组件导入路径进行调整。示例中的
`customSubscribe`
逻辑为前端模拟，实际应用中应替换为真实的API调用逻辑。 
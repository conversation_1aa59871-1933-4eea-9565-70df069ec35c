<script setup lang="ts">
import {supportForm} from '@/composables/useMessage'
import SupportForm from '@/components/common/SupportForm.vue'

// 提供 v-model 绑定，允许外部控制表单显示状态
defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    // 允许外部控制 requestId 是否可编辑
    requestIdEditable: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue'])

// 监听表单状态变化，更新外部 v-model
const updateFormVisibility = (value: boolean) => {
    emit('update:modelValue', value)
}
</script>

<template>
  <!--
    使用两种方式控制表单显示：
    1. 通过 v-model 从外部控制
    2. 使用从 useMessage 导出的 supportForm 响应式对象
  -->
  <SupportForm
    :is-open="modelValue || supportForm.isOpen.value"
    :type="supportForm.type.value"
    :enable-request-id="!!supportForm.requestId.value"
    :request-id-value="supportForm.requestId.value"
    :request-id-editable="!!supportForm.requestId.value ? false : requestIdEditable"
    @update:is-open="(value) => {
      updateFormVisibility(value);
      supportForm.isOpen.value = value;
    }"
  />
</template>
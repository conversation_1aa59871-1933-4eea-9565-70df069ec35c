快速开始
跟随本章内容快速搭建一个最基本的聊天框吧
创建聊天框
1.定义 HTML 结构
<!-- 创建目标载体容器 -->
<div id="chatElm"></div>
2.引入 CSS 定义样式
<!-- 引入 css -->
<link href="./lib/ChatArea.css" rel="stylesheet">

<!-- 你可以修改插件默认提供样式 -->
<style>
    /** 聊天框样式 **/
    #chatElm {
        border: 1px solid #ccc;
        background: #fff;
    }

    /** @标签在聊天框内的样式 **/
    .at-user {
        color: #269aff;
    }

    /** 聊天框输入提示语样式 **/
    .chat-placeholder-wrap {
        color: #ccc;
    }
</style>
3.引入 JS 创建聊天框
<!-- 引入 js -->
<script src="./lib/ChatArea.js"></script>

<!-- 实例chat对象 -->
<script>
    var chat = new window.ChatArea({
        elm: document.getElementById('chatElm'),
        placeholder: '请输入',
        userList: [
            { id: '1', name: 'JianFv' },
            { id: '2', name: '松松' }
        ]
    })

    // 绑定键盘发送事件
    chat.addEventListener('enterSend', () => {
        // 获取html
        const htmlMsg = chat.getHtml({
            identifyLink: true
        })
        // 获取纯文本
        const textMsg = chat.getText({
            imgToText: true
        })
        // 获取聊天框中@人员
        const callUserList = chat.getCallUserList()
    })
</script>
这样就创建出了一个最基本的聊天框, 您可以随意敲击内容然后回车 观察控制台的打印信息
﻿

请输入
应用于Vue2
在vue2中使用参考示例
<template>
  <div class="demo-wrap">
    <!-- 创建目标载体容器 -->
    <div ref="chatElm"
         class="chat-elm"></div>
  </div>
</template>
<script>
  import ChatArea from 'chatarea'
  import 'chatarea/lib/ChatArea.css'
  export default {
    name: 'demo',
    data () {
      return {
        chat: null
      }
    },
    mounted () {
      this.initChat()
    },
    methods: {
      initChat () {
        // 实例chat对象
        this.chat = new ChatArea({
          elm: this.$refs.chatElm,
          placeholder: '请输入',
          userList: [
            { id: '1', name: 'JianFv' },
            { id: '2', name: '松松' }
          ]
        })
        // 绑定键盘发送事件
        this.chat.addEventListener('enterSend', this.sendMsg)
      },
      // 发送消息
      sendMsg () {
        if (!this.chat) return
        // 获取html
        const htmlMsg = this.chat.getHtml({
            identifyLink: true
        })
        // 获取纯文本
        const textMsg = this.chat.getText({
            imgToText: true
        })
        // 获取聊天框中@人员
        const callUserList = this.chat.getCallUserList()
      }
    },
    beforeDestroy () {
      // 释放实例
      if (this.chat) {
         this.chat.dispose()
         this.chat = null
      }
    }
  }
</script>
<style scoped lang="less">
  .demo-wrap {
    /** 聊天框样式 **/
    .chat-elm {
      border: 1px solid #ccc;
      background: #fff;

      /** @标签在聊天框内的样式 **/
      :deep(.at-user) {
        color: #269aff;
      }

      /** 聊天框输入提示语样式 **/
      :deep(.chat-placeholder-wrap) {
        color: #ccc;
      }
    }

}
</style>
在Vue中使用时，可别忘了在组件销毁时释放实例对象哦
应用于Vue3
在vue3中使用参考示例
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import ChatArea from 'chatarea'
import 'chatarea/lib/ChatArea.css'

let chat
const chatElm = ref()
const initChat = () => {
  // 实例chat对象
  chat = new ChatArea({
    elm: chatElm.value,
    placeholder: '请输入',
    userList: [
      { id: '1', name: 'JianFv' },
      { id: '2', name: '松松' }
    ]
  })
  // 4.5.2+版本绑定键盘发送事件
  chat.addEventListener('enterSend', sendMsg)
}

// 发送消息
const sendMsg = () => {
  if (!chat) return
  // 获取html
  const htmlMsg = chat.getHtml({
    identifyLink: true
  })
  // 获取纯文本
  const textMsg = chat.getText({
    imgToText: true
  })
  // 获取聊天框中@人员
  const callUserList = chat.getCallUserList()
}

onMounted(() => {
  initChat()
})

onBeforeUnmount(() => {
  // 释放实例
  if (chat) {
     chat.dispose()
     chat = null
  }
})
</script>
<template>
  <div class="demo-wrap">
    <!-- 创建目标载体容器 -->
    <div ref="chatElm"
         class="chat-elm"></div>
  </div>
</template>
<style scoped lang="less">
  .demo-wrap {
    /** 聊天框样式 **/
    .chat-elm {
      border: 1px solid #ccc;
      background: #fff;

      /** @标签在聊天框内的样式 **/
      :deep(.at-user) {
        color: #269aff;
      }

      /** 聊天框输入提示语样式 **/
      :deep(.chat-placeholder-wrap) {
        color: #ccc;
      }
    }

}
</style>

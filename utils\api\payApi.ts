import {apiLogger} from '~/utils/logger'
import type {ProductShowParams} from '~/types/api/params/ProductShowParams'
import type {ProductShowResponse} from '~/types/api/response/ProductShowResponse'
import {type ApiResponse, Post} from '~/utils/http'
import type {CheckoutResponse} from '~/types/api/response/CheckoutResponse'
import type {CheckoutParams} from '~/types/api/params/CheckoutParams'
import type {BillingHistoryResponse} from '~/types/api/response/BillingHistoryResponse'

/**
 * 创建通知相关API
 */
function createPayApi() {
    // 基础URL
    const baseUrl = '/app/pay'

    /**
     * 查询同个groupType的商品列表
     * @param params 参数
     * @returns 商品列表
     */
    function queryProductList(params: ProductShowParams): Promise<ApiResponse<Record<string, ProductShowResponse[]>>> {
        apiLogger.info('订阅邮件通知', params)
        return Post<Record<string, ProductShowResponse[]>>(`${baseUrl}/queryProductList`, params, {})
    }

    /**
     * 结账
     * @param params 参数
     * @returns 结账响应
     */
    function checkout(params: CheckoutParams): Promise<ApiResponse<CheckoutResponse>> {
        apiLogger.info('订阅邮件通知', params)
        return Post<CheckoutResponse>(`${baseUrl}/checkout`, params, {})
    }

    /**
     * 查询历史账单
     * @param params 参数
     * @returns 查询历史账单
     */
    function queryBillingHistory(): Promise<ApiResponse<BillingHistoryResponse[]>> {
        apiLogger.info('查询历史账单')
        return Post<BillingHistoryResponse[]>(`${baseUrl}/queryBillingHistory`, {}, {})
    }

    // 返回API对象
    return {
        queryProductList,
        checkout,
        queryBillingHistory
    }
}

// 导出通知API实例
export const payApi = createPayApi()

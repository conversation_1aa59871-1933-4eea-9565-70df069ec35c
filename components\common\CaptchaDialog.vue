<script setup lang="ts">

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue'])

const internalOpenState = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
})

function handlePointerDownOutside(event: Event) {
    event.preventDefault()
}

function handleEscapeKeyDown(event: KeyboardEvent) {
    event.preventDefault()
}

</script>

<template>
  <Dialog :open="internalOpenState" @update:open="(val) => internalOpenState = val">
    <DialogContent
      class="sm:max-w-[425px]"
      @pointer-down-outside="handlePointerDownOutside"
      @escape-key-down="handleEscapeKeyDown"
    >
      <DialogHeader>
        <DialogTitle>安全验证</DialogTitle>
        <DialogDescription>
          为了您的账户安全，请完成以下验证。
        </DialogDescription>
      </DialogHeader>
      <div id="captcha-box" class="min-h-[160px] flex items-center justify-center"/>
      <!-- 您可以在这里添加一个明确的关闭按钮，并通过 emit('update:modelValue', false) 来关闭 -->
    </DialogContent>
  </Dialog>
</template>

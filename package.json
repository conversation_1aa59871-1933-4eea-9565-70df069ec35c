{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "cross-env NODE_OPTIONS=--max-old-space-size=5120 nuxi dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "prepare": "husky", "lint": "eslint --fix .", "lint:check": "eslint ."}, "lint-staged": {"*.{js,ts,vue,mjs}": ["eslint --fix"]}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@nuxt/eslint": "^1.3.0", "@nuxt/icon": "^1.12.0", "@nuxtjs/i18n": "^9.5.3", "@nuxtjs/robots": "^5.2.10", "@nuxtjs/sitemap": "^7.3.0", "@pinia/nuxt": "^0.11.0", "@tailwindcss/vite": "^4.1.5", "@tanstack/vue-table": "^8.21.3", "@unhead/vue": "^2.0.10", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.2.0", "chatarea": "^5.6.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-vue": "^8.6.0", "eslint": "^9.25.1", "highlight.js": "^11.11.1", "lucide-vue-next": "^0.503.0", "markdown-it": "^14.1.0", "nuxt": "^3.17.4", "nuxt-og-image": "^5.1.4", "nuxt-schema-org": "^5.0.5", "pinia": "^3.0.2", "reka-ui": "^2.2.1", "shiki": "^3.3.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.9", "uninstall": "^0.0.0", "unstorage": "^1.16.0", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue-sonner": "^1.3.2", "zod": "^3.25.12"}, "devDependencies": {"@iconify-json/radix-icons": "^1.2.2", "@iconify/vue": "^5.0.0", "@nuxtjs/color-mode": "^3.5.2", "@tailwindcss/typography": "^0.5.16", "@types/markdown-it": "^14.1.2", "@types/qs": "^6.9.18", "cross-env": "^10.0.0", "date-fns": "^4.1.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "3.5.3", "shadcn-nuxt": "^2.1.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}}
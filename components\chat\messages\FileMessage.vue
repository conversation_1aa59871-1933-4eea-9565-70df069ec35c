<script setup lang="ts">
import {type Component, defineAsyncComponent} from 'vue'
import type {ChatMessage, FileData} from '@/types/chat'

// 定义组件的props
const props = defineProps<{
    // 文件消息对象
    message: ChatMessage
}>()

// 异步组件映射，用于根据消息类型动态加载不同的消息组件
const fileComponents: Record<string, Component> = {
    IMAGE: defineAsyncComponent(() => import('./ImageMessage.vue')),
    VIDEO: defineAsyncComponent(() => import('./VideoMessage.vue')),
    AUDIO: defineAsyncComponent(() => import('./AudioMessage.vue')),
    BINARY_FILE: defineAsyncComponent(() => import('./BinaryFileMessage.vue'))
}

// 计算属性，根据消息类型返回对应的组件
const componentType = computed(() => {
    const fileType = (props.message.clientData as FileData)?.fileType
    return fileComponents[fileType] || fileComponents.BINARY_FILE
})
</script>

<template>
  <component :is="componentType" :message="message"/>
</template>

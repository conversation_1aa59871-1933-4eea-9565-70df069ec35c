<template>
  <!-- 使用一个父元素来辅助重新创建 blockquote -->
  <div ref="redditWidgetParent"/>
</template>

<script setup lang="ts">
import {getCurrentLanguage} from '~/utils/i18n'
import {logger} from '~/utils/logger'

// 为 window 定义一个更具体的类型，以避免使用 any
interface WindowWithRedditEmbed extends Window {
    RedditEmbed?: unknown;
}

const props = defineProps({
    postId: {
        type: String,
        required: true
    }
})

const colorMode = useColorMode()
const currentLanguage = ref(getCurrentLanguage())
const redditWidgetParent = ref<HTMLElement | null>(null)

// ensureScriptLoaded 现在主要负责脚本的加载和重载，其回调不再负责创建widget
const ensureScriptLoaded = () => {
    if (!import.meta.client) {
        return
    }

    const scriptId = 'reddit-widgets-script'
    const existingScript = document.getElementById(scriptId)

    if (existingScript) {
        existingScript.remove()
    }
    // 清除可能由旧脚本设置的全局变量
    const win = window as WindowWithRedditEmbed
    if (win.RedditEmbed) {
        delete win.RedditEmbed
    }
    // 如果还有其他已知的由 redditWidgets.js 设置的全局变量，也应在此处清除

    const script = document.createElement('script')
    script.id = scriptId
    script.src = '/js/redditWidgets.js'
    script.async = true
    script.charset = 'utf-8'
    script.onload = () => {
        logger.debug('Reddit widgets.js loaded and executed.')
    }
    script.onerror = () => {
        logger.error('Failed to load Reddit widgets.js')
    }
    document.head.appendChild(script)
}

const loadWidget = () => {
    if (!import.meta.client || !redditWidgetParent.value) {
        return
    }
    logger.debug(`Creating/Updating Reddit widget DOM for Post ID: ${props.postId}, Theme: ${colorMode.value}, Lang: ${currentLanguage.value}`)

    // 清空父容器
    redditWidgetParent.value.innerHTML = ''

    // 重新创建 blockquote 元素
    const blockquote = document.createElement('blockquote')
    blockquote.className = 'reddit-embed-bq'
    // reddit的语言有点少 让它自动选择
    // blockquote.setAttribute('data-embed-locale', currentLanguage.value)
    blockquote.setAttribute('data-embed-theme', colorMode.value)
    blockquote.setAttribute('data-embed-height', '500')

    const link = document.createElement('a')
    link.href = `https://www.reddit.com/r/indiehackers/comments/${props.postId}`
    blockquote.appendChild(link)

    redditWidgetParent.value.appendChild(blockquote)
    logger.debug('Reddit widget DOM structure created/updated.')
}

onMounted(() => {
    logger.debug('RedditPreview onMounted: Loading widget DOM then ensuring script.')
    loadWidget()
    ensureScriptLoaded()
})

watch([() => props.postId, colorMode, currentLanguage], () => {
    logger.debug('Reddit watcher triggered: Loading widget DOM then ensuring script.')
    loadWidget()
    ensureScriptLoaded()
}, {deep: true, immediate: false})

</script>
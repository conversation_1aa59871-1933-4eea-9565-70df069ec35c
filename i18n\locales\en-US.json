{"nav": {"sign_in": "Sign In"}, "theme": {"theme_light": "Light", "theme_dark": "Dark", "theme_system": "System"}, "auth": {"login_terms": "By clicking continue, you agree to our ", "login_terms_service": "Terms of Service", "login_terms_privacy": "Privacy Policy", "unauthenticated_redirect": "Authentication required. Redirecting to login page...", "codeSentSuccess": "Verification code sent", "signupSuccessfulPleaseLogin": "Signup successful! Please login with your new password.", "login_successful_welcome_back": "Login successful, welcome back!", "login_fetch_user_info_incomplete": "<PERSON>gin successful, but failed to fetch complete user information.", "code_sent_failed": "Failed to send verification code, please try again.", "code_sent_success": "Verification code sent successfully.", "message_password_reset_success": "Password reset successful.", "message_password_reset_failed": "Password reset failed.", "labels": {"username": "Username", "email": "Email", "password": "Password", "loginText": "Sign In", "signOut": "Sign Out", "resendCodeBtn": "Resend", "sendingCode": "Sending...", "resendCodeCooldown": "Resend in {seconds}s", "backBtn": "Back", "emailVerificationCode": "Email Verification Code", "enterCodeSentTo": "Please enter the verification code sent to:", "registerWithEmailCodeLink": "Register with Email Code", "loginWithEmailCodeLink": "Sign In with Email Code", "backToEmailInput": "Back to Email Input", "accountExistNoPasswordUseCode": "This account exists but has no password set. Please use verification code to log in.", "resetSubmitting": "Submitting...", "signupBtn": "Sign Up", "setPasswordForNewAccount": "Set password for your new account", "loginForgotPassword": "Forgot password?", "loadingAuthOptions": "Loading authentication options...", "loginShowLessOptions": "Show less", "loginMoreOptions": "More options", "loginOrContinue": "Or continue with", "loadingChecking": "Checking...", "continueBtn": "Continue", "reset_back_login": "Back to login", "status_resetting_password": "Resetting password...", "reset_password": "Reset Password", "reset_new_password": "New Password", "email_verification_code": "Email Verification Code", "get_code_btn": "Get Code", "reset_password_mismatch": "Passwords do not match.", "reset_confirm_btn": "Confirm", "reset_confirm_password": "Confirm New Password", "reset_new_subtitle": "Please enter your new password.", "reset_enter_email": "Enter your email to receive a verification code.", "signup_have_account": "Already have an account?", "signup_create_account": "Create Account", "login_welcome_back": "Welcome back"}, "placeholders": {"password": "Enter your password", "username": "Enter your username", "email": "Enter your email", "new_password_min_length": "Enter new password (at least 6 characters)", "verification_code": "Enter verification code"}, "api": {"fetchUserInfoIncomplete": "Fetched user information is incomplete."}}, "error": {"home": "Home", "support": "Support", "server_error": "Server Error", "this_page_doesnt_exist": "This page does not exist."}, "support": {"title": "Submit a Support Request", "description": "We are here to help. Please tell us about the issue you are experiencing.", "type": "Request Type", "select_type": "Select request type", "type_feedback": "<PERSON><PERSON><PERSON>", "type_error": "Error Report", "type_required": "Please select a request type.", "description_label": "Description", "description_placeholder": "Describe your issue or feedback in detail...", "description_required": "Please provide a description.", "send": "Send Request", "sending": "Sending...", "success": "Thank you for your feedback! We will get back to you soon."}, "optional": "Optional", "confirm": {"default_title": "Confirm", "success_title": "Success", "warning_title": "Warning", "info_title": "Information", "error_title": "Error", "confirm_button": "Confirm", "cancel_button": "Cancel", "delete_button": "Delete"}, "notification": {"success": "Success", "warning": "Warning", "info": "Info", "error": "Error"}, "email": {"email_invalid": "Please enter a valid email address.", "text": "Email", "warn_empty": "Please enter your email address.", "warn_invalid_format": "Invalid email format."}, "pagination": {"previous": "Previous", "next": "Next", "first": "First", "last": "Last"}, "blog": {"back_to_blog": "Back to Blog List", "comments": "Comments", "comment_count": "comments", "no_comments": "No comments yet. Be the first to comment!", "write_comment": "Write your comment...", "reply_to": "Reply to", "post_comment": "Post Comment", "load_more": "Load More Comments", "all_comments_loaded": "All comments loaded.", "reading_time": "min read", "published_on": "Published on", "article_not_found": "Article not found.", "download": "Download as <PERSON><PERSON>", "page": {"recommended_reading": "Recommended Reading", "article_not_found_title": "Article Not Found", "siteTitle": "Blog", "listSiteDescription": "Explore our latest articles and insights on technology and development.", "articleNotFoundSiteTitle": "Sorry, the article you requested does not exist.", "no_recommendations": "No recommended articles found."}, "blogPreview": {"title": "Featured Blogs", "subtitle": "Stay updated with the latest industry trends and expert opinions."}}, "validation": {"email": "Please enter a valid email address.", "username": "Invalid username.", "password": "Invalid password.", "error_password_min_length": "Password must be at least {length} characters long.", "error_code_min_length": "Verification code must be at least {length} characters long."}, "newsletter": {"subscribeSuccess": "Subscription successful.", "subscribeError": "Subscription failed.", "contact_support": "Contact Support"}, "clipboard": {"emptyContent": "Content to copy cannot be empty.", "browserUnSupport": "Your browser does not support the copy function.", "copySuccess": "<PERSON>pied successfully.", "copyFailure": "<PERSON><PERSON> failed."}, "common": {"loading": "Loading...", "sort_desc": "Sort by time descending", "sort_asc": "Sort by time ascending", "submitting": "Submitting...", "close": "Close", "and": "and"}, "api": {"uri_cannot_be_empty": "URI cannot be empty.", "create_data_cannot_be_empty": "Creation data cannot be empty.", "update_id_cannot_be_empty": "Update ID cannot be empty.", "update_data_cannot_be_empty": "Update data cannot be empty.", "id_cannot_be_empty": "ID cannot be empty.", "pagination_params_cannot_be_empty": "Pagination parameters cannot be empty.", "delete_id_cannot_be_empty": "Delete ID cannot be empty.", "batch_delete_ids_cannot_be_empty": "Batch delete ID list cannot be empty.", "entity_name_cannot_be_empty": "Entity name cannot be empty.", "request_params_invalid": "Request parameters are missing."}, "captcha": {"concatText": "Drag the slider to complete the puzzle.", "imageClickText": "Please click in order.", "rotateText": "Drag the slider to complete the puzzle.", "sliderText": "Drag the slider to complete the puzzle."}, "http": {"service_unavailable": "Service temporarily unavailable.", "unknown_error": "An unknown error occurred.", "api_base_not_configured": "API base URL is not configured.", "request_preparation_error": "Error occurred during request preparation.", "request_failed": "Request failed.", "no_response_data": "No response data received.", "response_format_unexpected": "Response format is not as expected.", "request_params_must_be_object": "Request parameters must be an object."}}
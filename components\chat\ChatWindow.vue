<script setup lang="ts">
// import ChatArea from 'chatarea' - This will be dynamically imported
import 'chatarea/lib/ChatArea.css'
import { Button } from '@/components/ui/button'
import { ContextMenu, ContextMenuContent, ContextMenuTrigger } from '@/components/ui/context-menu'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Icon } from '#components'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import MessageRenderer from './messages/MessageRenderer.vue'
import {
    type BackendMessage,
    type ChatMessage,
    type Contact,
    type ContextMenuItem,
    type EmojiData,
    type EmojiDetail,
    type EmojiInfo,
    type FileData,
    type LocationMessage,
    MessageCategory,
    type MessageDetail,
    MessageTypeEnum,
    PlatformType,
    type PlatformUserinfo,
    type ProductData,
    type UnknownMessage
} from '@/types/chat'
import type { CustomerAgentClientWsService } from '~/utils/api/ws/agentClient.service'
import type { PageData, WebSocketResponse } from '~/utils/http/types'
import type { SendMessageParams } from '~/types/api/params/SendMessageParams'
import type { Platform, PlatformAccount } from '~/types/api/response/PlatformResponse'
import { formatHumanTime } from '~/utils/typeof'
import { watch } from 'vue'

// 定义组件的 props
const props = defineProps<{
    // 当前活跃的联系人
    activeContact: Contact | null
    // 当前用户信息
    currentSubAccount: PlatformAccount
    // websocket服务
    agentClientApi: CustomerAgentClientWsService | null
    platformInfo: Platform | null
    platformType: PlatformType | null
}>()

const platformEmojiPathMapping: Record<string, string> = {
    'QIAN_NIU': 'taobao'
}

// 定义组件的 emits
const emit = defineEmits<{
    (e: 'send-message', messageContent: string, messageType: ChatMessage['type'], extras?: Record<string, unknown>): void
    (e: 'update-message', updatedMessage: Partial<Omit<ChatMessage, 'platformMessageId'>> & {
        platformMessageId: string
    }): void
    (e: 'delete-message', messageId: string | number): void
}>()

// 对话输入区域的DOM元素引用
const chatElm = ref<HTMLElement | null>(null)
// ChatArea 实例的引用
const chatAreaInstance = ref<unknown>()
// 消息列表容器的DOM元素引用
const messageListContainer = ref<HTMLElement | null>(null)
// 正在回复的消息
const messageToReply = ref<ChatMessage | null>(null)
// 文件输入框的DOM元素引用
const fileInput = ref<HTMLInputElement | null>(null)
const currentPlatformEmojiList = ref<EmojiInfo[]>([])
const emojiShortCutPathMapping = ref<Record<string, string>>({})

// 控制"回到上次浏览位置"按钮的显示
const currentScrollTop = ref(0)
const lastViewButtonClicked = ref(false) // 记录按钮是否被点击过
const scrollTrigger = ref(0) // 用于触发 computed 重新计算的时间戳
const enterTime = ref(0) // 记录进入对话的时间戳

// 按联系人ID存储消息的状态
const conversationCache = ref<Record<string, {
    list: ChatMessage[]
    hasMore: boolean
    loadingSort: 'asc' | 'desc' | null
    scrollRestoreInfo?: {
        oldHeight: number
        oldScrollTop: number
    }
    lastViewPosition?: {
        scrollTop: number
        timestamp: number
    }
    lastAccessTime: number // 添加最后访问时间
}>>({})

const localSendMessageMapping = ref<Record<string, ChatMessage>>({})

// 缓存大小限制
const MAX_CACHE_SIZE = 50

/**
 * 清理缓存，只保留最近访问的30个联系人数据
 */
function cleanupCache() {
    const cacheEntries = Object.entries(conversationCache.value)

    // 如果缓存数量超过限制，则清理旧数据
    if (cacheEntries.length > MAX_CACHE_SIZE) {
        // 按最后访问时间排序，保留最近的30个
        const sortedEntries = cacheEntries.sort((a, b) => b[1].lastAccessTime - a[1].lastAccessTime)
        const keepEntries = sortedEntries.slice(0, MAX_CACHE_SIZE)
        const removeEntries = sortedEntries.slice(MAX_CACHE_SIZE)

        // 清空缓存并重新填充
        conversationCache.value = {}
        keepEntries.forEach(([key, value]) => {
            conversationCache.value[key] = value
        })

        logger.info(`缓存清理完成，移除了 ${removeEntries.length} 个旧联系人数据，保留 ${keepEntries.length} 个`)
    }
}

// 计算当前活跃联系人的消息列表
const activeContactMessages = computed(() => {
    if (!props.activeContact) {
        return []
    }
    const key = getMessageCacheKey(String(props.activeContact.conversationId), String(props.platformInfo?.id))
    return conversationCache.value[key]?.list || []
})

// 计算当前活跃联系人的消息状态
const activeContactState = computed(() => {
    if (!props.activeContact || !props.platformInfo) {
        return { hasMore: false, loadingSort: null, list: [] }
    }
    const cacheKey = getMessageCacheKey(String(props.activeContact.conversationId), String(props.platformInfo.id))
    const state = conversationCache.value[cacheKey]

    // 更新最后访问时间
    if (state) {
        state.lastAccessTime = Date.now()
    }

    return state || { hasMore: true, list: [], loadingSort: null }
})

// 计算是否显示"回到上次浏览位置"按钮
const shouldShowBackToLastViewButton = computed(() => {
    scrollTrigger.value

    if (!props.activeContact || !props.platformInfo || lastViewButtonClicked.value) {
        return false
    }
    const cacheKey = getMessageCacheKey(String(props.activeContact.conversationId), String(props.platformInfo.id))
    const state = conversationCache.value[cacheKey]

    // 只有在进入对话后10秒内且距离底部400px以内才显示按钮
    if (state?.lastViewPosition && messageListContainer.value && enterTime.value > 0) {
        const container = messageListContainer.value
        const distanceFromBottom = Math.abs(container.scrollHeight - container.clientHeight - container.scrollTop)
        const timeSinceEnter = Date.now() - enterTime.value

        // 距离底部400px以内 且 进入时间不超过10秒
        return distanceFromBottom <= 700 && timeSinceEnter <= 10000
    }

    return false
})

/**
 * 获取历史消息
 * @param contactId 联系人id
 * @param conversationId 对话id
 * @param sort 排序方式
 */
async function fetchMessages(contactId: string, conversationId: string, sort: 'asc' | 'desc' = 'desc') {
    logger.info('正在查询对话消息', { contactId: contactId, sort, conversationId: conversationId })
    if (!props.agentClientApi) {
        return
    }

    if (contactId !== props.activeContact?.contactId) {
        logger.error('当前查询的contactId并不是当前激活的联系人', { contactId, activeContact: props.activeContact })
        return
    }
    const state = conversationCache.value[getMessageCacheKey(props.activeContact.conversationId, String(props.platformInfo?.id))]
    if (state.loadingSort) {
        return
    }

    state.loadingSort = sort

    let cursorData = null
    let extData = null

    if (sort === 'desc' && state.list.length > 0) {
        // 加载更旧的消息，用当前最旧一条消息的游标
        const oldestMessage = state.list[0]
        cursorData = oldestMessage.cursorData
        extData = oldestMessage.extData
    } else if (sort === 'asc' && state.list.length > 0) {
        // 加载更新的消息，用当前最新一条消息的游标
        const newestMessage = state.list[state.list.length - 1]
        cursorData = newestMessage.cursorData
        extData = newestMessage.extData
    }

    logger.info('正在获取对话的消息', { contactId: contactId, cursorData, extData, sort })
    props.agentClientApi.queryHistoryMsgList({
        conversationId: conversationId,
        contactId: contactId,
        pageSize: 20,
        cursorData,
        extData,
        messageSort: sort
    }, props.currentSubAccount.accountId)
}

function handleHistoryMessageResponse(response: WebSocketResponse<PageData<BackendMessage>>) {
    if (response.code !== 200) {
        return
    }

    const conversationId = response.data?.result?.[0]?.conversationId
    const platformId = response.data?.result?.[0]?.platformId
    if (!conversationId || !platformId) {
        logger.warn('当前消息中不存在platformId或conversationId', { conversationId, platformId: platformId })
        return
    }
    const cacheKey = getMessageCacheKey(conversationId, platformId)
    logger.info(`本地缓存key -> ${cacheKey}`)
    const state = conversationCache.value[cacheKey]
    if (!state || !state.loadingSort) {
        logger.warn('未在messagesByContact中查询到该对话', { conversationId: conversationId })
        // Not a response we are waiting for, ignore.
        return
    }
    const sort = state.loadingSort
    const isInitialLoad = state.list.length === 0
    const { scrollRestoreInfo } = state
    state.loadingSort = null
    state.scrollRestoreInfo = undefined

    if (response.data && response.data.result.length > 0) {
        const newMessages = response.data.result.map(transformBackendMessage)

        if (sort === 'desc') {
            state.list = [...newMessages, ...state.list]
        } else {
            state.list = [...state.list, ...newMessages]
        }

        state.hasMore = response.data.additionalData?.hasMore ?? false
        console.log('当前对话处理之后的数据为', {
            responseMsgList: response.data.result,
            handledMsgList: newMessages,
            sort,
            hasMore: state.hasMore,
            isInitialLoad,
            scrollRestoreInfo
        })

        // 处理滚动逻辑
        nextTick(() => {
            const container = messageListContainer.value
            if (!container) {
                return
            }

            if (isInitialLoad && sort === 'desc') {
                // 初次加载消息（切换联系人），滚动到底部
                container.scrollTop = container.scrollHeight
                logger.info('初次加载消息完成，已滚动到底部')
            } else if (sort === 'desc' && scrollRestoreInfo) {
                // 加载更多历史消息，恢复滚动位置
                const newHeight = container.scrollHeight
                const heightDiff = newHeight - scrollRestoreInfo.oldHeight
                container.scrollTop = scrollRestoreInfo.oldScrollTop + heightDiff

                logger.info('加载更多历史消息完成，已恢复滚动位置', {
                    oldHeight: scrollRestoreInfo.oldHeight,
                    newHeight,
                    heightDiff,
                    oldScrollTop: scrollRestoreInfo.oldScrollTop,
                    newScrollTop: container.scrollTop
                })
            }
        })
    } else {
        state.hasMore = false
        logger.info('当前对话已没有更多历史消息', { contactId: conversationId })
    }
}

function getPlatformEmojiFormat(emoji: EmojiData): string {
    if (props.platformType === PlatformType.QIAN_NIU) {
        return emoji.shortCut
    }
    return `[${emoji.meaning}]`
}

/**
 * 将后端返回的消息格式转换为前端可用的ChatMessage格式
 * @param backendMsg 后端消息对象
 */
function transformBackendMessage(backendMsg: BackendMessage): ChatMessage {
    const sendTimeValue = new Date(backendMsg.sendTime).getTime()

    // 基础的 ChatMessage 对象，继承自 BackendMessage
    const baseChatMessage: Omit<ChatMessage, 'type' | 'content' | 'clientData'> = {
        ...backendMsg,
        sendTimeValue
    }

    const { list } = backendMsg

    // 1. 处理混合内容 (文本+表情)
    if (list.length > 1) {
        const content = list.map((detail) => {
            if (detail.type === MessageTypeEnum.TEXT) {
                return detail.data as string
            }
            if (detail.type === MessageTypeEnum.EMOJI) {
                const emoji = detail.data as EmojiData
                const emojiSrc = emojiShortCutPathMapping.value[String(emoji.shortCut)]
                // 将表情数据转换为img标签
                return `<img src="${emojiSrc}" alt="${emoji.meaning}" style="width: 22px; height: 22px; vertical-align: bottom; display: inline-block;" data-img-text="{{${getPlatformEmojiFormat(emoji)}}}">`
            }
            // 理论上不会有其他类型
            logger.error('当前消息存在非TEXT, EMOJI类型', { detail })
            return ''
        }).join('')

        return {
            ...baseChatMessage,
            type: MessageCategory.TEXT,
            content,
            clientData: content
        }
    }

    // 2. 处理单一内容
    const detail = list[0]
    if (!detail) {
        logger.error('后端消息中list不存在有效消息', { backendMsg })
        return {
            ...baseChatMessage,
            type: MessageCategory.UNKNOWN,
            content: '[错误消息-请看日志]',
            clientData: '[错误消息-请看日志]'
        }
    }

    const clientData = detail.data
    switch (detail.type) {
        case MessageTypeEnum.TEXT:
            return {
                ...baseChatMessage,
                type: MessageCategory.TEXT,
                content: clientData as string,
                clientData
            }
        case MessageTypeEnum.EMOJI: {
            const emoji = clientData as EmojiData
            const emojiSrc = emojiShortCutPathMapping.value[String(emoji.shortCut)]
            const content = `<img src="${emojiSrc}" alt="${emoji.meaning}" style="width: 22px; height: 22px; vertical-align: bottom; display: inline-block;" data-img-text="{{${getPlatformEmojiFormat(emoji)}}}">`
            return {
                ...baseChatMessage,
                // 统一作为富文本处理
                type: MessageCategory.TEXT,
                content,
                clientData: emoji
            }
        }
        case MessageTypeEnum.PRODUCT_CONSULT: {
            const productInfo = clientData as ProductData
            return {
                ...baseChatMessage,
                type: MessageCategory.PRODUCT_CONSULT,
                content: `[商品] ${detail.messageContent}`,
                clientData: productInfo
            }
        }

        case MessageTypeEnum.FILE: {
            const fileInfo = clientData as FileData
            return {
                ...baseChatMessage,
                type: MessageCategory.FILE,
                content: `[文件] ${fileInfo.filename}`,
                clientData: fileInfo
            }
        }
        case MessageTypeEnum.LOCATION:
            const location = clientData as LocationMessage
            return {
                ...baseChatMessage,
                type: MessageCategory.LOCATION,
                content: `[位置] ${location.location}`,
                clientData
            }
        case MessageTypeEnum.INVITE_ORDER:
            return {
                ...baseChatMessage,
                type: MessageCategory.INVITE_ORDER,
                content: `[邀请下单] ${detail.messageContent}`,
                clientData
            }
        case MessageTypeEnum.ORDER_QUESTION:
            return {
                ...baseChatMessage,
                type: MessageCategory.ORDER_CONSULT,
                content: `[订单咨询] ${detail.messageContent}`,
                clientData
            }
        case MessageTypeEnum.UNKNOWN:
            return {
                ...baseChatMessage,
                type: MessageCategory.UNKNOWN,
                content: `[暂不支持的消息类型 类型代码: ${(clientData as UnknownMessage).type} 平台侧消息: ${detail.messageContent}]`,
                clientData
            }
        default:
            return {
                ...baseChatMessage,
                type: MessageCategory.UNKNOWN,
                content: '[位置消息类型]',
                clientData
            }
    }
}

async function handleLoadMore(): Promise<void> {
    if (props.activeContact) {
        await fetchMessages(String(props.activeContact.contactId), String(props.activeContact.conversationId), 'desc')
    }
}

/**
 * 保存指定联系人的浏览位置
 * @param contact 要保存浏览位置的联系人
 */
function saveCurrentViewPosition(contact: Contact) {
    const targetContact = contact || props.activeContact
    if (!targetContact || !props.platformInfo || !messageListContainer.value) {
        return
    }

    const cacheKey = getMessageCacheKey(String(targetContact.conversationId), String(props.platformInfo.id))
    const state = conversationCache.value[cacheKey]

    if (state) {
        state.lastViewPosition = {
            scrollTop: messageListContainer.value.scrollTop,
            timestamp: Date.now()
        }
        logger.info('已保存浏览位置', {
            scrollTop: state.lastViewPosition.scrollTop,
            contactId: targetContact.contactId
        })
    }
}

/**
 * 回到上次浏览位置
 */
function backToLastViewPosition() {
    logger.info('触发回到上次浏览位置')

    if (!props.activeContact || !props.platformInfo || !messageListContainer.value) {
        return
    }

    const cacheKey = getMessageCacheKey(String(props.activeContact.conversationId), String(props.platformInfo.id))
    const state = conversationCache.value[cacheKey]

    if (state?.lastViewPosition) {
        // 使用平滑滚动到目标位置
        messageListContainer.value.scrollTo({
            top: state.lastViewPosition.scrollTop,
            behavior: 'smooth'
        })

        // 点击后隐藏按钮
        lastViewButtonClicked.value = true
        logger.info('已回到上次浏览位置（平滑滚动）', {
            scrollTop: state.lastViewPosition.scrollTop,
            contactId: props.activeContact.contactId
        })
    }
}

/**
 * 模拟文件上传过程
 * @param file 要上传的文件
 * @param messageId 对应的消息ID
 */
function simulateUpload(file: File, messageId: string | number) {
    let progress = 0
    const interval = setInterval(() => {
        progress += 10
        if (progress <= 100) {
            // 更新上传进度
            emit('update-message', {
                platformMessageId: messageId as string,
                uploadProgress: progress
            })
        }
        if (progress >= 100) {
            clearInterval(interval)
            // 上传完成，更新消息的文件信息
            emit('update-message', {
                platformMessageId: messageId as string,
                clientData: {
                    filename: file.name,
                    size: file.size,
                    url: URL.createObjectURL(file),
                    fileType: 'BINARY_FILE' // Mock fileType
                } as FileData,
                uploadProgress: 100
            })
        }
    }, 200)
}

/**
 * 处理通用文件上传
 * @param file 要上传的文件
 */
function handleGenericFileUpload(file: File) {
    if (!file) {
        return
    }
    const newMessageId = `msg-file-${Date.now()}`

    // 创建一个临时的 FileData 对象用于立即显示
    const tempFileData: FileData = {
        filename: file.name,
        size: file.size,
        // URL 在上传后才会知道
        url: '',
        // 假设为通用二进制文件
        fileType: 'BINARY_FILE'
    }

    // 发送一个文件类型的消息（初始状态）
    emit('send-message', `[文件] ${file.name}`, MessageCategory.FILE, {
        id: null,
        platformMessageId: newMessageId,
        clientData: tempFileData,
        uploadProgress: 0
    })
    // 开始模拟上传
    simulateUpload(file, newMessageId)
}

/**
 * 处理图片上传（直接插入到输入框）
 * @param file 要上传的图片文件
 */
function handleImageUpload(file: File) {
    const reader = new FileReader()
    reader.onload = (e) => {
        const imageUrl = e.target?.result as string
        if (imageUrl && chatAreaInstance.value) {
            // 将图片以img标签形式插入到输入框
            (chatAreaInstance.value as any).insertHtml(`<img src="${imageUrl}" alt="${file.name}" style="max-width: 200px; vertical-align: bottom; display: inline-block;">`)
        }
    }
    reader.readAsDataURL(file)
}

/**
 * 当文件输入框选择文件后触发
 * @param event input事件对象
 */
function onFileSelected(event: Event) {
    const target = event.target as HTMLInputElement
    const { files } = target
    if (!files || files.length === 0) {
        return
    }

    // 遍历所有选择的文件
    for (const file of Array.from(files)) {
        // 根据文件类型调用不同的处理函数
        if (file.type.startsWith('image/')) {
            handleImageUpload(file)
        } else {
            handleGenericFileUpload(file)
        }
    }

    // 重置输入框，以便可以再次选择相同的文件
    target.value = ''
}

/**
 * 处理回复消息操作
 * @param message 要回复的消息
 */
function handleReply(message: ChatMessage) {
    messageToReply.value = message
    if (chatAreaInstance.value) {
        // 聚焦到输入框
        const richTextArea = (chatElm.value as HTMLElement)?.querySelector('.rich-text-area')
        if (richTextArea) {
            (richTextArea as HTMLElement).focus()
        }
    }
}

/**
 * 处理撤回消息
 * @param message 要撤回的消息
 */
function handleRecallMessage(message: ChatMessage) {
    props.agentClientApi?.recallMessage(message.cursorData, message.platformMessageId, props.currentSubAccount.accountId, message.conversationId).then(executeResult => {
        logger.info(`消息 [${message.platformMessageId}] 已被成功撤回, 正在从消息列表中删除, 工具执行结果`, { executeResult })
        const cacheKey = getMessageCacheKey(message.conversationId, message.platformId)
        if (conversationCache.value[cacheKey] && conversationCache.value[cacheKey].list) {
            conversationCache.value[cacheKey].list.splice(conversationCache.value[cacheKey].list.indexOf(message), 1)
        }
    }).catch(msg => {
        logger.info(`消息 [${message.platformMessageId}] 未能成功撤回, 工具执行结果`, { msg })
        message.errorResponseMessage = `消息撤回失败: ${msg}`
        message.errorRetryAction = () => handleRecallMessage(message)
    })
}

/**
 * 取消回复
 */
function cancelReply() {
    messageToReply.value = null
}

/**
 * 处理粘贴事件，用于支持粘贴文件
 * @param event 粘贴事件对象
 */
function handlePaste(event: ClipboardEvent) {
    const items = event.clipboardData?.items
    if (!items) {
        return
    }

    // 查找剪贴板中是否存在非图片文件
    const nonImageFileItem = Array.from(items).find(item => item.kind === 'file' && !item.type.startsWith('image/'))

    if (nonImageFileItem) {
        const file = nonImageFileItem.getAsFile()
        if (!file) {
            return
        }

        // 如果找到非图片文件，则阻止默认粘贴行为
        event.preventDefault()
        handleGenericFileUpload(file)
    }
    // 如果是图片文件，则不阻止，由 chatarea 库自行处理
}

/**
 * 处理消息列表的滚动事件，用于加载更多历史消息
 */
function handleScroll() {
    if (messageListContainer.value) {
        // 更新当前滚动位置，用于按钮显示判断
        currentScrollTop.value = messageListContainer.value.scrollTop
        // 更新滚动触发器，让 computed 重新计算
        scrollTrigger.value = Date.now()
        // 当滚动到顶部时，加载更多
        if (messageListContainer.value.scrollTop === 0 && activeContactState.value.hasMore && !activeContactState.value.loadingSort) {
            const container = messageListContainer.value
            const oldHeight = container.scrollHeight
            const oldScrollTop = container.scrollTop

            // 保存滚动恢复信息到状态中
            if (props.activeContact) {
                const cacheKey = getMessageCacheKey(String(props.activeContact.conversationId), String(props.platformInfo?.id))
                const state = conversationCache.value[cacheKey]
                if (state) {
                    state.scrollRestoreInfo = {
                        oldHeight,
                        oldScrollTop
                    }
                }
            }

            // 触发加载更多消息
            handleLoadMore()
        }
    }
}

// 组件挂载后执行
onMounted(async () => {
    // 动态导入 chatarea 库
    const ChatArea = (await import('chatarea')).default
    initChatArea(ChatArea)
    // 添加滚动和粘贴事件监听
    messageListContainer.value?.addEventListener('scroll', handleScroll)
    chatElm.value?.addEventListener('paste', handlePaste)
})

// 组件卸载前执行
onBeforeUnmount(() => {
    // 销毁 chatarea 实例并移除事件监听
    if (chatAreaInstance.value) {
        (chatAreaInstance.value as any).dispose()
        chatAreaInstance.value = null
    }
    messageListContainer.value?.removeEventListener('scroll', handleScroll)
    chatElm.value?.removeEventListener('paste', handlePaste)
})

/**
 * 初始化 ChatArea 输入框
 * @param ChatArea 动态导入的 ChatArea 类
 */
function initChatArea(ChatArea: any) {
    if (chatElm.value) {
        chatAreaInstance.value = new ChatArea({
            elm: chatElm.value,
            placeholder: '请输入消息...',
            needCallSpace: true,
            copyType: ['text', 'image'],
            // 自定义图片上传逻辑
            uploadImage: async (file: File) => {
                return new Promise((resolve) => {
                    const reader = new FileReader()
                    reader.onload = e => resolve(e.target?.result as string)
                    reader.readAsDataURL(file)
                })
            }
        })

            // 监听回车发送事件
            ; (chatAreaInstance.value as any).addEventListener('enterSend', sendMessage)
    }
}

/**
 * 发送消息
 */
function sendMessage() {
    if (!chatAreaInstance.value || (chatAreaInstance.value as any).isEmpty()) {
        return
    }

    const instance = (chatAreaInstance.value as any)
    const originalContent = instance.getText({
        imgToText: true
    }) as string

    // 将表情前面的[{{和}}]移除
    const content = originalContent.replaceAll('[{{', '').replaceAll('}}]', '')

    // 获取输入框中的HTML内容 TODO 图片待完成
    // const htmlMsg = (chatAreaInstance.value as any).getHtml({identifyLink: true})

    // 触发 send-message 事件
    const messageDetail: MessageDetail = {
        type: MessageTypeEnum.TEXT,
        messageContent: content,
        data: content
    }
    handleLocalSendMessage(messageDetail, MessageCategory.TEXT, { replyTo: messageToReply.value?.cursorData })

        // 清空输入框
        ; (chatAreaInstance.value as any).clear()
    // 取消回复状态
    cancelReply()
}

/**
 * 处理本地消息发送逻辑
 * @param messageDetail
 * @param messageCategory
 * @param extras
 */
function handleLocalSendMessage(messageDetail: MessageDetail, messageCategory: MessageCategory, extras?: Record<string, any>) {
    if (!props.activeContact || !props.currentSubAccount) {
        return
    }

    const clientMessageId = `local_${Date.now()}`
    const sender: PlatformUserinfo = {
        userId: props.currentSubAccount.accountId,
        displayName: props.currentSubAccount.displayName,
        nickname: props.currentSubAccount.nickname,
        avatar: props.currentSubAccount.avatar
    }

    // 1. Create a temporary message and add it to the list
    const temporaryMessage: ChatMessage = {
        id: null,
        platformMessageId: clientMessageId,
        conversationId: String(props.activeContact.contactId),
        sendTime: formatTime(new Date().getTime()),
        sender: sender,
        receiver: props.activeContact.contactDetail,
        category: messageCategory,
        list: [messageDetail],
        content: messageDetail.messageContent,
        extData: null,
        cursorData: null,
        platformId: String(props.platformInfo?.id),

        // Custom status for client-side state
        status: 'sending',
        type: messageCategory,
        sendTimeValue: Date.now(),
        clientData: messageDetail.data,
        replyTo: extras?.replyTo
    }

    const cacheKey = getMessageCacheKey(String(props.activeContact.conversationId), String(props.platformInfo?.id))
    if (!conversationCache.value[cacheKey]) {
        conversationCache.value[cacheKey] = {
            list: [],
            hasMore: true,
            loadingSort: null,
            lastAccessTime: Date.now()
        }
    }
    conversationCache.value[cacheKey].list.push(temporaryMessage)

    // 2. Call the API
    const params: SendMessageParams = {
        platformId: props.platformInfo?.id as string,
        platformUserinfo: sender,
        data: {
            conversationId: String(props.activeContact.conversationId),
            category: messageCategory,
            openReply: messageToReply.value != null,
            list: [messageDetail],
            sender: sender,
            receiver: props.activeContact.contactDetail,
            extData: extras ? extras : null,
            cursorData: extras ? extras['replyTo'] : null
        }
    }

    // 将消息放入本地消息映射中
    localSendMessageMapping.value[clientMessageId] = temporaryMessage

    logger.info('正在向对话发送消息', params)
    props.agentClientApi?.sendMessage(params, props.currentSubAccount.accountId, clientMessageId)

    // Scroll to bottom
    nextTick(() => {
        const container = messageListContainer.value
        if (container) {
            container.scrollTop = container.scrollHeight
        }
    })
}

function handleSendMessageResponse(response: WebSocketResponse) {
    if (!response.metadata || !response.metadata.localMessageId) {
        logger.error('消息回执结果中metadata为空或未包含localMessageId')
        message.error('错误的消息发送结果')
        return
    }

    const localMessageId = response.metadata.localMessageId as string
    if (!localSendMessageMapping.value[localMessageId]) {
        // 可能发送之后网页刷新了
        logger.error(`未能在本地消息映射中查找到本地msgId: ${localMessageId} 对应的消息, 无法修改其状态`, {
            localMessageId,
            response
        })
        return
    }

    const tempChatMessage = localSendMessageMapping.value[localMessageId]
    logger.info(`消息[${tempChatMessage.content}]发送状态为 --> ${response.code === 200}`)
    if (response.code === 200) {
        tempChatMessage.status = 'sent'
    } else {
        // Handle send failure
        // Try to get clientMessageId even on failure
        tempChatMessage.status = 'failed'
        message.error(`消息 [${tempChatMessage.content}] 发送失败 原因: ${response.message}`)
    }

    // 从localSendMessageMapping中移除
    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
    delete localSendMessageMapping.value[localMessageId]
}

/**
 * 在输入框中插入表情
 * @param emoji 表情对象
 */
function insertEmoji(emoji: EmojiDetail) {
    const emojiData: EmojiData = {
        shortCut: emoji.shortCut,
        meaning: emoji.meaning,
        url: emoji.src
    }
    if (chatAreaInstance.value) {
        (chatAreaInstance.value as any).insertHtml(`<img src="${emoji.src}" alt="${emoji.meaning}" style="width: 22px; height: 22px; vertical-align: bottom; display: inline-block;" data-img-text="{{${getPlatformEmojiFormat(emojiData)}}}">`)
    }
}

/**
 * 触发文件选择框（所有文件类型）
 */
function handleSendFile() {
    if (fileInput.value) {
        fileInput.value.accept = '*/*'
        fileInput.value.click()
    }
}

/**
 * 触发文件选择框（仅图片类型）
 */
function handleSendImage() {
    if (fileInput.value) {
        fileInput.value.accept = 'image/*'
        fileInput.value.click()
    }
}

// 获取平台菜单配置模板
function getPlatformMenuTemplate(platform: PlatformType): Map<MessageCategory, string[]> {
    const templates = new Map<MessageCategory, string[]>()

    switch (platform) {
        case PlatformType.QIAN_NIU:
            templates.set(MessageCategory.TEXT, ['回复', '撤回', '复制文字', '删除'])
            templates.set(MessageCategory.FILE, ['回复', '撤回', '下载', '删除'])
            templates.set(MessageCategory.PRODUCT_CONSULT, ['回复', '查看商品', '推荐给其他客户', '删除'])
            templates.set(MessageCategory.INVITE_ORDER, ['回复', '查看订单', '删除'])
            templates.set(MessageCategory.ORDER_CONSULT, ['回复', '查看订单', '处理订单问题', '删除'])
            templates.set(MessageCategory.LOCATION, ['回复', '在地图中查看', '删除'])
            templates.set(MessageCategory.UNKNOWN, ['回复', '删除'])
            break

        case PlatformType.PIN_DUO_DUO:
            templates.set(MessageCategory.TEXT, ['回复', '撤回', '复制文字', '拼团邀请', '删除'])
            templates.set(MessageCategory.FILE, ['回复', '撤回', '下载', '删除'])
            templates.set(MessageCategory.PRODUCT_CONSULT, ['回复', '查看商品', '发起拼团', '删除'])
            templates.set(MessageCategory.INVITE_ORDER, ['回复', '查看订单', '删除'])
            templates.set(MessageCategory.ORDER_CONSULT, ['回复', '查看订单', '处理订单问题', '删除'])
            templates.set(MessageCategory.LOCATION, ['回复', '在地图中查看', '删除'])
            templates.set(MessageCategory.UNKNOWN, ['回复', '删除'])
            break

        case PlatformType.JD:
            templates.set(MessageCategory.TEXT, ['回复', '撤回', '复制文字', '京东客服转接', '删除'])
            templates.set(MessageCategory.FILE, ['回复', '撤回', '下载', '删除'])
            templates.set(MessageCategory.PRODUCT_CONSULT, ['回复', '查看商品', '查看价格走势', '删除'])
            templates.set(MessageCategory.INVITE_ORDER, ['回复', '查看订单', '删除'])
            templates.set(MessageCategory.ORDER_CONSULT, ['回复', '查看订单', '处理订单问题', '删除'])
            templates.set(MessageCategory.LOCATION, ['回复', '在地图中查看', '删除'])
            templates.set(MessageCategory.UNKNOWN, ['回复', '删除'])
            break

        case PlatformType.XIAN_YU:
            templates.set(MessageCategory.TEXT, ['回复', '撤回', '复制文字', '议价', '删除'])
            templates.set(MessageCategory.FILE, ['回复', '撤回', '下载', '删除'])
            templates.set(MessageCategory.PRODUCT_CONSULT, ['回复', '查看商品', '发起议价', '查看卖家信誉', '删除'])
            templates.set(MessageCategory.INVITE_ORDER, ['回复', '查看订单', '删除'])
            templates.set(MessageCategory.ORDER_CONSULT, ['回复', '查看订单', '处理订单问题', '删除'])
            templates.set(MessageCategory.LOCATION, ['回复', '在地图中查看', '删除'])
            templates.set(MessageCategory.UNKNOWN, ['回复', '删除'])
            break
    }

    return templates
}

/**
 * 获取消息的右键菜单项
 * @param message 目标消息
 */
function getContextMenuItems(message: ChatMessage): ContextMenuItem[] {
    if (!props.platformType) {
        return []
    }

    const menuTemplate = getPlatformMenuTemplate(props.platformType)
    const menuLabels = menuTemplate.get(message.type) || []

    // 根据是否为自己发送的消息过滤菜单项
    const isSelf = message.sender.userId === props.currentSubAccount.accountId

    const filteredLabels = menuLabels.filter(label => {
        // 如果是自己发送的消息，显示撤回，隐藏回复
        if (isSelf && label === '回复') {
            return false
        }
        // 如果是他人发送的消息，隐藏撤回，显示回复
        if (!isSelf && label === '撤回') {
            return false
        }
        return true
    })

    // 将标签转换为菜单项
    return filteredLabels.map(label => createMenuItem(label, message))
}

/**
 * 根据标签创建菜单项
 * @param label 菜单标签
 * @param message 消息对象
 */
function createMenuItem(label: string, message: ChatMessage): ContextMenuItem {
    const baseItem: ContextMenuItem = {
        label,
        action: () => {
        }
    }

    switch (label) {
        case '回复':
            baseItem.action = () => handleReply(message)
            break
        case '撤回':
            baseItem.action = () => handleRecallMessage(message)
            break
        case '复制文字':
            baseItem.action = () => navigator.clipboard?.writeText(message.content)
            break
        case '删除':
            baseItem.danger = true
            baseItem.action = () => emit('delete-message', message.platformMessageId)
            break
        case '下载':
            baseItem.action = () => console.log('下载文件:', message)
            break
        case '查看商品':
            baseItem.action = () => console.log('查看商品:', message)
            break
        case '推荐给其他客户':
            baseItem.action = () => console.log('推荐给其他客户:', message)
            break
        case '查看订单':
            baseItem.action = () => console.log('查看订单:', message)
            break
        case '处理订单问题':
            baseItem.action = () => console.log('处理订单问题:', message)
            break
        case '在地图中查看':
            baseItem.action = () => console.log('在地图中查看:', message)
            break
        case '拼团邀请':
            baseItem.action = () => console.log('拼团邀请:', message)
            break
        case '发起拼团':
            baseItem.action = () => console.log('发起拼团:', message)
            break
        case '京东客服转接':
            baseItem.action = () => console.log('京东客服转接:', message)
            break
        case '查看价格走势':
            baseItem.action = () => console.log('查看价格走势:', message)
            break
        case '议价':
            baseItem.action = () => console.log('议价:', message)
            break
        case '发起议价':
            baseItem.action = () => console.log('发起议价:', message)
            break
        case '查看卖家信誉':
            baseItem.action = () => console.log('查看卖家信誉:', message)
            break
        default:
            baseItem.action = () => console.log('未知操作:', label, message)
    }

    return baseItem
}

function getMessageCacheKey(conversationId: string, platformId: string) {
    return `${platformId}_${conversationId}`
}

async function loadPlatformEmojiData(platformType: string) {
    const platformCode = platformEmojiPathMapping[platformType]
    if (!platformCode) {
        logger.error(`未找到platformName为${platformType}的平台代码`)
        currentPlatformEmojiList.value = []
        emojiShortCutPathMapping.value = {}
        return
    }
    logger.info(`正在加载${platformType}平台的Emoji`)
    try {
        const emojiModule = await import(`~/assets/emoji/${platformCode}.json`)
        currentPlatformEmojiList.value = (emojiModule.default || emojiModule) as EmojiInfo[]
        emojiShortCutPathMapping.value = {}
        currentPlatformEmojiList.value.forEach(t => t.children.forEach(f => emojiShortCutPathMapping.value[f.shortCut] = f.src))
        logger.info(`${platformType}平台的Emoji已加载完成`)
    } catch (e) {
        logger.error(`加载表情包'${platformCode}.json'失败`, e)
        currentPlatformEmojiList.value = []
        emojiShortCutPathMapping.value = {}
    }
}

watch(() => props.agentClientApi, (newApi, oldApi) => {
    if (newApi && newApi !== oldApi) {
        newApi.onResponseForward<PageData<BackendMessage>>('CUSTOMER_QUERY_HISTORY_MSG', handleHistoryMessageResponse)
        newApi.onResponseForward('CUSTOMER_SEND_NEW_MSG', handleSendMessageResponse)
    }
}, { immediate: true })

// 监听当前活跃联系人的变化
watch(() => props.activeContact, (newContact, oldContact) => {
    // 保存上一个联系人的浏览位置
    if (oldContact && props.platformInfo) {
        saveCurrentViewPosition(oldContact)
    }

    // 重置按钮点击状态、滚动触发器，并记录进入时间
    lastViewButtonClicked.value = false
    enterTime.value = Date.now()
    scrollTrigger.value = Date.now()

    // 清理缓存，保持内存使用在合理范围内
    cleanupCache()

    logger.info('联系人发生改变', {
        newContact,
        activePlatform: props.platformInfo,
        currentSubAccount: props.currentSubAccount
    })
    if (newContact) {
        const cacheKey = getMessageCacheKey(String(newContact.conversationId), String(props.platformInfo?.id))
        logger.info(`为该联系人生成的本地换成key --> ${cacheKey}`)
        if (!conversationCache.value[cacheKey]) {
            conversationCache.value[cacheKey] = {
                list: [],
                hasMore: true,
                loadingSort: null,
                scrollRestoreInfo: undefined,
                lastViewPosition: undefined,
                lastAccessTime: Date.now()
            }
            // 获取消息，滚动逻辑在 handleHistoryMessageResponse 中处理
            fetchMessages(newContact.contactId, newContact.conversationId, 'desc')
        } else {
            // 如果已有缓存的消息，立即滚动到底部
            nextTick(() => {
                const container = messageListContainer.value
                if (container) {
                    container.scrollTop = container.scrollHeight
                    logger.info(`切换到已缓存的联系人，已滚动到底部, currentScrollTop: ${container.scrollTop}, scrollHeight: ${container.scrollHeight}, clientHeight: ${container.clientHeight}`)
                }
            })
        }
    }

    // 切换联系人时清空输入框
    if (chatAreaInstance.value) {
        (chatAreaInstance.value as any).clear()
    }
}, { immediate: true })

watch(() => props.platformInfo, async (newPlatformInfo) => {
    logger.info('当前平台发生改变', { newPlatformInfo })
    if (newPlatformInfo) {
        const { type } = newPlatformInfo
        loadPlatformEmojiData(type)
    }
}, { immediate: true })
</script>

<template>
    <div class="flex flex-col h-full bg-white dark:bg-black">
        <div ref="messageListContainer"
            class="h-[75%] p-4 overflow-y-auto bg-slate-50 dark:bg-slate-900/50 message-list-container">
            <div class="flex justify-center">
                <div v-if="activeContactState.loadingSort === 'desc'" class="my-4">
                    <Icon name="lucide:loader-2" class="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
                <button v-else-if="!activeContactState.hasMore && activeContactMessages.length > 0"
                    class="text-xs text-muted-foreground my-4" disabled>
                    没有更多历史消息了
                </button>
                <button v-else-if="activeContactState.hasMore && activeContactMessages.length > 0"
                    class="text-xs text-primary my-4" @click="handleLoadMore">
                    加载更多历史消息
                </button>
            </div>

            <div v-if="props.activeContact && activeContactMessages.length">
                <div v-for="message in activeContactMessages" :key="message.platformMessageId"
                    class="flex flex-col gap-2 mb-4">
                    <div class="flex items-start gap-3"
                        :class="{ 'flex-row-reverse': message.sender.userId === props.currentSubAccount.accountId }">
                        <Avatar class="h-8 w-8">
                            <AvatarImage :src="String(message.sender.avatar)" :alt="message.sender.displayName" />
                            <AvatarFallback>{{
                                message.sender.displayName ? message.sender.displayName.substring(0, 2) :
                                    message.sender.userId.substring(0, 2)
                                }}
                            </AvatarFallback>
                        </Avatar>
                        <div class="max-w-[70%]"
                            :class="{ 'items-end': message.sender.userId === props.currentSubAccount.accountId, 'items-start': message.sender.userId !== props.currentSubAccount.accountId, 'flex flex-col': true }">
                            <div class="text-xs text-muted-foreground mb-1"
                                :class="{ 'self-end': message.sender.userId === props.currentSubAccount.accountId }">
                                {{ message.sender.displayName }} {{ formatHumanTime(message.sendTimeValue, true) }}
                            </div>
                            <ContextMenu>
                                <ContextMenuTrigger class="p-0">
                                    <MessageRenderer :message="message" />
                                </ContextMenuTrigger>

                                <!--右键点击消息-->
                                <ContextMenuContent>
                                    <ContextMenuItem v-for="item in getContextMenuItems(message)" :key="item.label"
                                        :class="{ 'text-red-600': item.danger }" @click="item.action">
                                        {{ item.label }}
                                    </ContextMenuItem>
                                </ContextMenuContent>
                            </ContextMenu>

                            <!-- 错误消息显示 -->
                            <div v-if="message.errorResponseMessage"
                                class="mt-2 animate-in slide-in-from-top-1 duration-300">
                                <div
                                    class="inline-flex items-center gap-1.5 px-2 py-2 bg-white dark:bg-gray-800 border-l-3 border-red-500 rounded-r-md shadow-sm ring-1 ring-red-100 dark:ring-red-900/20">
                                    <div class="flex-shrink-0">
                                        <Icon name="heroicons:exclamation-triangle" class="w-3 h-3 text-red-500" />
                                    </div>
                                    <span class="text-xs text-red-600 dark:text-red-400 font-medium flex-1">{{
                                        message.errorResponseMessage
                                        }}</span>
                                    <!-- 重试按钮 -->
                                    <button v-if="message.errorRetryAction"
                                        class="flex-shrink-0 inline-flex items-center gap-1 px-2 py-1 text-xs text-red-600 dark:text-red-400 cursor-pointer"
                                        @click="message.errorRetryAction(message)">
                                        <Icon name="lucide:rotate-ccw" class="w-2.5 h-2.5" />
                                        重试
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class="flex items-center justify-center h-full">
                <p class="text-muted-foreground">
                    选择一个会话开始聊天
                </p>
            </div>
        </div>

        <!-- 回到上次浏览位置按钮 -->

        <div class="h-[25%] flex flex-col border-t p-2 bg-white dark:bg-black relative">
            <!-- 回到上次浏览位置按钮 -->
            <div v-if="shouldShowBackToLastViewButton"
                class="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-full z-10 flex justify-center pb-2">
                <button
                    class="inline-flex items-center justify-center gap-1 px-3 py-1.5 text-xs text-primary bg-white dark:bg-gray-800 border border-primary/20 hover:border-primary/40 rounded-md transition-all duration-200 cursor-pointer shadow-lg"
                    @click="backToLastViewPosition">
                    <Icon name="lucide:arrow-up" class="w-3 h-3" />
                    回到上次浏览位置
                </button>
            </div>

            <div v-if="messageToReply"
                class="px-2 py-1 text-sm bg-muted rounded-md mb-2 flex justify-between items-center">
                <div>
                    <span class="text-muted-foreground">回复 {{ messageToReply.sender.displayName }}: </span>
                    <span class="line-clamp-1" v-html="messageToReply.content" />
                </div>
                <Button variant="ghost" size="icon" class="h-6 w-6" @click="cancelReply">
                    <Icon name="lucide:x" class="h-4 w-4" />
                </Button>
            </div>

            <div class="flex items-center gap-2 mb-2 px-1">
                <Popover>
                    <PopoverTrigger as-child>
                        <Button variant="ghost" size="icon">
                            <Icon name="lucide:smile" class="h-5 w-5" />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-full max-w-sm p-0">
                        <Tabs default-value="表情" class="w-full">
                            <TabsList class="grid w-full grid-cols-2">
                                <TabsTrigger v-for="cat in currentPlatformEmojiList" :key='cat.label'
                                    :value="cat.label">
                                    {{ cat.label }}
                                </TabsTrigger>
                            </TabsList>
                            <TabsContent v-for="cat in currentPlatformEmojiList" :key="cat.label" :value="cat.label"
                                class="p-2">
                                <div class="grid grid-cols-8 gap-2 max-h-60 overflow-y-auto">
                                    <button v-for="emoji in cat.children" :key="emoji.shortCut"
                                        class="p-1 rounded-md hover:bg-muted" @click="insertEmoji(emoji)">
                                        <img :src="emoji.src" :alt="emoji.meaning" class="w-6 h-6">
                                    </button>
                                </div>
                            </TabsContent>
                        </Tabs>
                    </PopoverContent>
                </Popover>
                <Button variant="ghost" size="icon" @click="handleSendFile">
                    <Icon name="lucide:paperclip" class="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon" @click="handleSendImage">
                    <Icon name="lucide:image" class="h-5 w-5" />
                </Button>
            </div>

            <div ref="chatElm" class="chat-elm w-full flex-1 border-none" />
            <input ref="fileInput" type="file" style="display: none" @change="onFileSelected">
        </div>
    </div>
</template>

<style>
.chat-elm {
    background: transparent;
    height: 100%;
    min-height: 90px;
    outline: none;
    border: none;
}

.chat-elm:focus-within {
    box-shadow: none;
}

.chat-elm .chat-placeholder-wrap {
    color: hsl(var(--muted-foreground));
    padding: 0.5rem 0.75rem;
}

.chat-elm .send-btn-wrap {
    display: none;
}

.chat-elm .rich-text-area {
    padding: 0.5rem 0.75rem;
    color: hsl(var(--foreground));
}

.rich-text-area div {
    display: inline;
}

.rich-text-area img {
    display: inline-block;
    vertical-align: bottom;
}

.chat-elm .at-user {
    color: hsl(var(--primary));
}

.message-list-container img {
    max-width: 100%;
    border-radius: 0.5rem;
    vertical-align: middle;
}

.reply-content {
    background-color: rgba(128, 128, 128, 0.1);
    padding: 6px 10px;
    border-radius: 6px;
    margin-bottom: 6px;
    border-left: 2px solid hsl(var(--primary));
    font-size: 0.8rem;
}

.reply-content .reply-user {
    font-weight: 600;
    margin-bottom: 4px;
}

.reply-content .reply-text {
    opacity: 0.8;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
</style>

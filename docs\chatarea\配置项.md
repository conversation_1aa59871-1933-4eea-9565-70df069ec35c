配置项
实例化ChatArea类时的配置参数
elm
描述：基于 目标元素下 创建聊天框元素
类型值：HTMLElement
是否为必传：true
<script>
    const chat = new ChatArea({
        elm: document.getElementById('elmId')
    })
</script>
device
描述：配置强行启用PC还是H5的交互方式
可选值：pch5auto
是否为必传：false
默认值：auto
支持版本：5.3.6+
<script>
    const chat = new ChatArea({
        device: 'auto'
    })
</script>
placeholder
描述：聊天框提示输入语
类型值：String
是否为必传：false
默认值：''
如果不需要提示语功能，该项参数不传即可
<script>
    const chat = new ChatArea({
        placeholder: '请输入'
    })

    // 更新提示语
    chat.updateConfig({
        placeholder: '新的提示语'
    })
</script>
autoFocus
描述：配置聊天框生成后是否进行自动聚焦行为
类型值：Boolean
是否为必传：false
默认值：true
支持版本：5.4.1+
<script>
    const chat = new ChatArea({
        autoFocus: true
    })
</script>
userList
描述：聊天成员集合
类型值：Array
是否为必传：false
默认值：[]
推荐数据要支持pinyin 这对用户交互上的体验更加友好
如现有业务未支持，可暂由前端支持，前端强大的中文转拼音库pinyin-pro
﻿

请试着在唤起@人员弹窗后继续输入英文 比如@sm
<script>
    const chat = new ChatArea({
        userList: [
            {
                id: '1' // 唯一标识
                name: '松松', // 人员姓名
                avatar: '', // 人员头像，为空时将用用户姓名后二位字符作为作头像展示
                pinyin: 'song song' // 用户中文拼音， 数据一旦支持该项将开启强大的拼音检索功能
            },
            {
                id: '2',
                name: '寒松鸣',
                pinyin: 'han song ming'
            }
        ]
    })

    // 更新成员列表
    chat.updateConfig({
        userList: [
            { id: '3', name: '张三', pinyin: 'zhang san' }
        ]
    })
</script>
userProps
描述：转接库人员参数值与实际业务的差异
类型值：Object
是否为必传：false
默认值：null
<script>
    // 现有数据
    const userList = [
        {
            userId: '1',
            userName: '松松',
            orgId: '11',
            orgName: '研发部',
            city: '杭州市',
            user_avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
            user_pinyin: 'song song'
        }
    ]

    const chat = new ChatArea({
        userList,
        userProps: { // 只要将库所需要的值进行转接即可
            id: 'userId',
            name: 'userName',
            avatar: 'user_avatar',
            pinyin: 'user_pinyin'
        }
    })

    // 更新成员转接值列表
    chat.updateConfig({
        userProps: {
            id: 'key1',
            name: 'key2',
            avatar: 'key3',
            pinyin: 'key4'
        }
    })
</script>
maxLength
描述：设置聊天框内容最大输入长度限制
类型值：Number
是否为必传：false
默认值：null
支持版本：4.6.1+
如果业务没有明确需要限制，就不要开启该配置项，以减少不要的性能开销
开启该配置项后，可通过访问chat.textLength实时获取当前聊天框内容长度
<script>
    const chat = new ChatArea({
        maxLength: 1000 // 设置聊天框内容最大输入长度限制
    })

    // 更新输入长度限制
    chat.updateConfig({
        maxLength: 2000
    })

    // 实时获取当前聊天框输入文本长度
    chat.addEventListener('operate', () => {
        console.log(chat.textLength)
    })
</script>
customTrigger
描述：配置自定义触发符号以及数据选择列表
类型值：Array
是否为必传：false
默认值：null
支持版本：PC4.8.0+
该交互弹窗的数据列表同样也支持拼音检索功能
配置项参数中prefix是唯一标识值，不可重复定义相同的prefix
﻿

请试着敲击#号和$号
<script>
    const chat = new ChatArea({
        customTrigger: [
          {
            dialogTitle: '群聊话题' // 弹窗标题
            prefix: '#', // 自定义触发符号
            tagList: [ // 数据选择列表
              { id: '1', name: '股票趋势', pinyin: 'gu piao qu shi' }, // 在同一组的tagList下 id请确保是唯一关键值
              { id: '2', name: '新闻娱乐', pinyin: 'xin wen yu le' },  // name将作为选择列表展示的内容
              { id: '3', name: '游戏趣闻', pinyin: 'you xi qu wen' } // pinyin不是非必传值，启用后将支持拼音匹配功能
            ]
          },
          {
            dialogTitle: 'A股话题',
            prefix: '$',
            tagList: [
              { id: '1', name: '令人震惊', pinyin: 'ling ren zhen jing' },
              { id: '2', name: '不可思议', pinyin: 'bu ke si yi' },
              { id: '3', name: '难以置信', pinyin: 'nan yi zhi xin' }
            ]
          }
        ]
    })
</script>
selectList
描述：配置聊天框选择元素交互的数据项
类型值：Array
是否为必传：false
默认值：null
支持版本：PC5.3.0+
配置项参数中key是唯一标识值，不可重复定义相同的key
﻿

本例示例仿照类似豆包的选择元素
<script>
    const chat = new ChatArea({
        selectList: [
          {
            dialogTitle: '风格' // 弹窗标题
            key: 'style', // 唯一标识值
            options: [ // 弹窗下拉选项
              { id: '1', name: '人像摄影', preview: 'url' }, // name：选择项文案 preview：预览图
              { id: '2', name: '电影写真', preview: 'url' },
              { id: '3', name: '中国风', preview: 'url' },
              { id: '4', name: '动漫', preview: 'url' },
              { id: '5', name: '3D渲染', preview: 'url' },
              { id: '6', name: '赛博朋克', preview: 'url' }
            ]
          }
        ],
    })

    // 对应按钮点击触发下拉选择弹窗 可以调用api showPCSelectDialog
    const onDemo = () => {
        chat.showPCSelectDialog(
            'style', // 这里传入配置项 selectList 集合中你想唤起对应的选择列的key
            document.getElementById('elmId') // 这里传入 弹窗位置基于哪个元素显示
        )
    }
</script>
asyncMatch
描述：开启异步加载人员列表渲染
类型值：Boolean
是否为必传：false
默认值：false
支持版本：PC 5.0.0+H5 5.0.4+
当开启该配置项后，配置项userList已无需传入，并且获取聊天框@人员数据API应该使用getCallUserTagList而不是getCallUserList
异步匹配模式将关闭多选功能
﻿

本示例接口查询的是github上的用户列表，请随意在@字符后输入点什么吧
<script>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import ChatArea from 'chatarea'
import 'chatarea/lib/ChatArea.css'


let chat
const chatElm = ref(null)

onMounted(() => {
  chat = new ChatArea({
    elm: chatElm.value,
    placeholder: '本示例接口查询的是github上的用户列表，请随意在@字符后输入点什么吧',
    asyncMatch: true
    // 转接github的用户列表
    userProps: {
      name: 'login',
      avatar: 'avatar_url'
    }
  })

  // 接管匹配
  chat.addEventListener('atMatch', async (str) => {
    // 阻止空字符请求
    if (!str) return []
    // 异步请求获取人员列表信息
    const res = await fetch('https://api.github.com/search/users?q=' + str).then(res => res.json())
    // 返回需要渲染的人员列表
    return res?.items ?? []
  })
})

onBeforeUnmount(() => {
  chat && chat.dispose()
})
</script>
dialogLabels
描述：修改内置交互弹窗展示的文案
类型值：Object
是否为必传：false
默认值：null
支持版本：5.2.0+
<script>
    // 按需传入替换的文案即可 可以不需要传入完整的配置对象
    const chat = new ChatArea({
        dialogLabels: {
            // PC人员光标选择弹窗
            pcPointDialog: {
                title: '群成员',
                callEveryLabel: '所有人'
                checkLabel: '多选',
                emptyLabel: '暂无数据'
            },
            // PC人员多选选择弹窗
            pcPCheckDialog: {
                title: '选择要@的人',
                searchPlaceholder: '搜素人员名称',
                searchEmptyLabel: '没有匹配到任何结果',
                userTagTitle: '研讨成员列表',
                checkAllLabel: '全选',
                checkEmptyLabel: '请选择需要@的成员',
                confirmLabel: '确定',
                cancelLabel: '取消'
            },
            // H5人员选择弹窗
            h5Dialog: {
                title: '选择提醒的人',
                callEveryLabel: '所有人'
                searchPlaceholder: '搜素人员名称',
                searchEmptyLabel: '没有匹配到任何结果',
                confirmLabel: '确定',
                cancelLabel: '收起'
            }
        }
    })
</script>
needDialog
描述：是否需要库提供的默认交互弹框功能
类型值：Boolean
是否为必传：false
默认值：true
支持版本：3.7.3+
<script>
    const chat = new ChatArea({
        needDialog: false // 关闭默认交互弹窗
    })
</script>
needCallEvery
描述：是否需要 @所有人 功能
类型值：Boolean
是否为必传：false
默认值：true
<script>
    const chat = new ChatArea({
        needCallEvery: true
    })

    // 更新是否需要 @所有人 功能
    chat.updateConfig({
        needCallEvery: false
    })
</script>
needDebounce
描述：配置是否对聊天框频繁触发的交互点添加防抖函数
类型值：Boolean
是否为必传：false
默认值：true
支持版本：4.9.3+
<script>
    // 关闭该配置项后 交互在视觉上会更加流畅但是会增加性能的开销 这将取决于你对业务场景评估的取舍
    const chat = new ChatArea({
        needDebounce: false // 关闭使用防抖函数
    })
</script>
callEveryLabel
描述：修改 @所有人 展示文案
类型值：String
是否为必传：false
默认值：所有人
支持版本：4.2.0+&&< 5.2.0
该配置项已在5.2.0+版本废除，请使用新的配置项dialogLabels
<script>
    const chat = new ChatArea({
        callEveryLabel: '全体成员' // 修改@所有人展示文案
    })
</script>
needCallSpace
描述：是否需要像微信一样在 @标签 后面衔接一个空格之间的距离
类型值：Boolean
是否为必传：false
默认值：false
<script>
    const chat = new ChatArea({
        needCallSpace: true
    })

    // 更新是否需要 @标签 衔接空格功能
    chat.updateConfig({
        needCallSpace: false
    })
</script>
wrapKeyFun
描述：自定义折行键 触发方式
类型值：Function
是否为必传：false
默认值：(event) => event.ctrlKey && ['Enter'].includes(event.key)
如果修改了该默认配置项 请最好同步修改sendKeyFun配置项
该配置项仅对PC端生效 H5端虚拟键盘回车统一为换行操作sendKeyFun配置项也是如此
<script>
    const chat = new ChatArea({
        wrapKeyFun: (event) => event.ctrlKey && ['Enter'].includes(event.key) // 默认为ctrl + enter折行
    })

   // 更新折行键
   chat.updateConfig({
        wrapKeyFun: (event) => event.shiftKey && ['Enter'].includes(event.key)
    })
</script>
sendKeyFun
描述：自定义发送键 触发方式
类型值：Function
是否为必传：false
默认值：(event) => !event.ctrlKey && ['Enter'].includes(event.key)
<script>
    const chat = new ChatArea({
        sendKeyFun: (event) => !event.ctrlKey && ['Enter'].includes(event.key)
    })

   // 更新发送键
   chat.updateConfig({
        sendKeyFun: (event) => !event.shiftKey && ['Enter'].includes(event.key)
    })
</script>
copyType
描述：定义聊天框支持的粘贴类型
可选值：['text', 'image']
是否为必传：false
默认值：['text']
<script>
    const chat = new ChatArea({
        copyType: ['text'] // 仅允许文本粘贴
        // copyType: [], // 不允许粘贴
        // copyType: ['text', 'image'], // 允许文本和图片粘贴
    })

    // 更新粘贴类型
    chat.updateConfig({
        copyType: ['text', 'image']
    })
</script>
uploadImage
描述：当允许聊天框粘贴图片时，将图片粘贴的上传到服务器的方法
类型值：Function
是否为必传：false
默认值：默认将图片转成base64渲染
<script>
    const chat = new ChatArea({
        uploadImage: async (file) => {
           const res = await youUploadFileApi(file)
           // 需要返回img src的地址
           return res.data
        }
    })

    // 更新上传图片方法
    chat.updateConfig({
        uploadImage: async (file) => {
           const res = await youUploadFileApi(file)
           // 需要返回img src的地址
           return res.data
        }
    })
</script>
Contributors：
JianFv
Last Updated：
2024/11/25 15:20
快速开始
API

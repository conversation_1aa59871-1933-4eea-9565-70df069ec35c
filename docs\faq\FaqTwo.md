# FaqTwo 组件使用文档

`FaqTwo` 是一个用于展示常见问题解答 (FAQ) 的 Vue 组件。它以简单的网格布局直接显示问题和答案列表，不像 `FaqOne` 那样使用可折叠的手风琴样式。组件内容和文本支持国际化。

## Props (属性)

`FaqTwo` 组件目前没有定义可接受的自定义 props。它的行为完全由数据配置和插槽驱动。

## 数据配置

`FaqTwo` 组件的内容（包括组件的标题、描述以及FAQ列表）与 `FaqOne` 组件一样，主要由 `/config/faqs.ts` 文件驱动。

在该配置文件中，通常会导出一个名为 `faqConfig` 的对象，其结构符合 `FaqConfig` 类型：

```typescript
interface FaqItemContent {
  question: I18nTextMap; // 问题文本，支持国际化
  answer: I18nTextMap;   // 答案文本，支持国际化
}

interface FaqItem {
  image?: string;          // 与此FAQ项关联的图片URL (可选，但FaqTwo默认不显示图片)
  content: FaqItemContent; // FAQ项的内容
}

interface FaqConfig {
  title: I18nTextMap;       // 组件的整体标题，支持国际化
  description: I18nTextMap; // 组件的整体描述，支持国际化
  items: FaqItem[];         // FAQ项目列表
}

// I18nTextMap 示例:
// {
//   en: "English Text",
//   zh: "中文文本"
// }
```

组件内部会使用 `getLocalizedConfigText` 工具函数根据当前i18n语言环境自动加载对应的文本。

## 插槽 (Slots)

为了提供更高级的自定义能力，`FaqTwo` 组件提供了以下插槽：

-   **`before-main-content`**: 在组件顶层容器的内部、主要内容渲染之前插入内容。
    -   **作用域数据**:
        -   `faqConfig: FaqConfig` (完整的FAQ配置对象)
-   **`header`**: 替换组件默认的标题和描述区域。
    -   **作用域数据**:
        -   `title: string` (已本地化的组件标题)
        -   `description: string` (已本地化的组件描述)
-   **`before-faq-list`**: 在FAQ列表的 `<dl>` 元素之前插入内容。
-   **`faq-item` (作用域插槽)**: 自定义FAQ列表中每个条目（问题和答案对）的渲染。
    -   **作用域数据**:
        -   `faqItem: FaqItem` (当前FAQ项的原始数据对象)
        -   `localizedQuestion: string` (已本地化的当前FAQ项的问题文本)
        -   `localizedAnswer: string` (已本地化的当前FAQ项的答案文本)
-   **`after-main-content`**: 在组件顶层容器的内部、主要内容渲染之后，但在底部的 `SupportForm` 之前插入内容。
    -   **作用域数据**:
        -   `faqConfig: FaqConfig` (完整的FAQ配置对象)

## 基本用法示例

```vue
<template>
  <FaqTwo />
</template>

<script setup lang="ts">
// FaqTwo 组件会自动导入，无需手动引入
</script>
```

## 使用自定义插槽示例

以下示例展示了如何使用 `FaqTwo` 组件的各种插槽来自定义其外观和行为，类似于在 `pages/index.vue` 中的实现：

```vue
<template>
  <FaqTwo>
    <!-- 示例：使用 before-main-content 插槽 -->
    <template #before-main-content="{ faqConfig }">
      <div class="my-4 p-4 bg-secondary rounded-md text-center">
        <p class="text-sm text-secondary-foreground">
          ✨ 当前 FAQ 配置共有 {{ faqConfig.items.length }} 个问题 (来自Slot: before-main-content) ✨
        </p>
      </div>
    </template>

    <!-- 示例：自定义 Header -->
    <template #header="{ title, description }">
      <div class="mx-auto max-w-4xl text-center border-b pb-6 mb-10">
        <h2 class="text-3xl font-bold leading-tight tracking-tight text-primary">
          🧐 {{ title }} (来自Slot: header)
        </h2>
        <p class="mt-3 text-lg text-muted-foreground">
          {{ description }} (来自Slot: header)
        </p>
      </div>
    </template>

    <!-- 示例：使用 before-faq-list 插槽 -->
    <template #before-faq-list>
      <div class="mb-6 text-center">
        <p class="text-base italic text-muted-foreground">
          以下是我们为您精心准备的解答 (来自Slot: before-faq-list)：
        </p>
        <Separator class="my-3"/>
      </div>
    </template>

    <!-- 示例：自定义 faq-item 插槽 -->
    <template #faq-item="{ faqItem, localizedQuestion, localizedAnswer, index }">
      <div class="p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow bg-card">
        <dt class="text-lg font-semibold leading-7 text-card-foreground flex items-start">
          <Icon name="lucide:message-square-question" class="mr-3 h-6 w-6 text-primary flex-shrink-0 mt-1" />
          <span>{{ index + 1 }}. {{ localizedQuestion }} (来自Slot: faq-item)</span>
        </dt>
        <dd class="mt-2 text-base leading-7 text-muted-foreground pl-9">
          {{ localizedAnswer }} (来自Slot: faq-item)
        </dd>
        <Button v-if="faqItem.image" variant="link" size="sm" class="mt-2 pl-9">
          (附图提示: {{ faqItem.image }}) <!-- FaqTwo 默认不显示图片，但数据可能存在 -->
        </Button>
      </div>
    </template>

    <!-- 示例：使用 after-main-content 插槽 -->
    <template #after-main-content="{ faqConfig }">
      <div class="mt-12 p-4 bg-accent rounded-md text-center">
        <p class="text-sm text-accent-foreground">
          没有找到您想要的答案？共有 {{ faqConfig.items.length }} 条FAQ。您可以通过底部的表单联系我们！ (来自Slot: after-main-content)
        </p>
      </div>
    </template>
  </FaqTwo>
</template>

<script setup lang="ts">
import { Icon } from '#components' // 如果在插槽中使用了 Icon
import { Button } from '@/components/ui/button' // 如果在插槽中使用了 Button
import Separator from '@/components/ui/separator/Separator.vue' // 如果在插槽中使用了 Separator
// FaqTwo 组件会自动导入
</script>
```

**注意**: 在插槽中使用像 `<Icon />`, `<Button />`, 或 `<Separator />` 这样的组件时，如果它们不是全局注册或自动导入的，请确保在 `<script setup>` 中导入它们。在当前项目中，这些常用组件通常是自动导入的。 
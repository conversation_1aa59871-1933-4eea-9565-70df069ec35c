import {type ApiResponse, Delete, Get, type PageData, type PageParams, Post, type RequestOptions} from '~/utils/http'
import {message} from '~/composables/useMessage'
import {PageConstant} from '~/utils/constants/pagination'
// 导入i18n工具函数
import {tMsg} from '~/utils/i18n/utils'

/**
 * 通用RESTful CRUD接口封装
 * 使用标准RESTful风格的HTTP方法进行CRUD操作
 */
export function createCrudApi<T = Record<string, any>, R = Record<string, any>>(uri: string) {
    if (!uri) {
        throw new Error(tMsg('api.uri_cannot_be_empty'))
    }

    /**
     * 创建实体
     * @param data 创建数据
     * @param options 请求选项
     * @returns 创建结果
     */
    async function create(data: Partial<T>, options?: RequestOptions): Promise<ApiResponse<R>> {
        if (!data || Object.keys(data).length === 0) {
            message.error(tMsg('api.create_data_cannot_be_empty'))
            return Promise.reject({code: 400, message: tMsg('api.create_data_cannot_be_empty'), data: null})
        }
        return Post<R>(`${uri}/insert`, data, options)
    }

    /**
     * 更新实体
     * @param data 更新数据
     * @param options 请求选项
     * @returns 更新结果
     */
    async function update(data: Partial<T>, options?: RequestOptions): Promise<ApiResponse<void>> {
        if (!data || Object.keys(data).length === 0) {
            message.error(tMsg('api.update_data_cannot_be_empty'))
            return Promise.reject({code: 400, message: tMsg('api.update_data_cannot_be_empty'), data: null})
        }
        return Post(`${uri}/update`, data, options)
    }

    /**
     * 获取单个实体
     * @param params 查询参数
     * @param options 请求选项
     * @returns 实体数据
     */
    async function get(params: Partial<T>, options?: RequestOptions): Promise<ApiResponse<R>> {
        if (!params || Object.keys(params).length === 0) {
            message.error(tMsg('api.id_cannot_be_empty'))
            return Promise.reject({code: 400, message: tMsg('api.id_cannot_be_empty'), data: null})
        }
        return Get<R>(`${uri}/queryOne`, params, options)
    }

    /**
     * 分页查询实体列表
     * @param params 分页查询参数
     * @param options 请求选项
     * @returns 分页数据
     */
    async function page(params: PageParams & Partial<T>, options?: RequestOptions): Promise<ApiResponse<PageData<R>>> {
        if (!params) {
            message.error(tMsg('api.pagination_params_cannot_be_empty'))
            return Promise.reject({code: 400, message: tMsg('api.pagination_params_cannot_be_empty'), data: null})
        }

        // 验证并设置默认分页参数
        const pageParams = {
            pageNum: params.pageNum ?? PageConstant.PAGE_NUM,
            pageSize: params.pageSize ?? PageConstant.PAGE_SIZE,
            ...params
        }

        return Get<PageData<R>>(`${uri}/pageList`, pageParams, options)
    }

    /**
     * 根据id删除实体
     * @param condition 参数
     * @param options 请求选项
     * @returns 删除结果
     */
    async function remove(condition: Partial<T>, options?: RequestOptions): Promise<ApiResponse<void>> {
        if (!condition || Object.keys(condition).length === 0) {
            message.error(tMsg('api.delete_id_cannot_be_empty'))
            return Promise.reject({code: 400, message: tMsg('api.delete_id_cannot_be_empty'), data: null})
        }
        return Delete(`${uri}/delete`, condition, options)
    }

    /**
     * 批量删除实体
     * @param ids 实体ID数组
     * @param options 请求选项
     * @returns 批量删除结果
     */
    async function batchRemove(ids: Array<string | number>, options?: RequestOptions): Promise<ApiResponse<void>> {
        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            message.error(tMsg('api.batch_delete_ids_cannot_be_empty'))
            return Promise.reject({code: 400, message: tMsg('api.batch_delete_ids_cannot_be_empty'), data: null})
        }
        return Delete(`${uri}/batchDelete`, {ids}, options)
    }

    return {
        create,
        update,
        get,
        page,
        remove,
        batchRemove
    }
}

/**
 * 创建泛型RESTful CRUD API客户端
 * @param entityName 实体名称
 * @returns CRUD API客户端
 * @example
 * // 创建用户CRUD API
 * const userApi = createEntityApi<User>('users');
 * // 分页查询用户
 * const { data } = await userApi.page({ pageNum: 1, pageSize: 10 });
 */
export function createEntityApi<T = Record<string, any>, R = Record<string, any>>(entityName: string) {
    if (!entityName) {
        throw new Error(tMsg('api.entity_name_cannot_be_empty'))
    }

    const uri = `/${entityName}`

    return createCrudApi<T, R>(uri)
}

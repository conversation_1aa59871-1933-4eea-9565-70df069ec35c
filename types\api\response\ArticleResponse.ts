
/**
 * 博客文章视图类 <br/>
 */
export interface ArticleResponse {

    /**
     * 主键
     */
    id?: number

    /**
     * 文章标题
     */
    title?: string

    /**
     * 文章别名
     */
    slug?: string;

    /**
     * 封面
     */
    cover?: string;

    /**
     * 内容
     */
    content?: string

    /**
     * 发布状态
     */
    status?: boolean

    /**
     * 文章简介
     */
    summary?: string;

    /**
     * 关键词
     */
    keyWords?: string;

    /**
     * 语言
     */
    language?: string;

    /**
     * 创建时间
     */
    createTime?: Date

    /**
     * 创建时间
     */
    updateTime?: Date,

    /**
     * 评论数
     */
    commentCount?: number

}
import type {FaqConfig} from '~/types/site/faq'

const faqsConfig: FaqConfig = {
    title: {
        en: 'Frequently asked questions',
        zh: '常见问题'
    },
    description: {
        en: 'Can\'t find the answer you\'re looking for? Reach out to our',
        zh: '找不到您想要的答案？请联系我们的'
    },
    items: [
        {
            image: '/img/faq/11.png',
            content: {
                question: {
                    en: 'How do you make holy water?',
                    zh: '如何制作圣水？'
                },
                answer: {
                    en: 'You boil the hell out of it. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.',
                    zh: '把它煮沸就行了。Lorem ipsum dolor sit amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.'
                }
            }
        },
        {
            image: null,
            content: {
                question: {
                    en: 'What\'s the best thing about Switzerland?',
                    zh: '瑞士最棒的地方是什么？'
                },
                answer: {
                    en: 'I don\'t know, but the flag is a big plus. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.',
                    zh: '我不知道，但他们的国旗是个大大的加号。Lorem ipsum dolor sit amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.'
                }
            }
        },
        {
            image: '/img/faq/12.png',
            content: {
                question: {
                    en: 'What do you call someone with no body and no nose?',
                    zh: '没有身体也没有鼻子的人叫什么？'
                },
                answer: {
                    en: 'Nobody knows. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.',
                    zh: '没人知道（Nobody knows）。Lorem ipsum dolor sit amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.'
                }
            }
        }
    ]
}

export default faqsConfig
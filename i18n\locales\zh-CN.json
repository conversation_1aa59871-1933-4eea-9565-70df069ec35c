{"nav": {"sign_in": "登录"}, "theme": {"theme_light": "Light", "theme_dark": "Dark", "theme_system": "System"}, "auth": {"login_terms": "点击继续，即表示您同意我们的", "login_terms_service": "服务条款", "login_terms_privacy": "隐私政策", "unauthenticated_redirect": "需要进行认证。正在跳转到登录页面...", "codeSentSuccess": "验证码已发送", "signupSuccessfulPleaseLogin": "注册成功！请使用您的新密码登录。", "login_successful_welcome_back": "登录成功，欢迎回来！", "login_fetch_user_info_incomplete": "登录成功，但获取用户信息不完整", "code_sent_failed": "验证码发送失败，请重试", "code_sent_success": "验证码发送成功", "message_password_reset_success": "密码重置成功", "message_password_reset_failed": "密码重置失败", "labels": {"username": "用户名", "email": "邮箱", "password": "密码", "loginText": "登录", "signOut": "退出登录", "resendCodeBtn": "重新发送", "sendingCode": "发送中...", "resendCodeCooldown": "{seconds}秒后重发", "backBtn": "返回", "emailVerificationCode": "邮箱验证码", "enterCodeSentTo": "请输入发送至以下邮箱的验证码：", "registerWithEmailCodeLink": "通过邮箱验证码注册", "loginWithEmailCodeLink": "邮箱验证码登录", "backToEmailInput": "返回邮箱输入", "accountExistNoPasswordUseCode": "此账户存在但未设置密码，请使用验证码登录。", "resetSubmitting": "提交中...", "signupBtn": "注册", "setPasswordForNewAccount": "为您的新账户设置密码", "loginForgotPassword": "忘记密码？", "loadingAuthOptions": "正在加载认证列表...", "loginShowLessOptions": "展示更少", "loginMoreOptions": "更多登录项", "loginOrContinue": "或继续使用", "loadingChecking": "检查中...", "continueBtn": "继续", "reset_back_login": "返回登录", "status_resetting_password": "重置中...", "reset_password": "重置密码", "reset_new_password": "新密码", "email_verification_code": "邮箱验证码", "get_code_btn": "获取验证码", "reset_password_mismatch": "两次输入的密码不一致", "reset_confirm_btn": "确认", "reset_confirm_password": "确认新密码", "reset_new_subtitle": "请输入您的新密码", "reset_enter_email": "输入您的邮箱以接收验证码", "signup_have_account": "已有账户？", "signup_create_account": "创建账户", "login_welcome_back": "欢迎回来"}, "placeholders": {"password": "请输入密码", "username": "请输入你的用户名", "email": "请输入您的邮箱", "new_password_min_length": "输入新密码（至少6位）", "verification_code": "输入验证码"}, "api": {"fetchUserInfoIncomplete": "获取到的用户信息不完整"}}, "error": {"home": "首页", "support": "技术支持", "server_error": "服务器错误", "this_page_doesnt_exist": "此页面不存在"}, "support": {"title": "提交支持请求", "description": "我们随时为您提供帮助。请告诉我们您遇到的问题。", "type": "请求类型", "select_type": "选择请求类型", "type_feedback": "反馈建议", "type_error": "错误报告", "type_required": "请选择请求类型", "description_label": "描述", "description_placeholder": "详细描述您遇到的问题或反馈...", "description_required": "请提供描述", "send": "发送请求", "sending": "发送中...", "success": "感谢您的反馈！我们会尽快回复您。"}, "optional": "选填", "confirm": {"default_title": "提示", "success_title": "成功", "warning_title": "警告", "info_title": "信息", "error_title": "错误", "confirm_button": "确定", "cancel_button": "取消", "delete_button": "删除"}, "notification": {"success": "成功", "warning": "警告", "info": "通知", "error": "错误"}, "email": {"email_invalid": "请输入一个有效的邮箱", "text": "邮箱", "warn_empty": "请输入邮箱地址", "warn_invalid_format": "邮箱格式不正确"}, "pagination": {"previous": "上一页", "next": "下一页", "first": "首页", "last": "末页"}, "blog": {"back_to_blog": "返回博客列表", "comments": "评论", "comment_count": "条评论", "no_comments": "还没有评论，快来发表第一条评论吧！", "write_comment": "写下你的评论...", "reply_to": "回复", "post_comment": "发布评论", "load_more": "加载更多评论", "all_comments_loaded": "已显示全部评论", "reading_time": "分钟阅读", "published_on": "发布于", "article_not_found": "文章未找到", "download": "下载为Markdown", "page": {"recommended_reading": "推荐阅读", "article_not_found_title": "文章未找到", "siteTitle": "博客", "listSiteDescription": "探索我们关于技术和开发的最新文章和见解。", "articleNotFoundSiteTitle": "抱歉，您请求的文章不存在。", "no_recommendations": "没有找到相关推荐文章"}, "blogPreview": {"title": "博客精选", "subtitle": "了解最新的行业动态与专业观点"}}, "validation": {"email": "请输入有效的邮箱地址", "username": "用户名不合法", "password": "密码不合法", "error_password_min_length": "密码长度至少为{length}位", "error_code_min_length": "验证码至少为{length}位"}, "newsletter": {"subscribeSuccess": "订阅成功", "subscribeError": "订阅失败", "contact_support": "联系支持"}, "clipboard": {"emptyContent": "复制的内容不能为空", "browserUnSupport": "您的浏览器不支持复制功能", "copySuccess": "复制成功", "copyFailure": "复制失败"}, "common": {"loading": "加载中...", "sort_desc": "按时间降序", "sort_asc": "按时间升序", "submitting": "提交中...", "close": "关闭", "and": "和"}, "api": {"uri_cannot_be_empty": "uri不能为空", "create_data_cannot_be_empty": "创建数据不能为空", "update_id_cannot_be_empty": "更新ID不能为空", "update_data_cannot_be_empty": "更新数据不能为空", "id_cannot_be_empty": "ID不能为空", "pagination_params_cannot_be_empty": "分页参数不能为空", "delete_id_cannot_be_empty": "删除ID不能为空", "batch_delete_ids_cannot_be_empty": "批量删除ID列表不能为空", "entity_name_cannot_be_empty": "实体名称不能为空", "request_params_invalid": "请求参数缺失"}, "captcha": {"concatText": "拖动滑块完成拼图", "imageClickText": "请依次点击", "rotateText": "拖动滑块完成拼图", "sliderText": "拖动滑块完成拼图"}, "http": {"service_unavailable": "服务暂时不可用", "unknown_error": "发生未知错误", "api_base_not_configured": "API基础URL未配置", "request_preparation_error": "请求准备阶段发生错误", "request_failed": "请求失败", "no_response_data": "未收到响应数据", "response_format_unexpected": "响应格式不符合预期", "request_params_must_be_object": "请求参数必须是对象类型"}}
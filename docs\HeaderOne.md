# Header 组件使用文档

`Header` 是一个全局页头组件，用于展示网站的Logo、导航链接、语言切换、主题切换以及用户登录等功能。它具有多种显示模式和丰富的插槽，以适应不同的页面布局和定制需求。

## Props (属性)

| Prop 名称       | 类型                                     | 描述                                                                                                    | 可选值                                | 默认值        |
|:--------------|:---------------------------------------|:------------------------------------------------------------------------------------------------------|:-----------------------------------|:-----------|
| `displayMode` | `'sticky'` \| `'scroll'` \| `'static'` | 控制Header的显示模式：<br>- `sticky`: 始终吸顶显示。<br>- `scroll`: 页面向下滚动时隐藏，向上滚动时显示。<br>- `static`: 正常的静态布局，随页面滚动。 | `'sticky'`, `'scroll'`, `'static'` | `'sticky'` |

## 数据配置

`Header` 组件的部分内容（如网站标题、Logo、导航链接）通常依赖于以下配置文件：

- `~/config/navLinks.ts` (或 `.json`): 定义导航链接的数组，每个链接包含 `href`, `title` (I18nTextMap), `icon` (可选)。
- `~/config/site.ts` (或 `.json`): 定义网站的基本配置，如 `title` (I18nTextMap), `logo` (图片URL)。

组件内部会使用 `getLocalizedConfigText` 工具函数根据当前i18n语言环境自动加载对应的文本。

## 插槽 (Slots)

`Header` 组件提供了以下插槽以实现灵活的定制：

### 主要内容插槽

- **`navbar`**:
    - **描述**: 用于插入主要的导航栏内容，通常会放置如 `PageNavbar` 这样的组件。
    - **作用域数据**: 无。

### PC端特定功能插槽

这些插槽通常用于在 `md` 及以上断点显示的内容。

- **`pc-switch-locale`**:
    - **描述**: 自定义PC端的语言切换区域。默认会显示一个包含地球图标和当前语言名称的按钮，点击后弹出语言选择菜单。
    - **作用域数据**: 无。

- **`pc-switch-theme`**:
    - **描述**: 自定义PC端的主题切换区域。默认会显示一个根据当前主题（亮/暗）变化的图标按钮，点击后弹出主题选择菜单。
    - **作用域数据**: 无。

- **`pc-extra-area` (作用域插槽)**:
    - **描述**: 用于在PC端（通常在主题切换右侧）添加额外的自定义内容，如用户头像、登录/注册按钮等。
    - **作用域数据**:
        - `loginStatus`: `boolean` - 当前用户的登录状态。
        - `currentUser`: `object` - 当前登录用户的信息对象 (包含 `nickname`, `email`, `avatar`)。
        - `handleSignOutFunction`: `() => void` - 执行登出操作的函数。
        - `openLoginDialogFunction`: `() => void` - 打开登录弹窗的函数。

### 移动端特定功能插槽

这些插槽通常用于在 `md` 以下断点通过侧边栏 (Sheet) 显示的内容。

- **`mobile-trigger-sheet`**:
    - **描述**: 自定义用于触发移动端侧边栏（Sheet）显示的按钮。默认是一个汉堡菜单图标按钮。
    - **作用域数据**: 无。

- **`mobile-sheet-content`**:
    - **描述**: 完全自定义移动端侧边栏 (Sheet) 的所有内容。如果使用此插槽，则下面更细粒度的移动端插槽将不会生效，除非你在
      `mobile-sheet-content` 内部再次提供了它们的 `slot` 出口。
    - **作用域数据**: 无。

- **`mobile-sheet-header` (作用域插槽)**:
    - **描述**: 自定义移动端侧边栏的头部区域。默认显示Logo和网站标题。
    - **作用域数据**:
        - `siteConfig`: `object` - 来自 `~/config/site.ts` 的网站配置对象。

- **`mobile-nav-links` (作用域插槽)**:
    - **描述**: 自定义移动端侧边栏中的导航链接列表。
    - **作用域数据**:
        - `navLinks`: `array` - 来自 `~/config/navLinks.ts` 的导航链接数组。
        - `closeMobileMenuFunction`: `() => void` - 关闭移动端侧边栏的函数。

- **`mobile-bottom-content` (作用域插槽)**:
    - **描述**: 自定义移动端侧边栏底部的整体内容区域，通常用于放置登录按钮或用户信息、以及语言/主题切换工具。
    - **作用域数据**:
        - `openLoginDialogFunction`: `() => void` - 打开登录弹窗的函数。
        - `loginStatus`: `boolean` - 当前用户的登录状态。

- **`mobile-extra-area` (作用域插槽)**:
    - **描述**: 在移动端侧边栏底部，用于展示登录/用户信息或登录按钮。此插槽位于 `mobile-bottom-content` 内部提供的默认结构中。
    - **作用域数据**:
        - `loginStatus`: `boolean` - 当前用户的登录状态。
        - `currentUser`: `object` - 当前登录用户的信息对象。
        - `handleSignOutFunction`: `() => void` - 执行登出操作的函数。
        - `openLoginDialogFunction`: `() => void` - 打开登录弹窗的函数。

- **`mobile-switch-locale`**:
    - **描述**: 自定义移动端侧边栏中的语言切换组件。默认是一个显示当前语言的按钮，点击后弹出选择菜单。此插槽位于
      `mobile-bottom-content` 内部提供的默认结构中。
    - **作用域数据**: 无。

- **`mobile-switch-theme`**:
    - **描述**: 自定义移动端侧边栏中的主题切换组件。默认是一个根据当前主题变化的图标按钮，点击后弹出选择菜单。此插槽位于
      `mobile-bottom-content` 内部提供的默认结构中。
    - **作用域数据**: 无。

## 基本用法示例

```vue
<template>
  <Header display-mode="scroll">
    <template #navbar>
      <!-- 通常在此处放置 PageNavbar 组件 -->
      <PageNavbar />
    </template>
  </Header>
</template>

<script setup lang="ts">
// Header 和 PageNavbar 组件通常会自动导入 (Nuxt 3项目特性)
// import Header from '~/components/market/header/Header.vue';
// import PageNavbar from '~/components/page/PageNavbar.vue';
</script>
```

## 使用自定义插槽示例 (PC端额外区域和移动端触发器)

```vue
<template>
  <Header display-mode="sticky">
    <template #navbar>
      <PageNavbar />
    </template>

    <!-- 自定义PC端右上角额外区域 -->
    <template #pc-extra-area="{ loginStatus, currentUser, openLoginDialogFunction, handleSignOutFunction }">
      <div class="flex items-center space-x-3">
        <button v-if="!loginStatus" @click="openLoginDialogFunction" class="text-sm font-medium text-primary hover:underline">
          从插槽登录
        </button>
        <div v-else class="flex items-center space-x-2">
          <img :src="currentUser.avatar" alt="avatar" class="w-7 h-7 rounded-full">
          <span class="text-sm">{{ currentUser.nickname }} (来自插槽)</span>
          <button @click="handleSignOutFunction" class="text-xs text-red-500 hover:underline">(登出)</button>
        </div>
        <button class="p-2 rounded-md hover:bg-accent">
          <Icon name="uil:bell" class="h-5 w-5" />
        </button>
      </div>
    </template>

    <!-- 自定义移动端菜单触发按钮 -->
    <template #mobile-trigger-sheet>
      <button class="p-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 h-full flex items-center">
        <Icon name="uil:align-justify" class="h-6 w-6" />
        <span class="ml-1 text-sm">菜单</span>
      </button>
    </template>

    <!-- 自定义移动端侧边栏头部 -->
    <template #mobile-sheet-header="{ siteConfig }">
        <div class="p-5 border-b bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
          <NuxtLink :to="$localePath({ path: '/' })" class="flex items-center space-x-3">
            <img v-if="siteConfig.logo" :src="siteConfig.logo" alt="Logo" class="w-10 h-10 rounded-xl shadow-lg">
            <div>
              <p class="text-xl font-bold">{{ getLocalizedConfigText(siteConfig.title) }}</p>
              <p class="text-xs opacity-80">自定义移动菜单头</p>
            </div>
          </NuxtLink>
        </div>
      </template>

  </Header>
</template>

<script setup lang="ts">
// import Header from '~/components/market/header/Header.vue';
// import PageNavbar from '~/components/page/PageNavbar.vue';
// import { Icon } from '#components'; // Icon组件可能需要导入
// import { getLocalizedConfigText } from '~/utils/i18n'; // 如果在插槽内使用
</script>
```

**注意**:

- 上述插槽示例中的CSS类名主要基于Tailwind CSS。
- 在插槽内容中如果使用到如 `Icon`、`Button`、`NuxtLink` 等组件，或 `getLocalizedConfigText`、`$localePath` 等函数，请确保它们在
  `index.vue` (或使用这些插槽的父组件) 的 `<script setup>` 中已正确导入或全局可用。
- `currentUser` 对象结构 (`nickname`, `email`, `avatar`) 是基于 `Header.vue` 内部定义的模拟数据，实际项目中应根据你的用户系统数据结构调整。 
/**
 * API响应接口
 */
export interface ApiResponse<T = unknown> {
    code: number;
    message: string;
    data?: T;
    requestId?: string;
    headers?: Map<string, string>;
}

/**
 * Websocket响应接口
 */
export interface WebSocketResponse<T = unknown> extends ApiResponse<T> {
    eventType: string,
    originalEventType?: string
    requestId?: string
    metadata: Record<string, unknown>
}

/**
 * Websocket请求体
 */
export interface WebSocketRequest<T = unknown> {
    event: string
    body?: T
    requestId?: string
    metadata?: Record<string, unknown>
    forwardResponse?: boolean
    response?: never
}

/**
 * 响应转换器类型
 * 用于将非标准格式的API响应转换为标准的ApiResponse格式
 * @typeparam O 原始响应类型
 * @typeparam T 转换后的数据类型
 */
export type ResponseTransformer<O = unknown, T = unknown> = (response: O) => ApiResponse<T>;

/**
 * 请求配置选项
 */
export interface RequestOptions {
    // 自定义请求头
    headers?: Record<string, string>;
    // 是否使用响应式数据 (默认: false)
    useReactive?: boolean;
    // 是否懒加载 (默认: false)
    lazy?: boolean;
    // 额外的FormData参数，会与主要请求数据合并
    additionalFormData?: Record<string, unknown>;
    // 请求超时时间 (毫秒)
    timeout?: number;
    // useFetch的额外选项
    fetchOptions?: Record<string, unknown>;
    // 响应转换器，用于将非标准格式的响应转换为ApiResponse格式
    responseTransformer?: ResponseTransformer;
    // 是否忽略token，如果忽略，则不会传递
    ignoreToken?: boolean;

    security?: {
        // 服务类型
        serviceType: string;
        // 主键
        primaryKey: string
    }
}

/**
 * 分页请求参数
 */
export interface PageParams {
    // 当前页码
    pageNum?: number;
    // 每页数量
    pageSize?: number;
    // 排序SQL列表
    sortSqlList?: string[];

    // 其他可能的查询参数
    [key: string]: unknown;
}

/**
 * 分页数据结构
 */
export interface PageData<T = unknown> {
    // 总数
    total: number;
    // 总页数
    pages: number;
    // 每页数量
    pageSize: number;
    // 当前页码
    pageNum: number;
    // 数据集
    result: T[];
    // 额外数据
    additionalData?: Record<string, any>;
}

/**
 * HTTP错误
 */
export class HttpError extends Error {
    code: number
    requestId?: string

    constructor(code: number, message: string, requestId?: string) {
        super(message)
        this.name = 'HttpError'
        this.code = code
        this.requestId = requestId
    }
}

/**
 * 刷新Token响应
 */
export interface RefreshTokenResponse {
    token: string;
    refreshToken: string;
    expiresIn: number;
}

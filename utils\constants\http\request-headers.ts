/**
 * 请求头常量
 *
 * 使用标准的常量命名规范，便于静态分析和代码提示
 */
export const REQUEST_HEADERS = {
    /** 项目特定的语言请求头 */
    ACCEPT_LANGUAGE: 'App-Accept-Language',
    /** 认证令牌头 */
    TOKEN: 'Token',
    /** 项目名称头 */
    PROJECT_NAME: 'Project-Name',
    /** 内容类型 */
    CONTENT_TYPE: 'Content-Type',
    /** 请求ID头 */
    REQUEST_ID: 'X-Request-Id',

    /** ticket */
    TICKET: 'Ticket',

    /** 存储图形验证码Id */
    CAPTCHA_ID: 'Captcha'
} as const

// 导出类型，便于类型检查
export type RequestHeaderName = keyof typeof REQUEST_HEADERS
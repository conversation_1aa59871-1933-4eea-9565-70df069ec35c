<script setup lang="ts">
import type {ArticleResponse} from '~/types/api'
import {logger} from '~/utils/logger'
import {tMsg} from '~/utils/i18n/utils'
import {Skeleton} from '~/components/ui/skeleton'
import ArticleCard from '~/components/article/ArticleCard.vue'
import AutoScrollContainer from '@/components/common/AutoScrollContainer.vue'
import {articleApi} from '~/utils/api/articleApi'

// 定义 Props
interface DisplayConfig {
    rows: number;
    // displayStyle 决定了是网格还是滚动，以及 ArticleCard 的样式
    displayStyle: 'default' | 'overlay';
}

interface Props {
    // PC端的显示配置
    pc?: Partial<DisplayConfig>;
    // 移动端的显示配置
    mobile?: Partial<DisplayConfig>;
}

const props = withDefaults(defineProps<Props>(), {
    // 默认 PC 配置: 网格布局
    pc: () => ({
        rows: 2,
        displayStyle: 'default'
    }),
    // 默认 Mobile 配置: 滚动布局
    mobile: () => ({
        rows: 1,
        displayStyle: 'overlay'
    })
})

// 文章列表数据
const articles = ref<ArticleResponse[]>([])
// 加载状态
const loading = ref(true)

// 获取最新文章
const fetchLatestPosts = async () => {
    loading.value = true
    try {
        const response = await articleApi.queryArticleList({
            pageNum: 1,
            pageSize: 6,
            sortSqlList: ['createTime desc']
        })
        if (response.code === 200 && response.data) {
            articles.value = response.data.result
        } else {
            articles.value = []
            logger.error('Failed get latest posts', {errorCode: response.code, errorMsg: response.message})
        }
    } catch (err) {
        articles.value = []
        logger.error('Get latest posts error', err)
    } finally {
        loading.value = false
    }
}

// --- 设备检测 ---
const isMobileView = ref(false)
let resizeObserver: ResizeObserver | null = null

const checkDevice = () => {
    if (typeof window !== 'undefined') {
        // 使用 Tailwind md断点 (768px)
        isMobileView.value = window.innerWidth < 768
    }
}

onMounted(() => {
    checkDevice()
    if (typeof window !== 'undefined') {
        resizeObserver = new ResizeObserver(checkDevice)
        resizeObserver.observe(document.body)
    }
    fetchLatestPosts()
})

onBeforeUnmount(() => {
    if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
    }
})

// --- 计算当前配置 ---
const currentConfig = computed<DisplayConfig>(() => {
    return isMobileView.value
        ? {...{rows: 1, displayStyle: 'overlay'}, ...props.mobile}
        : {...{rows: 2, displayStyle: 'default'}, ...props.pc}
})

const activeDisplayStyle = computed(() => currentConfig.value.displayStyle)
const activeRows = computed(() => currentConfig.value.rows)

// 组件挂载时获取数据
onMounted(fetchLatestPosts)
</script>

<template>
  <div v-if="articles.length > 0 && !loading" class="blog-preview bg-background py-16">
    <slot name="before-layout"/>

    <!-- #header 插槽 -->
    <slot name="header">
      <div class="text-center mb-8 md:mb-10">
        <h2 class="text-3xl md:text-4xl font-bold tracking-tight text-foreground mb-3">{{
          tMsg('blog.blogPreview.title')
        }}
        </h2>
        <p class="text-lg text-muted-foreground max-w-2xl mx-auto">{{ tMsg('blog.blogPreview.subtitle') }}</p>
      </div>
    </slot>

    <!-- 加载状态：骨架屏或 #loading 插槽 -->
    <div v-if="loading">
      <slot name="loading">
        <!-- 默认骨架屏 -->
        <div class="blog-preview-skeleton">
          <div class="text-center mb-12 md:mb-16">
            <Skeleton class="h-10 w-1/3 mx-auto mb-3"/>
            <Skeleton class="h-6 w-2/3 mx-auto"/>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12">
            <div v-for="n in 3" :key="n" class="flex flex-col bg-card rounded-lg overflow-hidden">
              <Skeleton class="aspect-video w-full"/>
              <div class="p-4 flex flex-col flex-grow space-y-3">
                <Skeleton class="h-4 w-1/4"/>
                <Skeleton class="h-5 w-3/4"/>
                <Skeleton class="h-4 w-full"/>
                <Skeleton class="h-4 w-5/6"/>
              </div>
            </div>
          </div>
        </div>
      </slot>
    </div>

    <div v-else>
      <!-- 主要内容区域 -->
      <div v-if="articles.length > 0">
        <!-- #before-articles 插槽 -->
        <slot name="before-articles"/>

        <!-- 文章列表 -->
        <div>
          <!-- 默认样式 ('default') -->
          <div v-if="activeDisplayStyle === 'default'"
               class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12">
            <ArticleCard v-for="(article, index) in articles"
                         :key="article.id"
                         :article="article"
                         display-style="default"
                         :class="[index >= 3 ? 'hidden sm:flex' : 'flex']">
              <template #article-cover>
                <slot name="article-cover" :article="article"/>
              </template>

              <template #article-detail>
                <slot name="article-detail" :article="article"/>
              </template>

              <template #article-attr>
                <slot name="article-attr" :article="article"/>
              </template>

              <template #article-content>
                <slot name="article-content" :article="article"/>
              </template>

              <template #after-article>
                <slot name="after-single-article" :article="article"/>
              </template>
            </ArticleCard>
          </div>

          <!-- 覆盖样式 ('overlay') 使用 AutoScrollContainer -->
          <AutoScrollContainer v-else-if="activeDisplayStyle === 'overlay'"
                               :rows="activeRows"
                               pause-on-hover
                               :fade-edges="true"
                               class="py-4">
            <ArticleCard v-for="article in articles"
                         :key="article.id"
                         :article="article"
                         display-style="overlay"
                         class="mx-2.5 flex-shrink-0 w-[300px] sm:w-[350px] h-auto">
              <template #article-cover>
                <slot name="article-cover" :article="article"/>
              </template>

              <template #article-detail>
                <slot name="article-detail" :article="article"/>
              </template>

              <template #article-attr>
                <slot name="article-attr" :article="article"/>
              </template>

              <template #article-content>
                <slot name="article-content" :article="article"/>
              </template>

              <template #after-article>
                <slot name="after-single-article" :article="article"/>
              </template>
            </ArticleCard>
          </AutoScrollContainer>
        </div>

        <!-- #after-articles 插槽 -->
        <slot name="after-articles"/>
      </div>
      <div v-else>
        <slot name="empty-posts"/>
      </div>
    </div>

    <slot name="after-layout"/>
  </div>
</template>

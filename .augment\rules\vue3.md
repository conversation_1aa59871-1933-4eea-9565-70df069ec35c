---
type: "agent_requested"
description: "本规则为Vue 3项目提供了最佳实践和编码标准，涵盖性能、安全性、前后端API交互，以确保应用程序的可维护性和效率。旨在指导开发者编写高质量的Vue 3代码。"
---

- 项目:
    - 目录结构: 采用基于功能的目录结构。将相关文件（组件、存储、工具）按功能分组在特定目录中，而不是按文件类型分开。这提高了可维护性和可发现性。
    - 组件架构: 优先使用shadcn-vue库组件，组件使用时不需要加UI前缀。在创建组件，设计组件时要小巧、可重用和可组合，浏览器和设备的兼容性。使用props进行数据输入，使用events进行数据输出。
    - 采用TypeScript

- 性能考虑:
    - 优化技术: 对静态内容使用`v-once`。使用`v-memo`记忆模板部分。在`v-for`循环中使用`key`属性提高渲染性能。
    - 内存管理: 通过正确清理事件监听器和计时器避免内存泄漏。使用`onBeforeUnmount`生命周期钩子释放资源。
    - 渲染优化: 高效使用虚拟DOM。

-- 前后端API交互:

- 请求参数校验：按照功能，业务的预期情况，在发送请求时，对必要的请求参数进行空值，类型，参数缺失校验。
- 响应体数据校验：做到对API响应体的零信任，对锁需要的数据进行null值，类型校验以及值的转换(如json反序列化,
  对返回long型的string进行转换等)

- 与库的使用：
    - 优先使用shadcn-vue组件实现UI
    - 在需要自定义组件时，遵循shadcn-vue的设计理念和样式系统
    - 样式优先使用tailwindcss，尽量避免自己写css
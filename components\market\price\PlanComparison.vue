<script setup lang="ts">
// --- Props Interfaces ---
interface ComparisonPlan {
    id: string;
    name: string;
    features: Record<string, string | boolean | number | null>;
}

type FeatureCategory = 'Features' | 'Reporting' | 'Support';

interface FeatureDefinition {
    id: string;
    name: string;
    category: FeatureCategory;
}

interface Props {
    plans: ComparisonPlan[];
    features: FeatureDefinition[];
}

const props = defineProps<Props>()

// --- Feature Grouping ---
const groupedFeatures = computed(() => {
    const groups: Record<FeatureCategory, FeatureDefinition[]> = {
        Features: [],
        Reporting: [],
        Support: []
    }
    props.features.forEach(feature => {
        if (groups[feature.category]) {
            groups[feature.category].push(feature)
        }
    })
    return groups
})

// --- Mobile Tab State ---
const defaultMobileTab = computed(() => props.plans[0]?.id || '')

// --- Helper to display feature value ---
const getDisplayValue = (value: string | boolean | number | null) => {
    if (value === true) {
        return {type: 'icon', name: 'i-lucide-check', class: 'text-primary'}
    }
    if (value === false || value === null || value === undefined) {
        return {type: 'icon', name: 'i-lucide-minus', class: 'text-muted-foreground'}
    }
    return {type: 'text', value: String(value)}
}
</script>

<template>
  <div class="w-full max-w-7xl mx-auto mt-12 md:mt-16">
    <!-- Desktop View -->
    <div class="hidden md:block">
      <Table>
        <TableHeader>
          <TableRow class="border-b-0 hover:bg-transparent">
            <TableHead class="w-1/4 pt-6 pb-4 align-bottom"/>
            <TableHead v-for="plan in plans" :key="plan.id" class="text-center pt-6 pb-4 align-bottom">
              <span class="font-semibold text-primary">{{ plan.name }}</span>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <template v-for="(categoryFeatures, category) in groupedFeatures" :key="category">
            <TableRow v-if="categoryFeatures.length > 0 && category === 'Features'"
                      class="border-b-0 bg-muted/50 hover:bg-muted/50">
              <TableCell class="font-semibold text-foreground pt-4 pb-3 align-middle">{{ category }}
              </TableCell>
              <TableCell v-for="plan in plans"
                         :key="plan.id + '-get-started'"
                         class="text-center pt-4 pb-3 align-middle">
                <span
                  class="text-sm font-medium text-muted-foreground hover:text-foreground cursor-pointer">
                  Get started
                </span>
              </TableCell>
            </TableRow>
            <TableRow v-else-if="categoryFeatures.length > 0"
                      class="border-b-0 bg-muted/50 hover:bg-muted/50">
              <TableCell colspan="4" class="font-semibold text-foreground pt-4 pb-3">{{ category }}
              </TableCell>
            </TableRow>
            <TableRow v-for="feature in categoryFeatures"
                      :key="feature.id"
                      class="border-b-0 hover:bg-transparent">
              <TableCell class="text-muted-foreground py-3 align-middle">{{ feature.name }}</TableCell>
              <TableCell v-for="plan in plans" :key="plan.id" class="text-center py-3 align-middle">
                <template v-if="getDisplayValue(plan.features[feature.id]).type === 'icon'">
                  <Icon :name="getDisplayValue(plan.features[feature.id]).name"
                        :class="getDisplayValue(plan.features[feature.id]).class"
                        class="h-5 w-5 mx-auto"/>
                </template>
                <template v-else>
                  <span class="text-sm font-medium text-foreground">{{
                    getDisplayValue(plan.features[feature.id]).value
                  }}</span>
                </template>
              </TableCell>
            </TableRow>
          </template>
        </TableBody>
      </Table>
    </div>

    <!-- Mobile View -->
    <div class="block md:hidden">
      <Tabs :default-value="defaultMobileTab" class="w-full">
        <TabsList class="grid w-full grid-cols-3 mb-4">
          <TabsTrigger v-for="plan in plans" :key="plan.id" :value="plan.id">
            {{ plan.name }}
          </TabsTrigger>
        </TabsList>
        <TabsContent v-for="plan in plans"
                     :key="plan.id"
                     :value="plan.id"
                     class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-medium text-primary">{{ plan.name }}</h3>
            <span class="text-sm font-medium text-muted-foreground hover:text-foreground cursor-pointer">
              Get started
            </span>
          </div>
          <div v-for="(categoryFeatures, category) in groupedFeatures" :key="category" class="mb-6 last:mb-0">
            <h4 v-if="categoryFeatures.length > 0" class="font-semibold text-foreground mb-3 border-b pb-2">
              {{
                category
              }}</h4>
            <ul class="space-y-3">
              <li v-for="feature in categoryFeatures"
                  :key="feature.id"
                  class="flex justify-between items-center text-sm">
                <span class="text-muted-foreground">{{ feature.name }}</span>
                <template v-if="getDisplayValue(plan.features[feature.id]).type === 'icon'">
                  <Icon :name="getDisplayValue(plan.features[feature.id]).name"
                        :class="getDisplayValue(plan.features[feature.id]).class"
                        class="h-5 w-5"/>
                </template>
                <template v-else>
                  <span class="font-medium text-foreground">{{
                    getDisplayValue(plan.features[feature.id]).value
                  }}</span>
                </template>
              </li>
            </ul>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  </div>
</template>
<script setup lang="ts">
import type {ChatMessage, FileData} from '@/types/chat'
import VideoPlayer from '~/components/common/VideoPlayer.vue'

const props = defineProps<{
    message: ChatMessage
}>()

const fileInfo = computed(() => props.message.clientData as FileData)
</script>

<template>
  <div class="w-100">
    <VideoPlayer :video-src="fileInfo.url" :thumbnail-src="fileInfo.thumbnail ? fileInfo.thumbnail : ''"/>
    <div v-if="message.uploadProgress !== undefined && message.uploadProgress < 100"
         class="mt-2 h-1 w-full bg-muted rounded-full">
      <div class="h-1 bg-primary rounded-full" :style="{ width: `${message.uploadProgress}%` }"/>
    </div>
  </div>
</template>

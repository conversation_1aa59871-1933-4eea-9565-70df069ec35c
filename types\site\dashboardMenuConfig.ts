import type {I18nTextMap} from '~/types/i18n'
import type {NavLinksConfig} from '~/types/site/navLinks'

/**
 * 单个导航链接的配置
 */
export interface DashboardGroupMenu {
    label?: I18nTextMap

    menus: NavLinksConfig
}

export interface DashboardConfigInfo {
    sidebarMenus: DashboardGroupMenu[],
    sidebarFooterMenus?: NavLinksConfig
}

/**
 * 导航链接配置数组
 */
export type DashboardConfig = DashboardConfigInfo
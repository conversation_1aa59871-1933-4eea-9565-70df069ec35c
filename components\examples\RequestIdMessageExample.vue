<template>
  <div class="space-y-4 p-4 bg-white rounded-lg shadow">
    <h2 class="text-xl font-bold">消息示例</h2>

    <div class="space-y-2">
      <h3 class="text-lg font-semibold">带请求ID的错误消息</h3>
      <div class="flex space-x-2">
        <Button @click="handleErrorWithRequestId">错误消息 + RequestId</Button>
      </div>
    </div>

    <div class="space-y-2">
      <h3 class="text-lg font-semibold">带支持按钮的错误消息</h3>
      <div class="flex space-x-2">
        <Button @click="handleErrorWithSupport">错误消息 + 支持按钮</Button>
      </div>
    </div>

    <div class="space-y-2">
      <h3 class="text-lg font-semibold">完整错误消息</h3>
      <div class="flex space-x-2">
        <Button @click="handleCompleteErrorMessage">完整错误消息</Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {Button} from '@/components/ui/button'
import {message} from '@/composables/useMessage'

// 带请求ID的错误消息
const handleErrorWithRequestId = () => {
    message.error('服务器错误', {
        description: '连接服务器失败，请稍后再试',
        requestId: 'REQ-2023-12-05-001'
    })
}

// 带支持按钮的错误消息
const handleErrorWithSupport = () => {
    message.error('连接失败', {
        description: '无法连接到服务器，请检查网络设置',
        showSupport: true
    })
}

// 完整错误消息（带RequestId和支持按钮）
const handleCompleteErrorMessage = () => {
    message.error('出现错误', {
        description: '无法连接到服务器，请稍后再试',
        requestId: 'REQ-2023-12-05-002',
        showSupport: true
    })
}
</script>
---
description: 
globs: 
alwaysApply: false
---
## I18n

### 核心原则

-   **集中管理**: 标准翻译集中在 `/i18n/locales`，配置相关翻译内嵌在对应配置文件中。

### 流程 1: 处理标准 UI 文本

适用于直接在 Vue 组件模板或 `<script setup>` 中使用的、相对静态的界面文本。

1.  **定义 Key**: 在 `/i18n/locales` 目录下的对应语言文件（如 `en-US.json`, `zh-CN.json`）中，按照功能或模块添加结构化的 Key。
    -   **推荐**: `{ "footer": { "copyright": "..." } }`
    -   **不推荐**: `{ "footer_copyright": "..." }`
2.  **使用 `tMsg`**: 在组件中，导入并使用 `@/utils/i18n/utils` 中的 `tMsg` 函数来获取翻译。
    ```vue
    <template>
      <p>{{ tMsg('footer.copyright') }}</p>
    </template>

    <script setup lang="ts">
    import { tMsg } from '@/utils/i18n/utils'
    </script>
    ```
3.  **静态文本**: 对于纯静态文本，Key 中不需要包含动态参数占位符。

### 流程 2: 处理配置驱动文本

适用于内容由 `/config` 目录下的 JSON 文件驱动的部分（例如导航链接、FAQ 列表等），允许非开发人员通过修改配置来更新内容及其翻译。

1.  **定义数据结构**: 在配置文件（如 [@i18nExample.json](mdc:config/i18nExample.json)）中，将需要翻译的文本组织成特定的映射结构。
    -   使用通用接口 [@I18nTextMap](mdc:types/i18n.ts) 定义的格式：`{ "en": "English Text", "zh": "中文文本" }`。
2.  **定义配置类型**: 在 `types/site/` 目录下为对应的配置文件创建详细的 TypeScript 接口（如 [@i18nExample.ts](mdc:types/site/i18nExample.ts)），并在接口中引用 `I18nTextMap`。
3.  **使用辅助函数**: 在组件的 `<script setup>` 部分：
    -   导入 `@/utils/i18n/utils` 中的 `getLocalizedConfigText` 辅助函数。
    -   导入相应的配置类型（例如从 `~/types/site/i18nExample` 导入 `I18nExampleConfig`）。
    -   导入对应的 JSON 配置文件（例如 `import i18nExampleConfig from '@/config/i18nExample.json'`）。
    -   使用类型断言将导入的 JSON 数据转换为定义的 TypeScript 类型（例如 `const configData = i18nExampleConfig as I18nExampleConfig`）。
    -   调用 `getLocalizedConfigText` 函数，并将配置对象中需要翻译的部分（如 `configData.pageTitle` 或 `feature.name`）作为参数传入，即可获取当前语言环境下的文本。
4.  **优势**: 将内容和翻译集中在配置文件中，便于管理和非代码修改；通过类型和辅助函数保证了代码的健壮性。

### 注意事项

-   **语言代码**: 系统配置的语言代码为 `en` 和 `zh` (参见 `nuxt.config.ts`)，`I18nTextMap` 和相关逻辑依赖这些代码。
-   **回退逻辑**: `getLocalizedConfigText` 函数内置了回退逻辑，当找不到当前语言的文本时，会尝试使用项目配置 (`config/projectConfig.ts`) 中指定的默认语言。


from flask import Flask, request, jsonify
from flask_cors import CORS
from rpa import QianNiuRPA
import os
import base64

app = Flask(__name__)
CORS(app)
qn_rpa = QianNiuRPA()

@app.route('/execute/rpa', methods=['POST'])
def execute_rpa():
    data = request.json
    action_type = data.get('type')
    content_b64 = data.get('data') # Expects base64 for image/file
    filename = data.get('filename', 'file.dat') # Optional filename for better type handling
    auto_close = data.get('autoClose', False)

    if not action_type:
        return jsonify({"status": "error", "message": "Missing 'type' parameter"}), 400

    if action_type == 'send':
        result = qn_rpa.send_message(auto_close)
        return jsonify(result)
    
    elif action_type in ['image', 'file']:
        if not content_b64:
            return jsonify({"status": "error", "message": f"Missing 'data' for {action_type} type"}), 400
        try:
            # For both image and file, decode the base64 string to bytes
            content_bytes = base64.b64decode(content_b64)
            result = qn_rpa.paste_and_send(content_bytes, action_type, auto_close, filename)
            return jsonify(result)
        except (ValueError, TypeError) as e:
            return jsonify({"status": "error", "message": f"Invalid base64 data for {action_type}: {e}"}), 400

    else:
        return jsonify({"status": "error", "message": "Invalid 'type' parameter"}), 400


@app.route('/execute/close', methods=['POST'])
def execute_close():
    result = qn_rpa.close_chat_window()
    return jsonify(result)

if __name__ == '__main__':
    # 监听所有网络接口，方便从其他设备或容器访问
    # 默认端口为 5000
    app.run(host='0.0.0.0', port=5000, debug=True) 
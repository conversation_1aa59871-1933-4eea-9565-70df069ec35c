import {type ApiResponse, type PageData, Post, type RequestOptions} from '~/utils/http'
import type {ArticleParams} from '~/types/api/params/ArticleParams'
import type {ArticleResponse} from '~/types/api'

/**
 * 创建博客文章API
 */
function createArticleApi() {
    // 基础URL
    const baseUrl = '/plat/article'

    async function queryArticleList(data: ArticleParams, options?: RequestOptions): Promise<ApiResponse<PageData<ArticleResponse>>> {
        return Post<PageData<ArticleResponse>>(`${baseUrl}/pageList`, data, options)
    }

    return {
        queryArticleList
    }
}

// 导出博客文章API
export const articleApi = createArticleApi()
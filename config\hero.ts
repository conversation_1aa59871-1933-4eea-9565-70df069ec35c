import type {HeroConfig} from '@/types/site/hero'

export const heroConfig: HeroConfig = {
    headline: {
        en: 'Empowering Healthand Healing forEvery <span class="text-primary font-bold">Body</span>',
        zh: '赋能健康疗愈身心关爱每<span class="text-primary font-bold">一位</span>'
    },
    subHeadline: {
        en: 'Your trusted source for <span class="font-semibold text-accent text-primary">comprehensive</span> healthcare solutions.',
        zh: '包括成功、失败和进行中的部署。通过GitHub Actions实时跟踪您的项目部署状态，包括成功、失败和进行中的部署。通过GitHub Actions实时跟踪您的'
    },
    // 默认图片路径
    heroImage: 'https://shipfa.st/_next/static/media/demo.753bdb0f.png',
    heroVideo: {
        thumbnailSrc: 'https://startup-template-sage.vercel.app/hero-light.png',
        videoSrc: 'https://youtu.be/iSuDS2zTDD0?si=FCJstj3_N4VXoiBd',
        playbackMode: 'dialog'
    },
    ctaText: {
        en: 'Book an Appointment',
        zh: '预约服务'
    },
    testimonial: {
        avatars: [
            {imageUrl: 'https://shipfa.st/_next/static/media/lennard.a8caddd5.jpeg', profileUrl: '#'},
            {imageUrl: 'https://shipfa.st/_next/static/media/wahab.c0419676.jpeg', profileUrl: '#'},
            {imageUrl: 'https://shipfa.st/_next/static/media/naveen.311d3eb1.jpeg', profileUrl: '#'},
            {imageUrl: 'https://shipfa.st/_next/static/media/artificery.ba1049ef.jpeg', profileUrl: '#'},
            {imageUrl: 'https://shipfa.st/_next/static/media/dunsin.c7d35d82.jpeg', profileUrl: '#'}
        ],
        totalCount: 2491,
        descText: {
            en: 'Happy Customers',
            zh: '满意客户'
        }
    }
}
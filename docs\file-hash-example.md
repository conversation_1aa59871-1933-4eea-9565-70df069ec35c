# 文件哈希和分片上传功能使用示例

本文档展示如何使用新增的文件哈希工具和分片上传功能。

## 功能概述

### 文件哈希工具 (utils/typeof.ts)
基于SparkMD5库，提供以下功能：

1. 单文件MD5哈希计算
2. 文件类型检测（图片、视频、音频）
3. 文件大小格式化
4. 进度回调支持

### 分片上传工具 (utils/fileUpload.ts)
提供完整的分片上传解决方案：

1. 文件分片处理
2. 并发上传控制
3. 断点续传支持
4. 上传进度跟踪
5. 自动重试机制

## 测试页面

访问 `/test-file-upload` 页面可以测试所有功能，包括：
- 文件选择和信息显示
- MD5哈希计算（单个和批量）
- 重复文件检测
- 模拟分片上传

## 基本使用

### 计算单个文件的MD5哈希

```typescript
import { calculateFileHash } from '~/utils/typeof'

// 获取文件（例如从文件输入框）
const fileInput = document.getElementById('fileInput') as HTMLInputElement
const file = fileInput.files?.[0]

if (file) {
  try {
    const result = await calculateFileHash(file, {
      // 可选：自定义分块大小（默认2MB）
      chunkSize: 1024 * 1024, // 1MB
      // 可选：进度回调
      onProgress: (progress) => {
        console.log(`计算进度: ${progress}%`)
        // 可以在这里更新UI进度条
      }
    })
    
    console.log('文件哈希计算完成:', {
      hash: result.hash,
      size: result.size,
      duration: result.duration
    })
  } catch (error) {
    console.error('哈希计算失败:', error)
  }
}
```

### 分片上传功能

```typescript
import { fileUploadUtils } from '~/utils/fileUpload'

// 使用分片上传
await fileUploadUtils.chunkUpload(
    file, // 文件对象
    { fileName: file.name, fileSize: file.size }, // 检查参数
    1024 * 1024, // 分片大小 (1MB)
    checkAndMergeFunction, // 检查和合并函数
    chunkUploadFunction, // 分片上传函数
    updateProgressFunction, // 进度更新函数
    3, // 最大并发数
    handleSuccessFunction, // 成功处理函数
    handleFailureFunction // 失败处理函数
)
```

### 文件类型检测

```typescript
import { isImageFile, isVideoFile, isAudioFile, formatFileSize } from '~/utils/typeof'

const file = fileInput.files?.[0]
if (file) {
  console.log({
    isImage: isImageFile(file),
    isVideo: isVideoFile(file),
    isAudio: isAudioFile(file),
    formattedSize: formatFileSize(file.size)
  })
}
```

### 批量计算多个文件的哈希

```typescript
// 注意：批量计算需要自己实现循环
const files = Array.from(fileInput.files || [])
const results = []

for (const file of files) {
  try {
    const result = await calculateFileHash(file)
    results.push({ file, result })
  } catch (error) {
    console.error(`文件 ${file.name} 计算失败:`, error)
  }
}

const files = Array.from(fileInput.files || [])

if (files.length > 0) {
  try {
    const results = await calculateMultipleFileHashes(files, {
      onProgress: (totalProgress) => {
        console.log(`总体进度: ${totalProgress}%`)
      }
    })
    
    results.forEach((result, index) => {
      console.log(`文件 ${files[index].name}:`, {
        hash: result.hash,
        size: result.size,
        duration: result.duration
      })
    })
  } catch (error) {
    console.error('批量哈希计算失败:', error)
  }
}
```

### 比较两个文件是否相同

```typescript
import { compareFiles } from '~/utils/fileHash'

const file1 = fileInput1.files?.[0]
const file2 = fileInput2.files?.[0]

if (file1 && file2) {
  try {
    const isSame = await compareFiles(file1, file2)
    console.log('文件是否相同:', isSame)
  } catch (error) {
    console.error('文件比较失败:', error)
  }
}
```

### 检测重复文件

```typescript
import { findDuplicateFiles } from '~/utils/fileHash'

const files = Array.from(fileInput.files || [])

if (files.length > 1) {
  try {
    const duplicates = await findDuplicateFiles(files)
    
    if (duplicates.size > 0) {
      console.log('发现重复文件:')
      duplicates.forEach((fileList, hash) => {
        console.log(`哈希值 ${hash} 对应的重复文件:`)
        fileList.forEach(file => {
          console.log(`  - ${file.name} (${file.size} bytes)`)
        })
      })
    } else {
      console.log('未发现重复文件')
    }
  } catch (error) {
    console.error('重复文件检测失败:', error)
  }
}
```

## 在Vue组件中使用

```vue
<template>
  <div>
    <input 
      ref="fileInput" 
      type="file" 
      multiple 
      @change="handleFileSelect"
    >
    <div v-if="isCalculating">
      计算进度: {{ progress }}%
    </div>
    <div v-if="results.length > 0">
      <h3>文件哈希结果:</h3>
      <ul>
        <li v-for="(result, index) in results" :key="index">
          {{ result.filename }}: {{ result.hash }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { calculateMultipleFileHashes } from '~/utils/fileHash'

const fileInput = ref<HTMLInputElement>()
const isCalculating = ref(false)
const progress = ref(0)
const results = ref<Array<{filename: string, hash: string}>>([])

async function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  
  if (files.length === 0) return
  
  isCalculating.value = true
  progress.value = 0
  results.value = []
  
  try {
    const hashResults = await calculateMultipleFileHashes(files, {
      onProgress: (p) => {
        progress.value = p
      }
    })
    
    results.value = hashResults.map((result, index) => ({
      filename: files[index].name,
      hash: result.hash
    }))
  } catch (error) {
    console.error('哈希计算失败:', error)
  } finally {
    isCalculating.value = false
  }
}
</script>
```

## 文件类型检测工具

除了哈希计算，还提供了文件类型检测工具：

```typescript
import { 
  isImageFile, 
  isVideoFile, 
  isAudioFile, 
  getFileExtension,
  formatFileSize 
} from '~/utils/typeof'

const file = fileInput.files?.[0]

if (file) {
  console.log('文件信息:', {
    name: file.name,
    extension: getFileExtension(file.name),
    size: formatFileSize(file.size),
    isImage: isImageFile(file),
    isVideo: isVideoFile(file),
    isAudio: isAudioFile(file)
  })
}
```

## 性能考虑

1. **分块大小**: 默认使用2MB分块，可根据文件大小和性能需求调整
2. **大文件处理**: 对于大文件，建议显示进度条提升用户体验
3. **内存使用**: SparkMD5使用流式处理，内存占用相对较小
4. **并发控制**: 批量处理时会串行计算，避免内存压力过大

## 注意事项

1. 文件哈希计算是CPU密集型操作，大文件可能需要较长时间
2. 在Web Worker中运行可以避免阻塞主线程（未来可考虑实现）
3. 哈希值可用于文件完整性验证、去重、缓存等场景
4. 建议在文件上传前计算哈希，可用于断点续传等高级功能

<script setup lang="ts">
import type {ChatMessage, FileData} from '@/types/chat'
import {Dialog, DialogContent, DialogTrigger} from '@/components/ui/dialog'

// 定义组件的props
const props = defineProps<{
    message: ChatMessage
}>()

const fileInfo = computed(() => props.message.clientData as FileData)
</script>

<template>
  <div class="relative w-64">
    <Dialog>
      <DialogTrigger as-child>
        <img :src="fileInfo.url"
             :alt="fileInfo.filename"
             class="w-full h-auto object-cover rounded-lg cursor-pointer transition-all hover:brightness-90">
      </DialogTrigger>
      <DialogContent class="p-0 border-0 max-w-4xl bg-transparent">
        <img :src="fileInfo.url" :alt="fileInfo.filename" class="w-full h-auto rounded-lg">
      </DialogContent>
    </Dialog>
    <div v-if="message.uploadProgress !== undefined && message.uploadProgress < 100"
         class="absolute bottom-2 left-2 right-2 h-1 bg-black/50 rounded-full">
      <div class="h-1 bg-primary rounded-full" :style="{ width: `${message.uploadProgress}%` }"/>
    </div>
  </div>
</template>

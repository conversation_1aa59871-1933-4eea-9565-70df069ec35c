<script setup lang="ts">
import type {ChatMessage, FileData} from '@/types/chat'

const props = defineProps<{
    message: ChatMessage
}>()

const fileInfo = computed(() => props.message.clientData as FileData)
</script>

<template>
  <div class="w-64">
    <audio controls class="w-full rounded-lg" :src="fileInfo.url">
      Your browser does not support the audio element.
    </audio>
    <div v-if="message.uploadProgress !== undefined && message.uploadProgress < 100"
         class="mt-2 h-1 w-full bg-muted rounded-full">
      <div class="h-1 bg-primary rounded-full" :style="{ width: `${message.uploadProgress}%` }"/>
    </div>
  </div>
</template>

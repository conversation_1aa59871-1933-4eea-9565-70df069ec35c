import type {TeamConfig} from '~/types/site/team'

// 团队成员配置文件
// 此文件用于定义 TeamOne 组件所需的数据，支持国际化。
const teamConfigData: TeamConfig = {
    // 新增：组件的默认标题和描述
    title: {
        en: 'Meet our leadership',
        zh: '认识我们的领导团队'
    },
    description: {
        en: 'We\'re a dynamic group of individuals who are passionate about what we do and dedicated to delivering the best results for our clients.',
        zh: '我们是一个充满活力的团队，对所从事的事业充满热情，致力于为客户提供最佳成果。'
    },
    members: [
        {
            imageUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
            name: {
                en: '<PERSON>',
                zh: '莱斯利·亚历山大'
            },
            role: {
                en: 'Co-Founder / CEO',
                zh: '联合创始人 / 首席执行官'
            },
            description: {
                en: '<PERSON> is a results-driven CEO with over 15 years of experience in the tech industry, specializing in strategic growth and market development. She is passionate about building innovative products that solve real-world problems.',
                zh: '莱斯利是一位注重结果的首席执行官，在科技行业拥有超过15年的经验，专注于战略增长和市场开发。她热衷于打造能够解决现实问题的创新产品。'
            },
            socials: [
                {name: 'X', url: '#', icon: 'lucide:twitter'},
                {name: 'LinkedIn', url: '#', icon: 'lucide:linkedin'}
            ]
        },
        {
            imageUrl: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
            name: {
                en: 'Michael Foster',
                zh: '迈克尔·福斯特'
            },
            role: {
                en: 'Co-Founder / CTO',
                zh: '联合创始人 / 首席技术官'
            },
            description: {
                en: 'Michael is a visionary CTO with a deep understanding of cutting-edge technologies. He leads the engineering team with a focus on scalability and performance.',
                zh: '迈克尔是一位富有远见的首席技术官，对尖端技术有着深刻的理解。他领导工程团队，专注于可扩展性和性能。'
            },
            socials: [
                {name: 'X', url: '#', icon: 'lucide:twitter'}
            ]
        },
        {
            imageUrl: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
            name: {
                en: 'Dries Vincent',
                zh: '德里斯·文森特'
            },
            role: {
                en: 'Business Relations',
                zh: '商务关系'
            },
            description: {
                en: 'Dries excels at building strong, lasting relationships with clients and partners. His strategic approach to business development has been key to our expansion.',
                zh: '德里斯擅长与客户和合作伙伴建立强大而持久的关系。他在业务拓展方面的战略性方法是我们扩张的关键。'
            }
            // socials: [] // Dries 没有社交链接示例
        },
        {
            imageUrl: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
            name: {
                en: 'Lindsay Walton',
                zh: '林赛·沃尔顿'
            },
            role: {
                en: 'Front-end Developer',
                zh: '前端开发者'
            },
            description: {
                en: 'Lindsay is a creative front-end developer passionate about crafting beautiful and intuitive user interfaces. She has a keen eye for detail and a commitment to web standards.',
                zh: '林赛是一位富有创造力的前端开发者，热衷于打造美观直观的用户界面。她对细节有敏锐的洞察力，并致力于遵循Web标准。'
            },
            socials: [
                {name: 'LinkedIn', url: '#', icon: 'lucide:linkedin'},
                {name: 'GitHub', url: '#', icon: 'lucide:github'}
            ]
        },
        {
            imageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
            name: {
                en: 'Courtney Henry',
                zh: '考特尼·亨利'
            },
            role: {
                en: 'Designer',
                zh: '设计师'
            },
            description: {
                en: 'Courtney brings a wealth of experience in visual design and user experience. Her creative solutions consistently elevate our projects.',
                zh: '考特尼在视觉设计和用户体验方面拥有丰富的经验。她的创意解决方案持续提升我们的项目水平。'
            },
            socials: [
                {name: 'Dribbble', url: '#', icon: 'lucide:dribbble'}
            ]
        },
        {
            imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
            name: {
                en: 'Tom Cook',
                zh: '汤姆·库克'
            },
            role: {
                en: 'Director of Product',
                zh: '产品总监'
            },
            description: {
                en: 'Tom has a strong background in product strategy and a knack for understanding user needs. He ensures our products are both innovative and market-relevant.',
                zh: '汤姆在产品策略方面拥有深厚的背景，并且善于理解用户需求。他确保我们的产品既创新又与市场相关。'
            }
            // Tom 没有社交链接示例
        }
    ]
}

export default teamConfigData
import {batch, createHeaders, lazyRequest, request} from './request'
import type {ApiResponse, RequestOptions} from './types'
import {CONTENT_TYPES, REQUEST_HEADERS} from '~/utils/constants/http'
import {isObject} from '~/utils/typeof'
// 导入i18n工具函数
import {tMsg} from '~/utils/i18n/utils'

// 导出常量，使其可以从http模块中导入
export {CONTENT_TYPES, REQUEST_HEADERS}

/**
 * 将任意类型转换为Record<string, unknown>
 * @param data 任意类型的数据
 * @returns 转换后的Record对象
 * @throws 如果data不是对象类型则抛出异常
 */
function toRecord(data: unknown): Record<string, unknown> | undefined {
    if (data === undefined) {
        return undefined
    }

    if (data === null) {
        return {}
    }

    if (!isObject(data)) {
        throw new Error(tMsg('http.request_params_must_be_object'))
    }

    return data as Record<string, unknown>
}

/**
 * GET请求
 * @param url 请求URL
 * @param params 查询参数，任意对象类型
 * @param options 请求选项
 * @returns 响应数据
 */
export async function Get<T = unknown>(
    url: string,
    params?: unknown,
    options: RequestOptions = {}
): Promise<ApiResponse<T>> {
    // 设置请求头
    if (!options.headers) {
        options.headers = {}
    }
    // 如果没有设置内容类型，则设置默认内容类型
    if (!options.headers[REQUEST_HEADERS.CONTENT_TYPE]) {
        options.headers[REQUEST_HEADERS.CONTENT_TYPE] = CONTENT_TYPES.FORM
    }
    return request<T>(url, 'GET', toRecord(params), options)
}

/**
 * POST请求
 * @param url 请求URL
 * @param data 请求数据，任意对象类型
 * @param options 请求选项
 * @returns 响应数据
 */
export async function Post<T = unknown>(
    url: string,
    data?: unknown,
    options: RequestOptions = {}
): Promise<ApiResponse<T>> {
    // 设置请求头
    if (!options.headers) {
        options.headers = {}
    }
    // 如果没有设置内容类型，则设置默认内容类型
    if (!options.headers[REQUEST_HEADERS.CONTENT_TYPE]) {
        options.headers[REQUEST_HEADERS.CONTENT_TYPE] = CONTENT_TYPES.JSON
    }
    return request<T>(url, 'POST', toRecord(data), options)
}

/**
 * PUT请求
 * @param url 请求URL
 * @param data 请求数据，任意对象类型
 * @param options 请求选项
 * @returns 响应数据
 */
export async function Put<T = unknown>(
    url: string,
    data?: unknown,
    options: RequestOptions = {}
): Promise<ApiResponse<T>> {
    // 设置请求头
    if (!options.headers) {
        options.headers = {}
    }
    // 如果没有设置内容类型，则设置默认内容类型
    if (!options.headers[REQUEST_HEADERS.CONTENT_TYPE]) {
        options.headers[REQUEST_HEADERS.CONTENT_TYPE] = CONTENT_TYPES.JSON
    }
    return request<T>(url, 'PUT', toRecord(data), options)
}

/**
 * DELETE请求
 * @param url 请求URL
 * @param params 请求参数，任意对象类型
 * @param options 请求选项
 * @returns 响应数据
 */
export async function Delete<T = unknown>(
    url: string,
    params?: unknown,
    options: RequestOptions = {}
): Promise<ApiResponse<T>> {
    // 设置请求头
    if (!options.headers) {
        options.headers = {}
    }
    // 如果没有设置内容类型，则设置默认内容类型
    if (!options.headers[REQUEST_HEADERS.CONTENT_TYPE]) {
        options.headers[REQUEST_HEADERS.CONTENT_TYPE] = CONTENT_TYPES.FORM
    }
    return request<T>(url, 'DELETE', toRecord(params), options)
}

/**
 * GET请求的懒加载实现
 * @param url 请求URL
 * @param params 查询参数，任意对象类型
 * @param options 请求选项
 * @returns AsyncData对象，包含响应数据
 */
export function LazyGet<T = unknown>(
    url: string,
    params?: unknown,
    options: RequestOptions = {}
) {
    // 设置请求头
    if (!options.headers) {
        options.headers = {}
    }
    // 如果没有设置内容类型，则设置默认内容类型
    if (!options.headers[REQUEST_HEADERS.CONTENT_TYPE]) {
        options.headers[REQUEST_HEADERS.CONTENT_TYPE] = CONTENT_TYPES.FORM
    }
    return lazyRequest<T>(url, 'GET', toRecord(params), options)
}

/**
 * POST请求的懒加载实现
 * @param url 请求URL
 * @param data 请求体数据，任意对象类型
 * @param options 请求选项
 * @returns AsyncData对象，包含响应数据
 */
export function LazyPost<T = unknown>(
    url: string,
    data?: unknown,
    options: RequestOptions = {}
) {
    // 设置请求头
    if (!options.headers) {
        options.headers = {}
    }
    // 如果没有设置内容类型，则设置默认内容类型
    if (!options.headers[REQUEST_HEADERS.CONTENT_TYPE]) {
        options.headers[REQUEST_HEADERS.CONTENT_TYPE] = CONTENT_TYPES.JSON
    }
    return lazyRequest<T>(url, 'POST', toRecord(data), options)
}

/**
 * PUT请求的懒加载实现
 * @param url 请求URL
 * @param data 请求体数据，任意对象类型
 * @param options 请求选项
 * @returns AsyncData对象，包含响应数据
 */
export function LazyPut<T = unknown>(
    url: string,
    data?: unknown,
    options: RequestOptions = {}
) {
    // 设置请求头
    if (!options.headers) {
        options.headers = {}
    }
    // 如果没有设置内容类型，则设置默认内容类型
    if (!options.headers[REQUEST_HEADERS.CONTENT_TYPE]) {
        options.headers[REQUEST_HEADERS.CONTENT_TYPE] = CONTENT_TYPES.JSON
    }
    return lazyRequest<T>(url, 'PUT', toRecord(data), options)
}

/**
 * DELETE请求的懒加载实现
 * @param url 请求URL
 * @param params 查询参数，任意对象类型
 * @param options 请求选项
 * @returns AsyncData对象，包含响应数据
 */
export function LazyDelete<T = unknown>(
    url: string,
    params?: unknown,
    options: RequestOptions = {}
) {
    // 设置请求头
    if (!options.headers) {
        options.headers = {}
    }
    // 如果没有设置内容类型，则设置默认内容类型
    if (!options.headers[REQUEST_HEADERS.CONTENT_TYPE]) {
        options.headers[REQUEST_HEADERS.CONTENT_TYPE] = CONTENT_TYPES.FORM
    }
    return lazyRequest<T>(url, 'DELETE', toRecord(params), options)
}

/**
 * 批量请求
 * @param requests 请求数组，每个请求可以包含任意对象类型的参数
 * @returns 所有请求的响应
 */
export async function batchRequest<T>(
    requests: Array<{
        url: string
        method: 'GET' | 'POST' | 'PUT' | 'DELETE'
        data?: unknown
        options?: RequestOptions
    }>
): Promise<T> {
    // 将每个请求的data转换为Record<string, unknown>
    const processedRequests = requests.map(req => ({
        url: req.url,
        method: req.method,
        data: toRecord(req.data),
        options: req.options
    }))

    return batch<T>(processedRequests)
}

// 导出批量请求方法，保持向后兼容
export {batch}

// 导出所有类型
export type {ApiResponse, RequestOptions, HttpError, PageData, PageParams, RefreshTokenResponse} from './types'
// 导出请求头创建函数
export {createHeaders, lazyRequest}

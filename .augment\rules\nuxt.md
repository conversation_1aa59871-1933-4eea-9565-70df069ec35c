---
type: "agent_requested"
description: "本规则为Nuxt.js项目提供了全面的最佳实践和编码标准，专注于Nuxt特有的功能和架构。适用于基于Nuxt.js的SSR系统，仅负责页面渲染和API调用的前端部分。"
---

- 遵循Nuxt.js约定优于配置原则
- SSR注意事项：编写同构代码，注意服务端和客户端环境差异。避免直接操作`window`、`document`等仅客户端可用的对象。

## 1. Nuxt.js职责划分

- Nuxt前端只负责页面渲染和API调用，不处理后端部分的业务逻辑
- 使用封装的 [index.ts](mdc:utils/http/index.ts)
  执行请求发送，以和后端服务进行API交互，不允许直接调用useAsyncData,useFetch,$fetch，[index.ts](mdc:utils/http/index.ts)
  底层使用的是useFetch

## 2. Nuxt特有功能最佳实践

- 路由与导航：
    - 使用`<NuxtLink>`替代普通`<a>`标签实现客户端导航
    - 利用`/pages`目录创建嵌套路由
    - 使用`navigateTo`函数进行编程式导航

- 状态管理：
    - 使用`useState`管理跨组件共享的简单状态
    - 对于复杂状态管理，结合Pinia使用
    - 创建功能明确的store，避免单一大型store
    - 区分服务端和客户端状态，避免状态混淆

- SEO优化：
    - 确保内容在服务端渲染，而不是客户端生成
    - 实现结构化数据（JSON-LD）提升搜索引擎理解能力

- 环境变量：
    - 使用`.env`文件管理环境变量，区分开发和生产环境
    - 在`nuxt.config.ts`中使用`runtimeConfig`暴露必要的环境变量，并使用useRuntimeConfig()访问这些值
    - 对需要经常在运行时动态改变的值优先使用环境变量方式，如项目名，密钥，Api地址。然而避免使用过多环境变量。当创建一个新的环境变量后，应更新README.md，增加该变量的介绍，用途，值类型，默认值

- 日志:
    - 编写业务时，使用@utils/logger.ts记录日志
    - 对未向上throw的异常执行日志记录

## 3. Nuxt性能优化

- 服务端渲染策略：
    - 合理选择渲染模式：SSR、SPA或混合模式, /pages/dashboard和/pages/admin目录下的所有页面可以使用SPA，不需要SSR渲染
    - 考虑使用`<ClientOnly>`组件包装仅客户端渲染的内容
    - 注意服务端代码中不应访问浏览器API，使用`process.client`进行环境检查

## 4. 框架配合

- 优先使用shadcn-vue组件，组件使用时,可以不用加Ui前缀(使用<Button/>而不是<UiButton/>)
- 和tailwindcss配合

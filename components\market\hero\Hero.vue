<script setup lang="ts">
import AvatarCircles from '@/components/common/AvatarCircles.vue'
import {heroConfig} from '@/config/hero'
import {getLocalizedConfigText} from '@/utils/i18n'
import VideoPlayer from '~/components/common/VideoPlayer.vue'

// 从配置中获取非本地化数据
const {heroImage, heroVideo, testimonial: rawTestimonial} = heroConfig

// 本地化文本计算属性
const localizedHeadline = computed(() => getLocalizedConfigText(heroConfig.headline))
const localizedSubHeadline = computed(() => getLocalizedConfigText(heroConfig.subHeadline))
const localizedCtaText = computed(() => getLocalizedConfigText(heroConfig.ctaText))
const localizedTestimonialDesc = computed(() => getLocalizedConfigText(rawTestimonial.descText))

// 计算传递给 AvatarCircles 的 numPeople (溢出数量)
// 如果总数大于显示的头像数，则计算差值，否则为0
const overflowCount = computed(() => {
    const displayedAvatarsCount = rawTestimonial.avatars.length
    if (rawTestimonial.totalCount > displayedAvatarsCount) {
        return rawTestimonial.totalCount - displayedAvatarsCount
    }
    return 0
})

// 计算是否显示视频（视频配置存在且有效时优先显示视频）
const showVideo = computed(() => {
    return !!heroVideo
        && !!heroVideo.thumbnailSrc
        && !!heroVideo.videoSrc
})

// 计算是否显示右侧媒体区域（当存在有效图片或视频时显示）
const showMediaSection = computed(() => {
    return (!!heroImage) || (showVideo.value)
})

// 为左侧内容区域动态计算class
const leftContentClasses = computed(() => {
    let classes = 'space-y-10'
    if (showMediaSection.value) {
        // 有媒体时：小屏居中，中屏及以上左对齐
        classes += ' text-center md:text-left'
    } else {
        // 无媒体时：内容块内文本居中，整个块在父容器内水平居中，但不限制最大宽度到3xl
        classes += ' text-center mx-auto'
        // max-w-3xl 已被移除，它将占据其父网格单元的全部可用宽度
    }
    return classes
})

// 为按钮容器动态计算class (确保在单列时按钮组也居中)
const actionsContainerClasses = computed(() => {
    let classes = 'flex flex-col gap-3 sm:flex-row'
    if (showMediaSection.value) {
        classes += ' sm:justify-center md:justify-start'
    } else {
        classes += ' justify-center'
    }
    return classes
})

// 为testimonial容器动态计算class
const testimonialContainerClasses = computed(() => {
    let classes = 'flex items-center space-x-3'
    if (showMediaSection.value) {
        classes += ' justify-center md:justify-start'
    } else {
        classes += ' justify-center'
    }
    return classes
})
</script>

<template>
  <section class="py-12 md:py-24 lg:py-32">
    <div class="container mx-auto">
      <div class="grid gap-8" :class="{ 'md:grid-cols-2 md:items-center': showMediaSection }">
        <!-- 左侧内容区域 -->
        <div :class="leftContentClasses">
          <slot name="header"/>

          <slot name="headline" :text="localizedHeadline">
            <h1 class="text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl"
                v-html="localizedHeadline"/>
          </slot>

          <slot name="subHeadline" :text="localizedSubHeadline">
            <p class="text-lg text-muted-foreground md:text-xl" v-html="localizedSubHeadline"/>
          </slot>

          <slot name="actions" :cta-text="localizedCtaText">
            <div :class="actionsContainerClasses">
              <Button size="lg" class="min-w-48">
                {{ localizedCtaText }}
                <Icon name="heroicons:arrow-long-right-20-solid" class="ml-2 h-5 w-5"/>
              </Button>
            </div>
          </slot>

          <slot name="testimonial" :testimonial="rawTestimonial" :description="localizedTestimonialDesc">
            <div :class="testimonialContainerClasses">
              <!-- 使用 AvatarCircles 组件替换原先的头像展示 -->
              <AvatarCircles :avatar-urls="rawTestimonial.avatars"
                             :num-people="overflowCount"
                             :show-people-num="false"
                             avatar-height-class="h-10"
                             avatar-width-class="w-10"
                             class="-ml-1"/>
              <div class="text-left">
                <p class="text-base font-semibold sm:text-lg">
                  {{ rawTestimonial.totalCount.toLocaleString() }}
                </p>
                <p class="text-xs text-muted-foreground sm:text-sm">
                  {{ localizedTestimonialDesc }}
                </p>
              </div>
            </div>
          </slot>
          <slot name="footer"/>
        </div>

        <!-- 右侧媒体区域（视频或图片）- 只在有媒体内容时显示 -->
        <div v-if="showMediaSection" class="flex justify-center">
          <slot name="media"
                :image="heroImage"
                :video="heroVideo"
                :show-video="showVideo">
            <!-- 优先显示视频（如果配置了视频） -->
            <VideoPlayer v-if="showVideo && heroVideo"
                         :thumbnail-src="heroVideo.thumbnailSrc"
                         :video-src="heroVideo.videoSrc"
                         :playback-mode="heroVideo.playbackMode"
                         class="w-full max-w-xl"/>
            <!-- 如果没有视频配置，则显示图片 -->
            <img v-else-if="heroImage"
                 :src="heroImage"
                 class="max-h-80 w-auto rounded-lg object-contain sm:max-h-96 md:max-h-screen md:max-h-128">
          </slot>
        </div>
      </div>
    </div>
  </section>
</template>
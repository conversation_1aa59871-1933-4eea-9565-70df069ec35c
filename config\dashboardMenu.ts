// 仪表盘侧边栏导航配置
import type {DashboardConfig} from '~/types/site/dashboardMenuConfig'
import {BookDown, Camera, Heart, LayoutDashboard, Send, TvMinimal, User} from 'lucide-vue-next'

const dashboardMenu: DashboardConfig = {
    sidebarMenus: [
        {
            menus: [
                {
                    title: {en: 'Dashboard', zh: 'Dashboard'},
                    href: '/dashboard',
                    icon: LayoutDashboard
                }
            ]
        },
        {
            label: {en: 'Customer Agent', zh: 'Customer Agent'},
            menus: [
                {

                    title: {en: 'Agent', zh: 'Agent'},
                    href: '',
                    icon: BookDown,
                    defaultOpen: true,
                    children: [
                        {
                            title: {en: 'Chat', zh: '聊天'},
                            href: '/dashboard/customer/chat'
                        },
                        {
                            title: {en: 'Chat', zh: '聊天'},
                            href: '/dashboard/customer/test'
                        }

                    ]
                }
            ]
        },
        {
            label: {en: 'Group Playground', zh: 'Group 实验区'},
            menus: [
                {

                    title: {en: 'Playground', zh: '实验区'},
                    href: '',
                    icon: BookDown,
                    defaultOpen: true,
                    children: [
                        {
                            title: {en: 'History', zh: '历史记录'},
                            href: '/dashboard/app'
                        },
                        {
                            title: {en: 'Starred', zh: '已加星标'},
                            href: '/dashboard/app2'
                        }

                    ]
                },
                {
                    title: {en: 'Models', zh: '模型管理'},
                    href: '',
                    defaultOpen: false,
                    icon: Camera,
                    children: [
                        {
                            title: {en: 'Settings Dev 1', zh: '开发设置1'},
                            href: '/dashboard/dev1'
                        }
                    ]
                }
            ]
        },
        {
            label: {en: 'Playground', zh: '实验区'},
            menus: [
                {
                    title: {en: 'Documentation', zh: '文档中心'},
                    href: '/dashboard/dev2',
                    icon: Heart
                },
                {
                    title: {en: 'History', zh: '历史记录'},
                    href: '/dashboard/user1',
                    icon: Send
                },
                {
                    title: {en: 'Starred', zh: '已加星标'},
                    href: '/dashboard/user2',
                    icon: TvMinimal
                }
            ]
        }
    ],
    sidebarFooterMenus: [
        {
            title: {en: 'Profile', zh: 'Profile'},
            href: '/dashboard/profile',
            icon: User
        }
    ]
}

export default dashboardMenu

<script setup lang="ts">
import {ref} from 'vue'

// 可以传入的props
defineProps<{
    commentCount?: number;
    showShareButtons?: boolean;
}>()

// 定义emit事件
const emit = defineEmits<{
    'toggle-comments': [show: boolean]
}>()

// 评论展开状态
const showComments = ref(false)

// 切换评论显示状态
const toggleComments = () => {
    showComments.value = !showComments.value
    emit('toggle-comments', showComments.value)
}
</script>

<template>
  <div class="mt-12 pt-6 border-t border-border">
    <!-- 互动区域 -->
    <div class="flex items-center justify-between">
      <!-- 评论 -->
      <div class="flex items-center gap-6">
        <!-- 评论按钮 -->
        <button
          class="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
          @click="toggleComments"
        >
          <Icon
            :name="showComments ? 'heroicons:chat-bubble-left-solid' : 'heroicons:chat-bubble-left'"
            class="w-5 h-5"
            :class="{ 'text-primary': showComments }"
          />
          <span>{{ commentCount || 0 }}</span>
        </button>
      </div>

      <!-- 分享按钮 -->
      <div v-if="showShareButtons" class="flex items-center gap-3">
        <button class="text-muted-foreground hover:text-foreground transition-colors">
          <Icon name="heroicons:share" class="w-5 h-5"/>
        </button>
        <button class="text-muted-foreground hover:text-foreground transition-colors">
          <Icon name="fa-brands:weixin" class="w-5 h-5"/>
        </button>
        <button class="text-muted-foreground hover:text-foreground transition-colors">
          <Icon name="heroicons:clipboard-document" class="w-5 h-5"/>
        </button>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="border-t border-border my-8"/>
  </div>
</template>
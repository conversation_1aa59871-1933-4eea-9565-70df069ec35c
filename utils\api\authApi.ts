import {type ApiResponse, Post} from '~/utils/http'
import {apiLogger} from '~/utils/logger'

/**
 * 创建通知相关API
 */
function createAuthApi() {
    // 基础URL
    const baseUrl = '/app/auth'

    /**
     * 订阅邮件通知
     * @param params 订阅参数
     * @returns 订阅结果
     */
    function getAuthSourceList(): Promise<ApiResponse<string[]>> {
        apiLogger.info('获取认证源列表')
        return Post<string[]>(`${baseUrl}/authSourceList`, null, {})
    }

    // 返回API对象
    return {
        getAuthSourceList
    }
}

// 导出通知API实例
export const authApi = createAuthApi()
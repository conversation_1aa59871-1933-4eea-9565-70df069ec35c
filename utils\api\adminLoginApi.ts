import {type ApiResponse, Post} from '~/utils/http'
import logger from '~/utils/logger'
import type {LoginRegister} from '~/types/api'
import {tMsg} from '~/utils/i18n'
import type {LoginResultResponse} from '~/types/api/response/LoginResultResponse'

const API_BASE_URL = '/app/adminUser'

/**
 * 验证参数是否为空或未定义
 * @param value 需要验证的值
 * @returns 如果值为空/未定义/空字符串则返回 true
 */
function isNullOrEmpty(value: unknown): boolean {
    return value === undefined || value === null || value === ''
}

/**
 * 验证必填参数
 * @param params 参数对象
 * @param requiredFields 必填字段数组
 * @returns 验证结果，包含是否有效和错误消息
 */
function validateRequiredParams<T>(params: T, requiredFields: string[]): { isValid: boolean; errorMessage?: string } {
    if (!params) {
        return {isValid: false, errorMessage: '参数对象不能为空'}
    }

    for (const field of requiredFields) {
        if (isNullOrEmpty((params as Record<string, unknown>)[field])) {
            return {
                isValid: false,
                errorMessage: `${field} 是必填参数`
            }
        }
    }

    return {isValid: true}
}

function createAdminLoginApi() {

    /**
     * 玩家通过account和password登录
     */
    function loginByAccount(params: LoginRegister): Promise<ApiResponse<LoginResultResponse>> {
        const validation = validateRequiredParams(params, ['password', 'username'])
        if (!validation.isValid) {
            logger.error('loginByAccount 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: tMsg('api.request_params_invalid')
            })
        }

        logger.info('Calling loginByAccount', {
            username: params.username
        })
        return Post<LoginResultResponse>(`${API_BASE_URL}/login`, params)
    }

    // 返回合并的API对象
    return {
        loginByAccount
    }
}

// 导出用户API
export const adminLoginApi = createAdminLoginApi()

import {createCrudApi} from '~/utils/api'
import type {AdminUserResponse} from '~/types/api/response/admin/AdminUserResponse'

/**
 * <AUTHOR> <br/>
 * @description 管理端用户 <br/>
 * @date 2025-05-16 18:24:14
 */
function createAdminUserApi() {
    const baseUrl = '/adminUser'

    const adminUserCrudApi = createCrudApi<AdminUserResponse>(baseUrl)
    return {
        ...adminUserCrudApi
    }
}

// 导出用户API
export const adminUserApi = createAdminUserApi()
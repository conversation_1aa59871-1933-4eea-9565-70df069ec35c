<script setup lang="ts">
import {computed} from 'vue'
import type {ChatMessage, FileData} from '@/types/chat'
import {Icon} from '#components'
import {formatFileSize} from '~/utils/typeof'

// 定义组件的props
const props = defineProps<{
    // 文件消息对象
    message: ChatMessage
}>()

const fileInfo = computed(() => props.message.clientData as FileData)
</script>

<template>
  <div class="bg-white dark:bg-slate-800 rounded-lg p-3 w-64">
    <a :href="fileInfo.url"
       target="_blank"
       class="flex items-center gap-3"
       :class="{ 'pointer-events-none': !fileInfo.url }">
      <Icon name="lucide:file" class="h-10 w-10 text-gray-400"/>
      <div class="flex-1 overflow-hidden">
        <p class="font-semibold truncate">
          {{ fileInfo.filename }}
        </p>
        <p class="text-xs text-muted-foreground">
          {{ formatFileSize(fileInfo.size ?? 0) }}
        </p>
      </div>
    </a>
    <div v-if="message.uploadProgress !== undefined && message.uploadProgress < 100"
         class="mt-2 h-1 w-full bg-muted rounded-full">
      <div class="h-1 bg-primary rounded-full" :style="{ width: `${message.uploadProgress}%` }"/>
    </div>
  </div>
</template>

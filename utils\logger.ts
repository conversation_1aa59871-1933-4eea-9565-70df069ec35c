/**
 * 日志级别枚举
 */
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    // 不记录任何日志
    NONE = 4
}

/**
 * 日志条目接口
 */
export interface LogEntry {
    level: LogLevel;
    message: string;
    context?: Record<string, unknown>;
    timestamp: string;
    moduleId?: string;
    error?: Error | unknown;
}

/**
 * 日志记录器配置
 */
export interface LoggerConfig {
    level: LogLevel;
    moduleId?: string;
    enableConsole?: boolean;
    enableRemote?: boolean;
    remoteEndpoint?: string;
    maxQueueSize?: number;
}

/**
 * 默认日志配置
 */
const defaultConfig: LoggerConfig = {
    level: import.meta.dev ? LogLevel.DEBUG : LogLevel.ERROR,
    enableConsole: true,
    enableRemote: false,
    maxQueueSize: 100
}

/**
 * 用于存储待发送的远程日志
 */
let logQueue: LogEntry[] = []

/**
 * 格式化错误对象，提取有用信息
 */
function formatError(error: unknown): Record<string, unknown> {
    if (error instanceof Error) {
        return {
            name: error.name,
            message: error.message,
            stack: error.stack
        }
    }

    if (error && typeof error === 'object') {
        return {...error}
    }

    return {value: error}
}

/**
 * 创建带有上下文的格式化日志消息
 */
function formatLog(entry: LogEntry): { message: string, context: unknown } {
    const {level, message, context, timestamp, moduleId, error} = entry

    const levelName = LogLevel[level]
    const moduleInfo = moduleId ? `[${moduleId}]` : ''

    let formattedMessage = `${timestamp} ${levelName}${moduleInfo}: ${message}`

    // if (context && Object.keys(context).length > 0) {
    //     formattedMessage += ` Context: ${JSON.stringify(context)}`
    // }

    if (error) {
        const errorInfo = formatError(error)
        formattedMessage += ` Error: ${JSON.stringify(errorInfo)}`
    }

    return {message: formattedMessage, context}
}

/**
 * 将日志发送到远程服务器
 */
async function sendRemoteLogs(config: LoggerConfig): Promise<void> {
    if (!config.enableRemote || !config.remoteEndpoint || logQueue.length === 0) {
        return
    }

    try {
        // 复制队列并清空原队列
        const logs = [...logQueue]
        logQueue = []

        // 发送日志到远程服务器
        await fetch(config.remoteEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(logs)
        })
    } catch (error) {
        // 如果发送失败，将日志添加回队列
        // 但要确保不超过最大队列大小
        const maxSize = config.maxQueueSize || defaultConfig.maxQueueSize as number
        logQueue = [...logQueue, ...logQueue.slice(0, maxSize - logQueue.length)]

        // 在控制台记录错误（如果启用）
        if (config.enableConsole) {
            console.error('Failed to send logs to remote server:', error)
        }
    }
}

/**
 * Logger类 - 负责记录和管理日志
 */
export class Logger {
    private config: LoggerConfig

    constructor(config: Partial<LoggerConfig> = {}) {
        this.config = {...defaultConfig, ...config}
    }

    /**
     * 记录调试级别的日志
     */
    debug(message: string, context?: Record<string, unknown>): void
    debug(message: string, error?: Error | unknown, context?: Record<string, unknown>): void
    debug(message: string, errorOrContext?: Error | unknown | Record<string, unknown>, extraContext?: Record<string, unknown>): void {
        this.processLog(LogLevel.DEBUG, message, errorOrContext, extraContext)
    }

    /**
     * 记录信息级别的日志
     */
    info(message: string, context?: Record<string, unknown>): void
    info(message: string, error?: Error | unknown, context?: Record<string, unknown>): void
    info(message: string, errorOrContext?: Error | unknown | Record<string, unknown>, extraContext?: Record<string, unknown>): void {
        this.processLog(LogLevel.INFO, message, errorOrContext, extraContext)
    }

    /**
     * 记录警告级别的日志
     */
    warn(message: string, context?: Record<string, unknown>): void
    warn(message: string, error?: Error | unknown, context?: Record<string, unknown>): void
    warn(message: string, errorOrContext?: Error | unknown | Record<string, unknown>, extraContext?: Record<string, unknown>): void {
        this.processLog(LogLevel.WARN, message, errorOrContext, extraContext)
    }

    /**
     * 记录错误级别的日志
     */
    error(message: string, context?: Record<string, unknown>): void
    error(message: string, error?: Error | unknown, context?: Record<string, unknown>): void
    error(message: string, errorOrContext?: Error | unknown | Record<string, unknown>, extraContext?: Record<string, unknown>): void {
        this.processLog(LogLevel.ERROR, message, errorOrContext, extraContext)
    }

    /**
     * 处理日志记录的通用方法
     */
    private processLog(
        level: LogLevel,
        message: string,
        errorOrContext?: Error | unknown | Record<string, unknown>,
        extraContext?: Record<string, unknown>
    ): void {
        // 检查日志级别
        if (level < this.config.level) {
            return
        }

        let error: Error | unknown | undefined
        let context: Record<string, unknown> | undefined

        // 处理参数，判断 errorOrContext 是错误对象还是上下文对象
        if (errorOrContext instanceof Error
            || (errorOrContext && typeof errorOrContext === 'object' && 'stack' in errorOrContext)) {
            // 是错误对象
            error = errorOrContext
            context = extraContext
        } else if (errorOrContext && typeof errorOrContext === 'object') {
            // 是上下文对象
            context = errorOrContext as Record<string, unknown>
            // 检查上下文中是否包含error字段
            if (context.error) {
                error = context.error
                // 创建新的上下文对象，排除error字段
                const {error: _, ...restContext} = context
                context = restContext
            }
        }

        const timestamp = new Date().toISOString()
        const entry: LogEntry = {
            level,
            message,
            context,
            timestamp,
            moduleId: this.config.moduleId,
            error
        }

        // 控制台输出
        if (this.config.enableConsole) {
            const formattedMessage = formatLog(entry)

            switch (level) {
                case LogLevel.DEBUG:
                    if (formattedMessage.context) {
                        console.debug(formattedMessage.message, formattedMessage.context)
                    } else {
                        console.debug(formattedMessage.message)
                    }
                    break
                case LogLevel.INFO:
                    if (formattedMessage.context) {
                        console.info(formattedMessage.message, formattedMessage.context)
                    } else {
                        console.info(formattedMessage.message)
                    }
                    break
                case LogLevel.WARN:
                    if (formattedMessage.context) {
                        console.warn(formattedMessage.message, formattedMessage.context)
                    } else {
                        console.warn(formattedMessage.message)
                    }
                    break
                case LogLevel.ERROR:
                    if (formattedMessage.context) {
                        console.error(formattedMessage.message, formattedMessage.context)
                    } else {
                        console.error(formattedMessage.message)
                    }
                    break
            }
        }

        // 远程日志
        if (this.config.enableRemote) {
            logQueue.push(entry)

            // 如果队列达到一定大小，立即发送
            if (logQueue.length >= (this.config.maxQueueSize || defaultConfig.maxQueueSize as number)) {
                sendRemoteLogs(this.config)
            }
        }
    }

    /**
     * 立即发送所有待发送的日志
     */
    flush(): Promise<void> {
        return sendRemoteLogs(this.config)
    }

    /**
     * 更新日志配置
     */
    updateConfig(config: Partial<LoggerConfig>): void {
        this.config = {...this.config, ...config}
    }
}

/**
 * 创建用于API相关日志的默认记录器
 */
export const apiLogger = new Logger({
    moduleId: 'API',
    level: import.meta.dev ? LogLevel.DEBUG : LogLevel.ERROR
})

/**
 * 默认的应用日志记录器
 */
export const logger = new Logger()

/**
 * 创建特定模块的日志记录器
 */
export function createLogger(moduleId: string, config: Partial<LoggerConfig> = {}): Logger {
    return new Logger({...config, moduleId})
}

export default logger

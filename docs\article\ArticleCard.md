# ArticleCard 组件使用文档

`ArticleCard` 是一个用于展示单个文章预览信息的 Vue 组件。它主要用于文章列表或相关文章推荐等场景，支持通过插槽进行灵活的定制。

## Props (属性)

以下是 `ArticleCard` 组件可接受的 props 列表：

| Prop 名称   | 类型                        | 描述                                     | 是否必需 | 默认值 |
|:----------|:--------------------------|:---------------------------------------|:-----|:----|
| `article` | `ArticleResponse`         | 文章对象，包含文章的各种信息（如标题、内容、封面、创建时间、slug 等）。 | 是    | 无   |
| `class`   | `HTMLAttributes['class']` | 应用于根 `<article>` 元素的额外 CSS 类。          | 否    | 无   |

*注：`ArticleResponse` 类型通常定义在 `~/types/api` 中。*

## 插槽 (Slots)

`ArticleCard` 组件提供了以下插槽以实现灵活的定制：

| 插槽名称              | 作用域数据                      | 描述                                                                                                                 |
|:------------------|:---------------------------|:-------------------------------------------------------------------------------------------------------------------|
| `before-layout`   | `article: ArticleResponse` | 在文章卡片主要 `<article>` 标签开始之前插入内容。                                                                                    |
| `article-cover`   | `article: ArticleResponse` | 自定义文章封面的显示。默认情况下，如果 `article.cover` 存在，会渲染一个 `<img>` 标签来显示封面图片。                                                    |
| `article-detail`  | `article: ArticleResponse` | 自定义文章详细信息的主体区域。如果提供此插槽，它将替换掉默认的包含 `article-attr`、`article-content` 和 `after-article` 的结构。                          |
| `article-attr`    | `article: ArticleResponse` | (在 `article-detail` 的默认内容中) 自定义文章属性（如发布时间）的显示区域。默认实现下，它显示文章的格式化后的发布时间。                                             |
| `article-content` | `article: ArticleResponse` | (在 `article-detail` 的默认内容中) 自定义文章主要内容区域的显示（不包括属性和 `after-article` 部分）。默认渲染文章标题 (链接到文章页面) 和文章摘要 (Markdown 格式会自动去除)。 |
| `after-article`   | `article: ArticleResponse` | (在 `article-detail` 的默认内容中) 在文章摘要之后插入额外内容。其外部 `div` 会在此插槽有内容时自动添加 `mt-4` (margin-top: 1rem) 的上边距。                  |
| `after-layout`    | `article: ArticleResponse` | 在文章卡片主要 `<article>` 标签结束之后插入内容。                                                                                    |

## 数据依赖

- `article` Prop: 组件的核心数据来源，应符合 `ArticleResponse` 类型定义。
- 内部工具函数:
    - `formatDate(dateString: string)`: 用于将日期字符串格式化为本地化的可读格式（例如 "Oct 20, 2023"）。
    - `resolveLocalePath(path: string)`: 用于生成本地化的 NuxtLink 路径。
    - `stripMarkdown(content: string)`: 用于从文章内容中移除 Markdown 标记，以显示纯文本摘要。
  - `cn(...inputs: ClassValue[])`: 用于动态合并 Tailwind CSS 类名。

## 基本用法示例

假设你有一个 `ArticleResponse` 类型的 `sampleArticle` 对象：

```vue
<template>
	<div class="p-4">
      <ArticleCard :article="sampleArticle"/>
	</div>
</template>

<script setup lang="ts">
  import type {ArticleResponse} from '~/types/api';
	// ArticleCard 组件通常会自动导入 (Nuxt 3项目特性)
	// import ArticleCard from '~/components/article/ArticleCard.vue';

  const sampleArticle: ArticleResponse = {
		id: '1',
		title: '探索Nuxt 3的奇妙世界',
		slug: 'exploring-nuxt-3',
		content: 'Nuxt 3 带来了许多令人兴奋的新特性，例如 **Vite** 支持、**Composition API** 的深度集成...',
		cover: 'https://images.unsplash.com/photo-1605647540924-852290ab8b58?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
		createTime: '2023-10-20T10:00:00Z',
    // ... 其他 ArticleResponse 所需的字段
		status: 1,
		authorId: 'author1',
		categoryId: 'cat1',
		tags: ['Nuxt', 'Vue', 'TypeScript'],
		views: 1024,
		isTop: false,
		isFeatured: true,
		updateTime: '2023-10-21T10:00:00Z',
		customData: {},
	};
</script>
```

## 使用自定义插槽示例

```vue
<template>
	<div class="max-w-md mx-auto bg-slate-800 p-4 rounded-lg">
      <ArticleCard :article="sampleArticle">
        <template #before-layout="{ article }">
          <p class="text-xs text-accent mb-2">自定义前置内容 - 文章ID: {{ article.id }}</p>
			</template>

        <template #article-cover="{ article }">
				<div class="aspect-video overflow-hidden rounded-md mb-4 border-2 border-primary">
					<img
                            v-if="article.cover"
                            :src="article.cover"
                            :alt="article.title"
							class="w-full h-full object-contain transition-transform duration-500 hover:scale-110"
					>
					<div v-else class="w-full h-full bg-muted flex items-center justify-center text-muted-foreground">
						无封面
					</div>
				</div>
			</template>

        <!-- 示例：完全自定义 article-detail 插槽 -->
        <template #article-detail="{ article }">
          <div class="p-4 bg-gradient-to-br from-purple-600 to-blue-500 rounded-b-lg">
            <h3 class="text-xl font-bold text-white mb-2">
              <NuxtLink :to="resolveLocalePath(`/posts/${article.slug}`)" class="hover:underline">
                {{ article.title }} (Custom Detail)
              </NuxtLink>
            </h3>
            <p class="text-sm text-blue-200 line-clamp-2 mb-3">
              摘要 (自定义): {{ stripMarkdown(article.content).substring(0, 50) }}...
            </p>
            <div class="text-xs text-blue-300">
              发布于: {{ new Date(article.createTime).toLocaleDateString('zh-CN') }}
            </div>
            <!-- after-article slot can be used here if needed -->
            <slot name="after-article" :article="article"></slot>
          </div>
        </template>

        <!-- 如果不提供 #article-detail，则可以使用 #article-attr 和 #article-content -->
        <!--
        <template #article-attr="{ article }">
            <div class="text-xs text-amber-400 mb-2">
                    <time :datetime="article.createTime">{{ new Date(article.createTime).toLocaleDateString('zh-CN') }}</time>
                    <span class="mx-1">·</span>
                    <span>阅读量: {{ article.views }}</span>
            </div>
        </template>

        <template #article-content="{ article }">
            <h3 class="text-xl font-bold text-sky-400 mb-3 leading-tight">
                    <NuxtLink
                            :to="resolveLocalePath(`/posts/${article.slug}`)"
                            class="hover:text-sky-300 transition-colors duration-200 line-clamp-2"
                    >
                        {{ article.title }} - *通过插槽修改*
                    </NuxtLink>
            </h3>
            <p class="text-sm text-slate-400 flex-grow line-clamp-3 mb-3">
                    插槽内容: {{ stripMarkdown(article.content).substring(0, 100) }}...
            </p>
        </template>
        -->

        <template #after-article="{ article }">
          <div class="flex justify-between items-center text-xs text-muted-foreground mt-4 border-t border-slate-700 pt-2">
					<span>自定义操作:</span>
            <NuxtLink :to="resolveLocalePath(`/posts/${article.slug}/edit`)" class="text-primary hover:underline">
						编辑文章
					</NuxtLink>
				</div>
			</template>
			
			<template #after-layout>
				<hr class="my-4 border-slate-700">
				<p class="text-xs text-slate-500 text-center">自定义后置内容 - 卡片结束</p>
			</template>
		</ArticleCard>
	</div>
</template>

<script setup lang="ts">
  import type {ArticleResponse} from '~/types/api';
	import {resolveLocalePath} from '~/utils/i18n/utils'; // 确保导入
	import {stripMarkdown} from '~/utils/stringUtils'; // 确保导入
	
	// ArticleCard 组件通常会自动导入 (Nuxt 3项目特性)
	// import ArticleCard from '~/components/article/ArticleCard.vue';

  const sampleArticle: ArticleResponse = {
		id: '2',
		title: '插槽的强大功能',
		slug: 'power-of-slots',
		content: 'Vue 插槽提供了极大的灵活性来自定义组件的渲染输出。这使得组件更加可重用和适应不同的场景。',
		cover: '', // 无封面示例
		createTime: '2023-11-05T14:30:00Z',
		status: 1,
		authorId: 'author2',
		categoryId: 'cat2',
		tags: ['Vue', 'Slots', 'Frontend'],
		views: 512,
		isTop: true,
		isFeatured: false,
		updateTime: '2023-11-05T15:00:00Z',
		customData: {},
	};
</script>
```

**注意**:

- 上述示例中的CSS类名主要基于Tailwind CSS。实际项目中请根据您的样式系统调整。
- 确保 `ArticleResponse` 类型定义与您的项目一致。
- 在实际使用中，`resolveLocalePath` 和 `stripMarkdown` 等工具函数如果已在 `ArticleCard.vue`
  内部导入并使用，则在父组件的插槽中再次使用时，可能需要从相应路径导入（如示例所示），或者通过作用域插槽从 `ArticleCard.vue`
  传递这些函数（如果设计如此）。当前 `ArticleCard.vue` 的设计并未将这些函数通过插槽传递。 
<template>
  <div v-if="shouldHandleError" class="error-container">
    <div class="error-content">
      <p class="error-code">{{ errorCode }}</p>
      <p class="error-text">{{ errorMessage }} 😢</p>
      <div class="error-buttons">
        <Button variant="secondary" class="home-button" @click="navigateToHome">
          <Icon
            name="heroicons-solid:home"
            class="h-5 w-5 rotate-0 scale-100 transition-all "/>
          {{ t('error.home') || 'Home' }}
        </Button>
        <Button variant="secondary" class="support-button" @click="toggleSupportDialog">
          <Icon
            name="heroicons-solid:chat-bubble-left-ellipsis"
            class="h-5 w-5 rotate-0 scale-100 transition-all "/>
          {{ t('error.support') || 'Support' }}
        </Button>
      </div>
    </div>
  </div>
  <SupportForm
    v-model:is-open="isSupportOpen"
    :type="'error'"
    :enable-request-id="false"
  />
</template>

<script setup>
import {useI18n} from 'vue-i18n'
import {Button} from '@/components/ui/button'
import SupportForm from '@/components/common/SupportForm.vue'

const props = defineProps({
    error: Object
})

const {t} = useI18n()

// 支持对话框状态
const isSupportOpen = ref(false)
logger.error('Error', props.error)

// 获取错误代码
const errorCode = computed(() => props.error?.statusCode || 500)

// 获取错误消息
const errorMessage = computed(() => {
    // 如果有错误消息，直接使用
    if (props.error?.message) {
        logger.error(props.error.message)
        // return props.error.message
    }

    // 根据状态码显示不同的消息
    switch (errorCode.value) {
        case 404:
            return t('error.this_page_doesnt_exist') || 'This page doesn\'t exist'
        case 500:
            return t('error.server_error') || 'Server error occurred'
        case 503:
            return t('http.service_unavailable') || 'Service temporarily unavailable'
        default:
            return t('http.unknown_error') || 'An unknown error occurred'
    }
})

// 只处理404和5xx错误
const shouldHandleError = computed(() => {
    const code = errorCode.value
    return code === 404 || code >= 500
})

// 导航到首页
const navigateToHome = () => {
    window.location.href = '/'
}

// 切换支持对话框
const toggleSupportDialog = () => {
    isSupportOpen.value = !isSupportOpen.value
}
</script>

<style scoped>
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  background-color: #000;
  color: #fff;
}

.error-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.error-code {
  font-size: 3rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.8);
}

.error-text {
  font-size: 1.5rem;
  font-weight: 500;
}

.error-buttons {
  display: flex;
  gap: 1rem;
}

.home-button, .support-button {
  min-width: 120px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  transition: background-color 0.2s;
}

.home-button:hover, .support-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>

<script setup lang="ts">
import type {ChatMessage, LocationMessage} from '@/types/chat'

// 定义组件的props
const props = defineProps<{
    // 消息对象
    message: ChatMessage
}>()

const locationInfo = computed(() => props.message.clientData as LocationMessage)
</script>

<template>
  <div class="bg-white dark:bg-slate-800 rounded-lg p-3 max-w-xs">
    <div class="flex items-center gap-3">
      <Icon name="lucide:map-pin" class="w-5 h-5 text-red-500"/>
      <p class="font-semibold text-blue-500">
        {{ locationInfo.location }}
      </p>
    </div>
  </div>
</template>

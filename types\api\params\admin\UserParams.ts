import type {PageParams} from '~/utils/http/types'

/**
 * @description 用户信息 请求参数
 */
export interface UserParams extends PageParams {

    /**
     * 用户id
     */
    userId?: string

    /**
     * 用户账号
     */
    username?: string

    /**
     * 用户手机号
     */
    phone?: string

    /**
     * 用户邮箱
     */
    email?: string

    /**
     * 昵称
     */
    nickname?: string

    /**
     * 头像
     */
    avatar?: string

    /**
     * 项目名(在多项目中使用)
     */
    projectName?: string

    /**
     * 标签
     */
    tag?: string

    /**
     * 用户密码
     */
    password?: string

    /**
     * 盐
     */
    salt?: string

    /**
     * 账号禁用状态 1: 禁用 0: 正常
     */
    disableState?: boolean

    /**
     * 绑定的第三方用户id(issuer_sub)
     */
    thirdSubjectId?: string

    /**
     * 创建时间
     */
    createTime?: Date

    /**
     * 修改时间
     */
    updateTime?: Date

}
<script setup lang="ts">
// 不需要导入组件，直接使用自动注册的组件
import FaqOne from '~/components/market/faqs/FaqOne.vue'
import FaqTwo from '~/components/market/faqs/FaqTwo.vue'
import LogoCloudOne from '~/components/market/logo-cloud/LogoCloudOne.vue'
import siteConfig from '@/config/site'

// 获取本地化文本
// No need to map plan names here anymore if PlanComparison uses the names passed in directly.
// Keep comparisonPlans as is.
// 导入 stripMarkdown 以便在插槽中使用
import BentoGrid from '~/components/market/bento/BentoGrid.vue'
import Hero from '~/components/market/hero/Hero.vue'
import Pricing from '~/components/market/price/Pricing.vue'
import Footer from '~/components/market/footer/Footer.vue'
import BlogPreview from '~/components/article/BlogPreview.vue'
import Testimonial from '~/components/market/testimonials/Testimonial.vue'
import Newsletter from '~/components/market/newsletter/Newsletter.vue'
import Team from '~/components/market/team/Team.vue'
import LogoCloudTwo from '~/components/market/logo-cloud/LogoCloudTwo.vue'
import {getLocalizedConfigText} from '~/utils/i18n'
// AutoScrollContainer 会被自动导入
// import AutoScrollContainer from '~/components/common/AutoScrollContainer.vue'

defineOgImageComponent('NuxtSeo', {
    description: 'Look at me in dark mode  👋',
    theme: '#ff0000',
    colorMode: 'dark',
    icon: 'material-symbols:30fps-rounded',
    title: '中文測試中文測試中文測試中文測試中文測試中文測試中文測試中文測試',
    siteLogo: '/logo.png'
})

useSeoMeta({
    title: 'this is home page',
    titleTemplate: (titleChunk) => {
        console.log(`titleChunk: ${titleChunk}`)
        return titleChunk ? `${titleChunk} - ${getLocalizedConfigText(siteConfig.title)}` : `${getLocalizedConfigText(siteConfig.title)}`
    }
})

</script>

<template>
  <div>
    <Hero/>

    <Pricing/>

    <BentoGrid mobile-layout="overlay"/>

    <FaqOne :expand-all="false" content-position="left"/>
    <FaqTwo/>

    <LogoCloudOne mobile-display-mode="scroll"/>

    <LogoCloudTwo mobile-display-mode="scroll">
      <template #action>
        <div class="mt-10 flex items-center gap-x-6">
          <NuxtLink to="/">
            <Button size="lg">
              创建账户
            </Button>
          </NuxtLink>
          <NuxtLink to="/">
            <Button variant="link" class="px-0 text-foreground">
              联系我们
              <Icon name="lucide:arrow-right" class="ml-2 h-4 w-4"/>
            </Button>
          </NuxtLink>
        </div>
      </template>
    </LogoCloudTwo>

    <Team layout="vertical"
          avatar-shape="circle"
          :show-social-icons="true"
          member-info-layout="horizontal"
          :items-per-row-sm="4"
          show-detailed-description/>
    <Newsletter layout="vertical" content-align="center"/>

    <!--新增 TeamOne 示例 -->

    <Testimonial :pc="{ displayStyle: 'default', rows: 3 }" :mobile="{ displayStyle: 'overlay', rows: 4 }"/>
    <BlogPreview :pc="{ displayStyle: 'default' }" :mobile="{ displayStyle: 'overlay', rows: 2 }"/>
    <Footer :show-site-details="true"
            site-details-layout="left"
            :show-social-links-in-site-details="true"
            :show-social-links-in-copyright="true"/>

  </div>
</template>

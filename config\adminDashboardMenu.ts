// 仪表盘侧边栏导航配置

import type {DashboardConfig} from '~/types/site/dashboardMenuConfig'

const dashboardMenu: DashboardConfig = {
    sidebarMenus: [
        {
            label: {en: 'Group Playground', zh: 'Group 实验区'},
            menus: [
                {

                    title: {en: 'Playground', zh: '实验区'},
                    href: '',
                    icon: 'lucide:square-terminal',
                    defaultOpen: true,
                    children: [
                        {
                            title: {en: 'History', zh: '历史记录'},
                            href: '/admin/app'
                        },
                        {
                            title: {en: 'Starred', zh: '已加星标'},
                            href: '/admin/app2'
                        }

                    ]
                },
                {
                    title: {en: 'Models', zh: '模型管理'},
                    href: '',
                    defaultOpen: false,
                    icon: 'lucide:bot',
                    children: [
                        {
                            title: {en: 'Settings Dev 1', zh: '开发设置1'},
                            href: '/admin/dev1'
                        }
                    ]
                }
            ]
        },
        {
            label: {en: 'Playground', zh: '实验区'},
            menus: [
                {
                    title: {en: 'Documentation', zh: '文档中心'},
                    href: '/admin/dev2',
                    icon: 'lucide:book-open'
                },
                {
                    title: {en: 'History', zh: '历史记录'},
                    href: '/admin/user1'
                },
                {
                    title: {en: 'Starred', zh: '已加星标'},
                    href: '/admin/user2'
                }
            ]
        }
    ]
}

export default dashboardMenu
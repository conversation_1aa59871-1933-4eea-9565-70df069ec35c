import {createCrudApi} from '~/utils/api'
import type {ThirdPartUserResponse} from '~/types/api/response/admin/ThirdPartUserResponse'

/**
 * <AUTHOR> <br/>
 * @description 第三方平台用户 <br/>
 * @date 2025-05-16 18:24:14
 */
function createThirdPartUserApi() {
    const baseUrl = '/thirdPartUser'

    const thirdPartUserCrudApi = createCrudApi<ThirdPartUserResponse>(baseUrl)
    return {
        ...thirdPartUserCrudApi
    }
}

// 导出用户API
export const thirdPartUserApi = createThirdPartUserApi()

thirdPartUserApi.create({
    username: 'asd'
})
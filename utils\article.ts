/**
 * 计算文章阅读时间
 * 按照中文阅读速度每分钟500字计算
 * @param content 文章内容
 * @returns 阅读时间（分钟）
 */
export function calculateReadingTime(content: string): string {
    if (!content) {
        return '1'
    }

    // 去除所有HTML标签
    const plainText = content.replace(/<[^>]*>/g, '')

    // 计算字数
    const wordCount = plainText.trim().length

    // 按照每分钟阅读500字计算
    const readingTime = Math.max(1, Math.ceil(wordCount / 500))

    return readingTime.toString()
}

/**
 * 获取文章摘要
 * @param content 文章内容
 * @param maxLength 最大长度
 * @returns 文章摘要
 */
export function getArticleExcerpt(content: string, maxLength: number = 200): string {
    if (!content) {
        return ''
    }

    // 去除所有HTML标签
    const plainText = content.replace(/<[^>]*>/g, '')

    // 截取指定长度
    if (plainText.length <= maxLength) {
        return plainText
    }

    return `${plainText.slice(0, maxLength)}...`
}

/**
 * 格式化日期
 * @param dateString 日期字符串
 * @returns 格式化后的日期
 */
export function formatDate(dateString?: string): string {
    if (!dateString) {
        return ''
    }

    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}
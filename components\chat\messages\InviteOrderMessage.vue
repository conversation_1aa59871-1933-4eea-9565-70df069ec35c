<script setup lang="ts">
import {computed, ref} from 'vue'
import type {ChatMessage, InviteOrderMessage} from '@/types/chat'
import {Icon} from '#components'
import {copyToClipboard} from '~/composables/useClipboard'
import ProductShowcase from '~/components/chat/messages/ProductShowcase.vue'

// 定义组件的props
const props = defineProps<{
    // 商品消息对象
    message: ChatMessage
}>()

const inviteInfo = computed(() => props.message.clientData as InviteOrderMessage)
const productInfo = computed(() => inviteInfo.value.product)

const isCopied = ref(false)

async function handleCopy() {
    const success = await copyToClipboard(productInfo.value.productUrl, false)
    if (success) {
        isCopied.value = true
        setTimeout(() => {
            isCopied.value = false
        }, 2000)
    }
}
</script>

<template>
  <div class="bg-white dark:bg-slate-800 rounded-lg p-3 max-w-xs w-80">
    <!-- 意图 -->
    <div v-if="message.list[0]?.messageContent"
         class="text-xs text-muted-foreground border-l-2 border-slate-200 dark:border-slate-700 pl-2 mb-3">
      {{ message.list[0].messageContent }}
    </div>

    <!-- 商品信息 -->
    <!--<div class="flex items-center gap-3">-->
    <!--  <img :src="productInfo.picture" :alt="productInfo.title" class="w-16 h-16 rounded-md">-->
    <!--  <div class="flex flex-col justify-between flex-1">-->
    <!--    <a :href="productInfo.productUrl" target="_blank" class="hover:underline">-->
    <!--      <p class="font-semibold line-clamp-2 text-blue-500">-->
    <!--        {{ productInfo.title }}-->
    <!--      </p>-->
    <!--    </a>-->
    <!--    <div v-if="productInfo.itemList && productInfo.itemList.length > 0">-->
    <!--      <div v-for="item in productInfo.itemList" :key="item.skuId" class="text-sm text-muted-foreground mt-1">-->
    <!--        <p v-if="item.skuTitle">-->
    <!--          规格: {{ item.skuTitle }}-->
    <!--        </p>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--  </div>-->
    <!--</div>-->

    <product-showcase :product-info="productInfo"/>

    <!-- 邀请操作 -->
    <div class="pt-2 border-t border-slate-200 dark:border-slate-700">
      <div class="flex justify-between items-center mb-2">
        <span class="text-sm font-semibold">邀请您下单</span>
        <span v-if="inviteInfo.count" class="text-xs text-muted-foreground">总数量: x{{ inviteInfo.count }}</span>
      </div>
      <button
        class="flex items-center justify-center w-full bg-primary text-primary-foreground h-9 rounded-md text-sm font-medium hover:bg-primary/90 disabled:opacity-50"
        :disabled="isCopied"
        @click="handleCopy">
        <Icon v-if="!isCopied" name="heroicons:link-20-solid" class="h-4 w-4 mr-1"/>
        <Icon v-else name="heroicons:check-20-solid" class="h-4 w-4 mr-1"/>
        {{ isCopied ? '已复制' : '复制链接' }}
      </button>
    </div>
  </div>
</template>

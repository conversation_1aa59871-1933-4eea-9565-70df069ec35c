// WebSocket工具，提供事件注册、消息发送、心跳、自动重连等功能
// 用法示例：
// import {createWebSocket, WebSocketOptions} from '@/utils/api/ws'
// const ws = createWebSocket({
//   serverUrl: 'ws://127.0.0.1:40900/customer/agent',
//   nodeName: '客户端-im',
//   nodeType: 'CONTROLLER',
//   token: 'xxx-your-token-xxx'
// })
// ws.registerEventCallback(...)
// ws.send(...)
// ws.close()

import {logger} from '@/utils/logger'
import {getToken} from '~/utils/http/auth'
import {tMsg} from '~/utils/i18n'
import type {WebSocketResponse} from '~/utils/http/types'
import type {MCPRequest, MCPResponse} from '~/types/aigc'

export interface WebSocketOptions {
    serverUri: string
    nodeName: string
    nodeType?: string
    token?: string
    toolCallEventType: string;
}

// WebSocket事件回调类型
type WsEventCallback<T = unknown> = (data: WebSocketResponse<T>, event: Event) => void

// 事件监听Map类型
// 使用 'unknown' 是因为Map需要存储不同事件、不同数据类型的回调函数
type EventListenerMap = Map<string, Map<string, WsEventCallback<unknown>>>

// 定义队列中消息的类型
interface QueuedMessage {
    type: 'send' | 'sendForwardResponse'
    data: unknown
    eventType: string
    metadata?: Record<string, unknown>
    requestId?: string
}

// WebSocket客户端类型
export type WebSocketClient = {
    registerEventCallback: <T = unknown>(method: WsEventCallback<T>, eventType: string, requestId?: string) => string
    registerResponseForwardEventCallback: <T = unknown>(method: WsEventCallback<T>, eventType: string, requestId?: string) => string
    registerRequestForwardEventCallback: <T = unknown>(method: WsEventCallback<T>, eventType: string) => string
    registerToolCallEventRequestCallback: <T = unknown>(method: WsEventCallback<MCPRequest<T>>, toolMethodName: string) => string
    registerToolCallEventResponseCallback: <T = unknown>(method: WsEventCallback<MCPResponse<T>>, toolMethodName: string, requestId?: string) => string
    removeCallback: (eventType: string, requestId?: string) => void
    send: <T>(
        data: T,
        eventType: string,
        metadata?: Record<string, unknown>,
        requestId?: string,
        retries?: number
    ) => Promise<boolean>
    sendForwardResponse: <T>(
        data: T,
        eventType: string,
        metadata?: Record<string, unknown>,
        requestId?: string,
        retries?: number
    ) => Promise<boolean>
    close: () => void
    open: () => Promise<void>
}

export function createWebSocket(options: WebSocketOptions): {
    sendForwardResponse: <T>(data: T, eventType: string, metadata?: Record<string, unknown>, requestId?: string) => Promise<boolean>;
    registerEventCallback: <T = unknown>(method: WsEventCallback<T>, eventType: string, requestId?: string) => string;
    registerRequestForwardEventCallback: <T = unknown>(method: WsEventCallback<T>, eventType: string) => string;
    registerResponseForwardEventCallback: <T = unknown>(method: WsEventCallback<T>, eventType: string, requestId?: string) => string;
    registerToolCallEventRequestCallback: <T = unknown>(method: WsEventCallback<MCPRequest<T>>, toolMethodName: string) => string;
    registerToolCallEventResponseCallback: <T = unknown>(method: WsEventCallback<MCPResponse<T>>, toolMethodName: string, requestId?: string) => string;
    removeCallback: (eventType: string, requestId?: string) => void;

    send: <T>(data: T, eventType: string, metadata?: Record<string, unknown>, requestId?: string) => Promise<boolean>;
    close: () => void;
    open: () => Promise<void>
} {
    const eventListenerMap: EventListenerMap = new Map()
    let webSocket: WebSocket | undefined = undefined
    let heartbeatTestTimer: ReturnType<typeof setInterval> | undefined = undefined
    let reconnectTimer: ReturnType<typeof setTimeout> | undefined = undefined
    // ws链接是否是明确关闭
    let isExplicitClose = false
    // WebSocket连接是否正在打开中
    let isOpening = false
    const heartbeatIntervalTime = 30000
    // 未发送消息队列
    const messageQueue: QueuedMessage[] = []
    let pendingMessageTimer: ReturnType<typeof setInterval> | undefined = undefined

    function registerEventCallback<T = unknown>(method: WsEventCallback<T>, eventType: string, requestId?: string): string {
        let eventMap = eventListenerMap.get(eventType)
        if (!eventMap) {
            eventMap = new Map<string, WsEventCallback<unknown>>()
        }
        if (!requestId) {
            requestId = 'default'
        }
        eventMap.set(requestId, method as WsEventCallback<unknown>)
        eventListenerMap.set(eventType, eventMap)
        logger.info(`eventType: ${eventType}, requestId: ${requestId} 的回调监听器已注册成功`)
        return requestId
    }

    function removeCallback(eventType: string, requestId?: string) {
        const eventMap = eventListenerMap.get(eventType)
        if (!eventMap) {
            return
        }
        if (!requestId) {
            requestId = 'default'
        }
        eventMap.delete(requestId)
        logger.info(`eventType: ${eventType}, requestId: ${requestId} 的回调监听器已被成功移除`)
    }

    function registerToolCallEventResponseCallback<T = unknown>(method: WsEventCallback<MCPResponse<T>>, toolMethodName: string, requestId?: string): string {
        const eventType = `FORWARD__RESPONSE${options.toolCallEventType}`
        if (!requestId) {
            requestId = 'default'
        }
        requestId = `${toolMethodName}_${requestId}`
        const id = registerEventCallback(method, eventType, requestId)
        logger.info(`工具调用响应回调监听器 toolMethodName: ${toolMethodName}, requestId: ${requestId} 已注册成功`)
        return id
    }

    function registerToolCallEventRequestCallback<T = unknown>(method: WsEventCallback<MCPRequest<T>>, toolMethodName: string): string {
        const eventType = `FORWARD__REQUEST${options.toolCallEventType}`
        const requestId = `${toolMethodName}_default`
        const id = registerEventCallback(method, eventType, requestId)
        logger.info(`工具调用请求回调监听器 toolMethodName: ${toolMethodName}, requestId: ${requestId} 已注册成功`)
        return id
    }

    function registerRequestForwardEventCallback<T = unknown>(method: WsEventCallback<T>, eventType: string): string {
        eventType = `FORWARD__REQUEST${eventType}`
        return registerEventCallback(method, eventType)
    }

    function registerResponseForwardEventCallback<T = unknown>(method: WsEventCallback<T>, eventType: string, requestId?: string): string {
        eventType = `FORWARD__RESPONSE${eventType}`
        return registerEventCallback(method, eventType, requestId)
    }

    async function send<T>(
        data: T,
        eventType: string,
        metadata?: Record<string, unknown>,
        requestId?: string
    ): Promise<boolean> {
        if (!eventType) {
            logger.error(`eventType为null, 发送 ${JSON.stringify(data)} 失败`)
            return false
        }

        if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
            logger.warn('【send】连接未打开，已将消息加入队列。')
            messageQueue.push({type: 'send', data, eventType, metadata, requestId})
            return false
        }

        const param: Record<string, unknown> = {
            event: eventType,
            requestId: requestId,
            metadata: metadata,
            body: data
        }
        webSocket.send(JSON.stringify(param))
        logger.info(`【${eventType}】WS数据已发送 requestId: ${requestId}, metadata: ${JSON.stringify(metadata)}`)
        return true
    }

    async function sendForwardResponse<T>(
        data: T,
        eventType: string,
        metadata?: Record<string, unknown>,
        requestId?: string
    ): Promise<boolean> {
        if (!eventType) {
            logger.error(`eventType为null, 发送 ${JSON.stringify(data)} 失败`)
            return false
        }

        if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
            logger.warn('【sendForwardResponse】连接未打开，已将消息加入队列。')
            messageQueue.push({type: 'sendForwardResponse', data, eventType, metadata, requestId})
            return false
        }

        const param: Record<string, unknown> = {
            event: eventType,
            requestId: requestId,
            metadata: metadata,
            forwardResponse: true,
            response: data
        }
        webSocket.send(JSON.stringify(param))
        logger.info(`【${eventType}】WS数据已发送 requestId: ${requestId}, metadata: ${JSON.stringify(metadata)}`)
        return true
    }

    function execute(eventType: string, data: Record<string, unknown>, event: Event) {
        // 如果eventType以FORWARD__REQUEST开始, 那么直接为default
        if (eventType.startsWith('FORWARD__REQUEST') && data.requestId) {
            data.requestId = 'default'
        }

        let requestId = (data.requestId as string) || 'default'
        const eventMap = eventListenerMap.get(eventType)
        if (eventMap) {
            // 判断是否是工具调用
            if (eventType === `FORWARD__REQUEST${options.toolCallEventType}` || eventType === `FORWARD__RESPONSE${eventType}`) {
                // 工具调用请求转发
                const toolMethodName = getToolMethodName(data)
                if (!toolMethodName) {
                    return
                }

                requestId = `${toolMethodName}_${requestId}`
            }

            const method = eventMap.get(requestId)
            if (method) {
                logger.info(`【WS】收到eventType: ${eventType} 响应, 正在调用事件处理器执行`, {
                    eventType,
                    data,
                    requestId
                })
                // 此处 data 已经是服务端返回的完整 WebSocketResponse 结构
                method(data as unknown as WebSocketResponse<unknown>, event)
            } else {
                logger.warn(`【WS】收到eventType: ${eventType} 响应, 但该eventType尚未注册事件处理器`, {
                    eventType,
                    data,
                    requestId
                })
            }
        } else {
            logger.warn(`【WS】收到eventType: ${eventType} 响应, 但该eventType尚未注册事件处理器`, {
                eventType,
                data,
                requestId
            })
        }
    }

    function startHeartbeat() {
        logger.info('开始发送心跳')
        if (heartbeatTestTimer) {
            clearInterval(heartbeatTestTimer)
        }
        heartbeatTestTimer = setInterval(() => {
            logger.info('发送心跳包')
            send({}, 'HEART_BEAT', {ignoreServerResponse: true}).catch(() => logger.error('心跳发送失败'))
        }, heartbeatIntervalTime)
    }

    function stopHeartbeat() {
        logger.info('停止发送心跳')
        if (heartbeatTestTimer) {
            clearInterval(heartbeatTestTimer)
            heartbeatTestTimer = undefined
        }
    }

    function open(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (webSocket && webSocket.readyState === WebSocket.OPEN) {
                logger.info('连接已存在, 无需重新打开')
                resolve()
                return
            }
            isExplicitClose = false
            if (isOpening) {
                logger.info('WebSocket正在连接中，等待连接完成')
                // 如果正在连接中，则等待当前连接完成，不发起新的连接
                const checkInterval = setInterval(() => {
                    if (webSocket && webSocket.readyState === WebSocket.OPEN) {
                        clearInterval(checkInterval)
                        resolve()
                        // 如果连接被明确关闭或isOpening状态被重置，则reject
                    } else if (isExplicitClose || !isOpening) {
                        clearInterval(checkInterval)
                        reject(new Error('连接尝试被中断或失败'))
                    }
                    // 每100ms检查一次
                }, 100)
                return
            }

            // 设置为正在打开状态
            isOpening = true
            if (reconnectTimer) {
                clearTimeout(reconnectTimer)
                reconnectTimer = undefined
            }

            let {serverUri, nodeName, nodeType, token} = options
            if (!token) {
                token = getToken() as string
            }

            if (!nodeType) {
                nodeType = 'ADMIN'
            }

            if (!serverUri) {
                logger.error('WebSocket参数缺失，请检查serverUri')
                isOpening = false
                reject(new Error('WebSocket参数缺失'))
                return
            }

            const serverUrl = getRequestUrl(serverUri)
            const connectUrl = `${serverUrl}?nodeName=${encodeURIComponent(nodeName)}&nodeType=${encodeURIComponent(nodeType)}&token=${encodeURIComponent(token)}`
            webSocket = new WebSocket(connectUrl)
            webSocket.addEventListener('open', (event: Event) => {
                logger.info(`已成功连接到Im服务器 连接地址 --> ${connectUrl}`)
                startHeartbeat()
                execute('CUSTOM_OPEN', {}, event)
                isOpening = false
                resolve()
            })

            webSocket.addEventListener('close', (event: Event) => {
                handleConnectionClosed(event)
                isOpening = false
                reject(event)
            })

            webSocket.addEventListener('error', (event: Event) => {
                handleConnectionClosed(event)
                isOpening = false
                reject(event)
            })

            webSocket.addEventListener('message', (event: MessageEvent) => {
                const {data} = event
                if (!data) {
                    logger.error('【WS】收到的数据为空')
                    return
                }

                let parsedData: Record<string, unknown>
                try {
                    parsedData = JSON.parse(data)
                } catch {
                    logger.error('【WS】无法将数据反序列化为对象', {data})
                    return
                }

                let eventType = parsedData['eventType'] as string
                if ('ERROR' === eventType) {
                    const originalEventType = parsedData['originalEventType'] as string
                    if (originalEventType) {
                        eventType = originalEventType
                    }

                    let errorNotice
                    if (parsedData.message) {
                        errorNotice = parsedData.message
                    } else {
                        errorNotice = parsedData
                    }
                    logger.error(`【WS响应异常】eventType: ${eventType}, error: ${errorNotice}`)
                }
                execute(eventType, parsedData, event)
            })
        })
    }

    function close() {
        logger.info('手动执行close操作')
        isExplicitClose = true
        stopHeartbeat()
        if (reconnectTimer) {
            clearTimeout(reconnectTimer)
            reconnectTimer = undefined
        }
        if (webSocket) {
            webSocket.close()
        }
    }

    function handleConnectionClosed(event: Event) {
        execute('CUSTOM_CLOSE', {}, event)
        stopHeartbeat()
        if (isExplicitClose) {
            logger.warn('主动断开连接, 不进行重连')
            return
        }

        if (reconnectTimer) {
            clearTimeout(reconnectTimer)
        }

        reconnectTimer = setTimeout(() => {
            logger.warn('正在尝试重新连接...')
            open().catch(() => {
                // 重新连接失败会自动触发 close/error 事件，无需在此处理
            })
        }, 3000)
    }

    // 新增：处理队列中的消息
    async function processMessageQueue() {
        if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
            logger.info('WebSocket未打开，尝试打开连接...')
            try {
                await open()
            } catch (error) {
                logger.error('打开WebSocket连接失败:', error)
                // 如果连接失败，则不处理队列
                return
            }
        }

        // 如果连接成功打开，处理队列中的消息
        if (webSocket && webSocket.readyState === WebSocket.OPEN) {
            while (messageQueue.length > 0) {
                const message = messageQueue.shift()
                if (message) {
                    let sendStatus
                    if (message.type === 'send') {
                        // 调用内部的send方法，不再带retries参数
                        sendStatus = await send(message.data, message.eventType, message.metadata, message.requestId)
                    } else if (message.type === 'sendForwardResponse') {
                        // 调用内部的sendForwardResponse方法，不再带retries参数
                        sendStatus = await sendForwardResponse(message.data, message.eventType, message.metadata, message.requestId)
                    }

                    logger.info(`【WS待发送队列】eventType: ${message.eventType} data: ${message.data}, metadata: ${message.metadata}, requestId: ${message.requestId} 重新发送状态 --> ${sendStatus}`)
                    if (!sendStatus) {
                        logger.warn(`【WS待发送队列】eventType: ${message.eventType} data: ${message.data}, metadata: ${message.metadata}, requestId: ${message.requestId} 未发送成功, 已将其重新放入发送队列`)
                        messageQueue.push(message)
                    }
                }
            }
        }
    }

    // 启动定时器，定期处理消息队列
    pendingMessageTimer = setInterval(processMessageQueue, 1500)

    return {
        registerEventCallback,
        removeCallback,
        registerRequestForwardEventCallback,
        registerResponseForwardEventCallback,
        registerToolCallEventRequestCallback,
        registerToolCallEventResponseCallback,
        send,
        sendForwardResponse,
        close,
        open
    }
}

/**
 * 获取完整的WSUrl
 * @param uri 请求URI
 * @returns 完整的请求URL
 */
function getRequestUrl(uri: string): string {
    // 如果是完整URL则直接返回
    if (uri.startsWith('ws') || uri.startsWith('wss')) {
        return uri
    }

    const config = useRuntimeConfig()
    // 获取API基础URL
    const wsApiBase = config.public.wsApiBase as string
    if (!wsApiBase) {
        throw new Error(tMsg('http.api_base_not_configured'))
    }

    // 标准化URI和基础URL
    const normalizedUri = uri.startsWith('/') ? uri.slice(1) : uri
    const normalizedBase = wsApiBase.endsWith('/') ? wsApiBase.slice(0, -1) : wsApiBase

    // 拼接完整URL
    return `${normalizedBase}/${normalizedUri}`
}

function getToolMethodName(data: Record<string, unknown>): string | null {
    const metadata: Record<string, unknown> | null = data['metadata'] as Record<string, unknown>
    if (!metadata || !metadata['toolMethodName']) {
        logger.error('从发从当前工具调用转发的metadata中获取到toolMethodName', data)
        return null
    }

    return String(metadata['toolMethodName'])
}

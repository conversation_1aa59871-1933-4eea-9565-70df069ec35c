import {toast} from 'vue-sonner'
import {h, ref} from 'vue'
import {copyToClipboard} from './useClipboard'

// 位置类型
type MessagePosition = 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'

// 操作按钮配置
interface ActionButton {
    label: string
    onClick: () => void
}

// 取消按钮配置
interface CancelButton {
    label: string
    onClick: () => void
}

// 消息选项接口
interface MessageOptions {
    title?: string
    description?: string
    duration?: number
    dismissible?: boolean
    position?: MessagePosition
    action?: ActionButton
    cancel?: CancelButton
    // 是否显示"联系支持"按钮（仅在错误消息中有效）
    showSupport?: boolean
    // 请求ID（传递给支持表单）
    requestId?: string
}

// Promise状态选项
interface PromiseOptions {
    loading: string
    success: string | ((data: unknown) => { title?: string; description?: string })
    error: string | ((err: Error) => {
        title?: string;
        description?: string;
        showSupport?: boolean;
        requestId?: string
    })
}

// 默认配置
const defaultOptions = {
    position: 'top-right' as MessagePosition,
    duration: 3000,
    dismissible: true,
    showSupport: false
}

// 支持表单控制
const isSupportFormOpen = ref(false)
const supportFormType = ref('')
const supportFormRequestId = ref('')

// 默认文本
const defaultTranslations = {
    'message.contact_support': '联系支持'
}

/**
 * 获取翻译
 * @param key 翻译键
 * @returns 翻译后的文本
 */
function getTranslation(key: string): string {
    try {
        // 只在客户端环境下执行
        if (typeof window !== 'undefined') {
            // 尝试使用 nuxt i18n
            const nuxtApp = useNuxtApp && useNuxtApp()
            if (nuxtApp && nuxtApp.$i18n) {
                return nuxtApp.$i18n.t(key)
            }
        }
    } catch (e) {
        console.warn('无法使用 i18n，使用默认翻译', e)
    }

    // 回退到默认翻译
    return defaultTranslations[key as keyof typeof defaultTranslations] || key
}

/**
 * 消息提示类
 */
class Message {
    /**
     * 普通消息
     * @param message 消息标题
     * @param options 配置选项
     */
    show(message: string, options?: MessageOptions) {
        toast(message, {
            ...defaultOptions,
            ...options
        })
    }

    /**
     * 成功消息
     * @param message 消息标题
     * @param options 配置选项
     */
    success(message: string, options?: MessageOptions) {
        toast.success(message, {
            ...defaultOptions,
            ...options
        })
    }

    /**
     * 警告消息
     * @param message 消息标题
     * @param options 配置选项
     */
    warning(message: string, options?: MessageOptions) {
        toast.warning(message, {
            ...defaultOptions,
            ...options
        })
    }

    /**
     * 信息消息
     * @param message 消息标题
     * @param options 配置选项
     */
    info(message: string, options?: MessageOptions) {
        toast.info(message, {
            ...defaultOptions,
            ...options
        })
    }

    /**
     * 错误消息
     * @param message 消息标题
     * @param options 配置选项
     */
    error(message: string, options?: MessageOptions) {
        const mergedOptions = {
            ...defaultOptions,
            ...options
        }

        // 创建自定义组件来显示requestId和支持按钮
        if (mergedOptions.showSupport || mergedOptions.requestId) {
            // 获取翻译文本
            const contactSupportText = getTranslation('message.contact_support')

            // 保存原始描述
            const originalDescription = mergedOptions.description

            // 创建一个自定义的底部组件
            mergedOptions.description = originalDescription

            // 使用h函数创建底部内容
            const footerContent = []

            // 添加分割线样式
            const dividerStyle = {
                margin: '8px 0',
                borderTop: '1px solid #f0f0f0',
                opacity: 0.6
            }

            // 添加底部文字样式
            const footerTextStyle = {
                color: 'rgba(0, 0, 0, 0.45)',
                fontSize: '12px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginTop: '8px'
            }

            // 添加requestId样式
            const requestIdStyle = {
                cursor: 'pointer',
                marginRight: '8px'
            }

            // 添加支持按钮样式
            const supportBtnStyle = {
                color: 'rgba(0, 0, 0, 0.45)',
                cursor: 'pointer',
                fontSize: '12px'
            }

            // 创建底部区域
            if (originalDescription) {
                footerContent.push(
                    h('div', {style: dividerStyle})
                )
            }

            const footerRow = h('div', {style: footerTextStyle}, [
                // RequestId部分（如果存在）
                mergedOptions.requestId ? h('span', {
                    style: requestIdStyle,
                    onClick: () => {
                        // 点击复制RequestId
                        copyToClipboard(mergedOptions.requestId || '', true)
                    }
                }, [
                    'Request ID: ',
                    h('span', {
                        style: {
                            textDecoration: 'underline',
                            fontFamily: 'monospace'
                        }
                    }, mergedOptions.requestId)
                ]) : null,

                // 支持按钮（如果需要显示）
                mergedOptions.showSupport ? h('span', {
                    style: supportBtnStyle,
                    onClick: () => {
                        // 打开支持表单，并传递请求ID
                        supportFormType.value = 'error'
                        supportFormRequestId.value = mergedOptions.requestId || ''
                        isSupportFormOpen.value = true
                        logger.info('设置支持表单 requestId:', supportFormRequestId.value)
                    }
                }, contactSupportText) : null
            ])

            footerContent.push(footerRow)

            // 将原始描述和底部内容组合
            const combinedDescription = [
                originalDescription ? h('div', {}, originalDescription) : null,
                ...footerContent
            ]

            // 修改 description，设置为自定义渲染
            mergedOptions.description = h('div', {}, combinedDescription)
        }

        toast.error(message, mergedOptions)
    }

    /**
     * 处理异步操作的消息
     * @param promise 需要处理的Promise
     * @param options 各状态下的提示文本
     */
    promise<T>(promise: Promise<T>, options: PromiseOptions) {
        return toast.promise(promise, {
            ...defaultOptions,
            ...options,
            error: (err: Error) => {
                // 处理错误状态的回调
                const errorResult = typeof options.error === 'function'
                    ? options.error(err)
                    : {title: options.error, description: err.message}

                // 添加支持按钮和RequestId（如果需要）
                if (errorResult.showSupport || errorResult.requestId) {
                    // 使用类似error方法的处理，但这里需要返回一个对象
                    // 在这里返回修改后的对象，保持原始的操作
                    return {
                        ...errorResult,
                        description: this._createErrorDescription(errorResult.description, errorResult.requestId, errorResult.showSupport)
                    }
                }

                return errorResult
            }
        })
    }

    /**
     * 创建错误描述内容（内部方法）
     * @private
     */
    _createErrorDescription(
        description: string | undefined,
        requestId: string | undefined,
        showSupport: boolean | undefined
    ) {
        // 如果没有requestId也不需要显示支持按钮，直接返回原始描述
        if (!requestId && !showSupport) {
            return description
        }

        // 获取翻译文本
        const contactSupportText = getTranslation('message.contact_support')

        // 创建自定义的底部组件
        return h('div', {}, [
            // 原始描述
            description ? h('div', {}, description) : null,

            // 分割线
            h('div', {
                style: {
                    margin: '8px 0',
                    borderTop: '1px solid #f0f0f0',
                    opacity: 0.6
                }
            }),

            // 底部信息栏
            h('div', {
                style: {
                    color: 'rgba(0, 0, 0, 0.45)',
                    fontSize: '12px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: '8px'
                }
            }, [
                // RequestId部分（如果存在）
                requestId ? h('span', {
                    style: {
                        cursor: 'pointer',
                        marginRight: '8px'
                    },
                    onClick: () => {
                        // 点击复制RequestId
                        copyToClipboard(requestId || '', true)
                    }
                }, [
                    'Request ID: ',
                    h('span', {
                        style: {
                            textDecoration: 'underline',
                            fontFamily: 'monospace'
                        }
                    }, requestId)
                ]) : null,

                // 支持按钮（如果需要显示）
                showSupport ? h('span', {
                    style: {
                        color: 'rgba(0, 0, 0, 0.45)',
                        cursor: 'pointer',
                        fontSize: '12px'
                    },
                    onClick: () => {
                        // 打开支持表单，并传递请求ID
                        supportFormType.value = 'error'
                        supportFormRequestId.value = requestId || ''
                        isSupportFormOpen.value = true
                        console.log('设置支持表单 requestId:', supportFormRequestId.value)
                    }
                }, contactSupportText) : null
            ])
        ])
    }

    /**
     * 显示支持表单
     * @param type 表单类型 ('feedback' 或 'error')
     * @param requestId 可选的请求ID
     */
    showSupportForm(type: 'feedback' | 'error' = 'feedback', requestId: string = '') {
        supportFormType.value = type
        supportFormRequestId.value = requestId
        isSupportFormOpen.value = true
    }
}

/**
 * 消息提示实例
 */
export const message = new Message()

/**
 * 原始 toast 函数
 */
export {toast}

/**
 * 支持表单状态
 */
export const supportForm = {
    isOpen: isSupportFormOpen,
    type: supportFormType,
    requestId: supportFormRequestId
}

/**
 * 使用消息提示组合函数
 */
export function useMessage() {
    return {
        message,
        toast,
        supportForm
    }
}
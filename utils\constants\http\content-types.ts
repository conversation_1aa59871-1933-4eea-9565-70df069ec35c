/**
 * 内容类型常量
 *
 * 常用的HTTP Content-Type值
 */
export const CONTENT_TYPES = {
    /** JSON格式 */
    JSON: 'application/json',
    /** 表单数据 */
    FORM: 'application/x-www-form-urlencoded',
    /** 多部分表单数据 */
    MULTIPART: 'multipart/form-data'
} as const

// 导出类型
export type ContentType = keyof typeof CONTENT_TYPES

/**
 * 获取默认请求头的Content-Type
 * @returns 默认的Content-Type值
 */
export function getDefaultContentType(): string {
    return CONTENT_TYPES.JSON
}
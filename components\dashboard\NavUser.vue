<script setup lang="ts">
import {useSidebar} from '@/components/ui/sidebar'
import {ChevronsUpDown, LogOut} from 'lucide-vue-next'
import {localStg} from '~/utils/localStorageService'
import {LocalStorageConstant} from '~/utils/constants/localStorage'
import type {User} from '~/types/models/user'
import {getLocalizedConfigText, resolveLocalePath, tMsg} from '~/utils/i18n'
import logger from '~/utils/logger'
import LanguageAndTheme from '~/components/common/LanguageAndTheme.vue'
import {executeLogout} from '~/utils/auth'
import type {NavLinksConfig} from '~/types/site/navLinks'

const props = defineProps<{
    footerLinks?: NavLinksConfig
}>()

const currentUser = ref<User | null>(null)
const {isMobile} = useSidebar()

onMounted(() => {
    const userInfo = localStg.get<User>(LocalStorageConstant.USER_INFO)
    if (!userInfo || !userInfo.userId) {
        // 因为有一个全局认证中间件 这里不进行重定向
    } else {
        currentUser.value = userInfo
    }
})

async function handleLogout() {
    logger.info('Attempting to sign out from NavUser.')
    await executeLogout(() => {
        currentUser.value = null
        logger.info('User data cleared locally after logout in NavUser.')
    })
}
</script>

<template>
  <SidebarMenu v-if="currentUser">
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton size="lg"
                             class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
            <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage v-if="currentUser.avatar"
                           :src="currentUser.avatar"
                           :alt="currentUser.nickname"/>
              <AvatarFallback class="rounded-lg">
                {{ (currentUser.username || 'Null').slice(0, 2).toUpperCase() || '' }}
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-medium">{{ currentUser.username }}</span>
              <span class="truncate text-xs">{{ currentUser.email }}</span>
            </div>
            <ChevronsUpDown class="ml-auto size-4"/>
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                             :side="isMobile ? 'bottom' : 'right'"
                             align="end"
                             :side-offset="4">
          <DropdownMenuLabel class="p-0 font-normal">
            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
              <Avatar class="h-8 w-8 rounded-lg">
                <AvatarImage v-if="currentUser.avatar"
                             :src="currentUser.avatar"
                             :alt="currentUser.nickname || 'avatar'"/>
                <AvatarFallback class="rounded-lg">
                  {{ currentUser.username?.slice(0, 2).toUpperCase() || '' }}
                </AvatarFallback>
              </Avatar>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">{{ currentUser.username }}</span>
                <span class="truncate text-xs">{{ currentUser.email }}</span>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator/>
          <template v-if="props.footerLinks && props.footerLinks.length > 0">
            <DropdownMenuGroup>
              <template v-for="(item, itemIndex) in props.footerLinks" :key="`footer-item-${itemIndex}`">
                <DropdownMenuItem v-if="item.href" as-child>
                  <NuxtLink :to="resolveLocalePath(item.href)">
                    <component :is="item.icon" v-if="item.icon" class="h-4 w-4"/>
                    <span>{{ getLocalizedConfigText(item.title) }}</span>
                  </NuxtLink>
                </DropdownMenuItem>
                <DropdownMenuItem v-else-if="!item.children" :disabled="true">
                  <component :is="item.icon" v-if="item.icon" class="h-4 w-4"/>
                  <span>{{ getLocalizedConfigText(item.title) }}</span>
                </DropdownMenuItem>
              </template>
            </DropdownMenuGroup>
            <DropdownMenuSeparator/>
          </template>
          <DropdownMenuItem @select.prevent>
            <div class="w-full">
              <language-and-theme/>
            </div>
          </DropdownMenuItem>
          <DropdownMenuItem @click="handleLogout">
            <LogOut class="h-4 w-4"/>
            {{ tMsg('auth.labels.signOut') }}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>

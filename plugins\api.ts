import {batch, Delete, Get, Post, Put} from '~/utils/http'
import {createCrudApi} from '~/utils/api/crud'

/**
 * API服务插件
 * 提供全局访问API方法的能力
 */
export default defineNuxtPlugin(() => {
    // 创建API客户端
    const api = {
        /**
         * GET请求
         * @param url 请求URL
         * @param params 查询参数
         * @param options 请求选项
         * @returns 响应数据
         */
        get: Get,

        /**
         * POST请求
         * @param url 请求URL
         * @param data 请求数据
         * @param options 请求选项
         * @returns 响应数据
         */
        post: Post,

        /**
         * PUT请求
         * @param url 请求URL
         * @param data 请求数据
         * @param options 请求选项
         * @returns 响应数据
         */
        put: Put,

        /**
         * DELETE请求
         * @param url 请求URL
         * @param options 请求选项
         * @returns 响应数据
         */
        delete: Delete,

        /**
         * 批量请求
         * @param requests 请求列表
         * @returns 所有请求的响应
         */
        batch,

        /**
         * 创建CRUD API客户端
         * @param baseUrl 基础URL
         * @returns CRUD API客户端
         */
        createCrud: <T = Record<string, any>>(baseUrl: string) => {
            return createCrudApi<T>(baseUrl)
        },

        /**
         * 创建实体API客户端
         * @param entityName 实体名称
         * @returns 实体CRUD API客户端
         */
        createEntityApi: <T = Record<string, any>>(entityName: string) => {
            const baseUrl = `/api/v1/${entityName}`
            return createCrudApi<T>(baseUrl)
        }
    }

    return {
        provide: {
            api
        }
    }
})
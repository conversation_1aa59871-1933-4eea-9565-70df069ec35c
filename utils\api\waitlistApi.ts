import {createCrudApi} from './crud'
import type {WaitlistResponse} from '~/types/api/response/WaitlistResponse'

/**
 * <AUTHOR> <br/>
 * @description 等待名单 <br/>
 * @date 2025-05-15 21:34:08
 */
function createWaitlistApi() {
    const baseUrl = '/waitlist'

    const waitlistCrudApi = createCrudApi<WaitlistResponse>(baseUrl)
    return {
        ...waitlistCrudApi
    }
}

// 导出用户API
export const waitlistApi = createWaitlistApi()
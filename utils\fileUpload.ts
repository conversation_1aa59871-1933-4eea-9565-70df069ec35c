/**
 * 文件分片上传工具
 */

import SparkMD5 from 'spark-md5'
import { logger } from '@/utils/logger'
import type { ApiResponse } from '~/utils/http'
import type { ChunkCheckResult } from '~/types/file'
import type { CheckAndMergeParams } from '~/types/api/params/CheckAndMergeParams'
import type { ChunkUploadParams } from '~/types/api/params/ChunkUploadParams'
import type { FileInfo } from '~/types/api/response/FileInfo'

/**
 * 文件工具类
 */
export class FileUploadUtils {
  private maxRetryCount = 3

  /**
     * 获取文件MD5哈希值
     * @param file 文件对象
     * @returns Promise<string> MD5哈希值
     */
  getFileMd5(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader()
      fileReader.onload = (event) => {
        try {
          const fileMd5 = SparkMD5.ArrayBuffer.hash(event.target?.result as ArrayBuffer)
          resolve(fileMd5)
        } catch (error) {
          reject(error)
        }
      }
      fileReader.onerror = (error) => {
        reject(error)
      }
      fileReader.readAsArrayBuffer(file)
    })
  }

  /**
     * 将文件分割成分片数组
     * @param file 文件对象
     * @param chunkSize 分片大小
     * @returns 分片数组
     */
  getFileChunkArr(file: File, chunkSize: number): Blob[] {
    const chunks: Blob[] = []
    let start = 0
    let end: number

    while (start < file.size) {
      end = Math.min(start + chunkSize, file.size)
      chunks.push(file.slice(start, end))
      start = end
    }

    logger.info('文件分片信息', {
      fileTotalSize: file.size,
      chunkSize,
      chunkLength: chunks.length
    })

    return chunks
  }

  /**
     * 分片上传主函数
     * @param fileData 文件数据
     * @param checkAndMergeParam 检查和合并参数
     * @param chunkSize 分片大小
     * @param checkAndMergeFunction 检查和合并函数
     * @param chunkUploadFunction 分片上传函数
     * @param updateUploadProgressFunction 更新上传进度函数
     * @param maxConcurrency 最大并发数
     * @param handleUploadSuccessFunction 上传成功处理函数
     * @param handleUploadFailureFunction 上传失败处理函数
     * @param retryCount 重试次数
     * @param result 结果对象
     */
  async chunkUpload(
    fileData: File & { uid?: string },
    checkAndMergeParam: CheckAndMergeParams,
    chunkSize: number,
    checkAndMergeFunction: (param: CheckAndMergeParams) => Promise<ApiResponse<ChunkCheckResult>>,
    chunkUploadFunction: (param: ChunkUploadParams) => Promise<void>,
    updateUploadProgressFunction: (uid: string, progress: number) => void,
    maxConcurrency: number,
    handleUploadSuccessFunction: (fileInfo: FileInfo, fileData: File) => void,
    handleUploadFailureFunction: (result: FileInfo, fileData: File) => void,
    retryCount: number = 0,
    result?: any
  ): Promise<void> {
    if (retryCount >= this.maxRetryCount) {
      logger.error('分片上传文件失败，超过最大重试次数', { fileName: fileData.name, retryCount })
      handleUploadFailureFunction(result, fileData)
      return
    }

    logger.info('开始分片上传', { fileName: fileData.name, fileSize: fileData.size })

    try {
      const fileChunkArr = this.getFileChunkArr(fileData, chunkSize)
      const fileName = fileData.name
      const md5 = await this.getFileMd5(fileData)

      checkAndMergeParam.md5 = md5
      const checkResult = await checkAndMergeFunction(checkAndMergeParam)

      if (checkResult.code === 200) {
        const uploadResult = checkResult.data
        if (uploadResult.finishStatus === true) {
          logger.info('文件已存在，上传成功', { fileName })
          if (fileData.uid) {
            updateUploadProgressFunction(fileData.uid, 100)
          }
          handleUploadSuccessFunction(uploadResult.fileInfo, fileData)
        } else {
          const { chunkUploadStatus } = uploadResult
          await this.executeChunkUpload(
            fileChunkArr,
            chunkUploadStatus,
            md5,
            fileName,
            fileData,
            chunkSize,
            chunkUploadFunction,
            checkAndMergeFunction,
            checkAndMergeParam,
            updateUploadProgressFunction,
            maxConcurrency,
            handleUploadSuccessFunction,
            handleUploadFailureFunction,
            retryCount
          )
        }
      }
    } catch (error) {
      logger.error('分片上传过程中发生错误', error)
      handleUploadFailureFunction(error, fileData)
    }
  }

  /**
     * 执行分片上传
     */
  private async executeChunkUpload(
    fileChunkArr: Blob[],
    chunkUploadStatus: string | undefined,
    md5: string,
    fileName: string,
    fileData: File & { uid?: string },
    chunkSize: number,
    chunkUploadFunction: (param: ChunkUploadParam) => Promise<any>,
    checkAndMergeFunction: (param: CheckAndMergeParam) => Promise<UploadResult>,
    checkAndMergeParam: CheckAndMergeParam,
    updateUploadProgressFunction: (uid: string, progress: number) => void,
    maxConcurrency: number,
    handleUploadSuccessFunction: (fileInfo: any, fileData: File) => void,
    handleUploadFailureFunction: (result: any, fileData: File) => void,
    retryCount: number
  ): Promise<void> {
    let finishedCount = 0
    const taskArr: Array<(() => Promise<any>) | null> = []

    if (!chunkUploadStatus) {
      // 所有分片都没有上传
      for (let i = 0; i < fileChunkArr.length; i++) {
        taskArr[i] = this.uploadSingleChunk(
          fileChunkArr[i],
          md5,
          fileChunkArr.length,
          i,
          fileName,
          chunkUploadFunction,
          chunkSize
        )
      }
    } else {
      // 部分分片已上传，进行断点续传
      logger.info('该文件部分已上传，正在进行断点续传')
      for (let i = 0; i < chunkUploadStatus.length; i++) {
        const status = chunkUploadStatus.charAt(i)
        if (status === '0') {
          // 需要上传
          taskArr[i] = this.uploadSingleChunk(
            fileChunkArr[i],
            md5,
            fileChunkArr.length,
            i,
            fileName,
            chunkUploadFunction,
            chunkSize
          )
        } else {
          // 已上传
          taskArr[i] = null
          finishedCount++
        }
      }
    }

    const startTime = Date.now()
    await this.limitConcurrency(
      taskArr,
      maxConcurrency,
      updateUploadProgressFunction,
      fileChunkArr,
      fileData.uid || '',
      finishedCount
    )

    // 所有分片上传完毕后，检查合并
    const mergeResult = await checkAndMergeFunction(checkAndMergeParam)
    if (mergeResult.code === 200) {
      const endTime = Date.now()
      const consumeTime = ((endTime - startTime) / 1000).toFixed(2)
      logger.info('分片上传完成', { fileName, consumeTime: `${consumeTime}s` })

      const uploadResult = mergeResult.data
      if (uploadResult.finishStatus === true) {
        if (fileData.uid) {
          updateUploadProgressFunction(fileData.uid, 100)
        }
        handleUploadSuccessFunction(uploadResult.fileInfo, fileData)
      } else {
        // 部分分片上传失败，重试
        retryCount++
        logger.warn('部分分片上传失败，正在重试', { fileName, retryCount })
        if (retryCount < this.maxRetryCount) {
          await this.chunkUpload(
            fileData,
            checkAndMergeParam,
            chunkSize,
            checkAndMergeFunction,
            chunkUploadFunction,
            updateUploadProgressFunction,
            maxConcurrency,
            handleUploadSuccessFunction,
            handleUploadFailureFunction,
            retryCount,
            mergeResult
          )
        }
      }
    }
  }

  /**
     * 上传单个分片
     */
  private uploadSingleChunk(
    data: Blob,
    md5: string,
    totalChunkCount: number,
    currentChunkNumber: number,
    fileName: string,
    chunkUploadFunction: (param: ChunkUploadParam) => Promise<any>,
    chunkSize: number
  ): () => Promise<any> {
    return () => new Promise((resolve, reject) => {
      const param: ChunkUploadParam = {
        file: new File([data], fileName),
        chunkSize,
        md5,
        totalChunkCount,
        currentChunkNumber
      }
      chunkUploadFunction(param).then(resolve).catch(reject)
    })
  }

  /**
     * 计算上传进度百分比
     */
  private computeUploadPercentage(
    updateUploadProgressFunction: (uid: string, progress: number) => void,
    totalChunkCount: number,
    currentCount: number,
    uid: string
  ): void {
    if (currentCount === 0) {
      updateUploadProgressFunction(uid, 0)
      return
    }
    const s = 100 / totalChunkCount
    const uploadPercentage = s * currentCount
    updateUploadProgressFunction(uid, uploadPercentage)
  }

  /**
     * 限制并发执行任务
     */
  private async limitConcurrency(
    tasks: Array<(() => Promise<any>) | null>,
    maxConcurrency: number,
    updateUploadProgressFunction: (uid: string, progress: number) => void,
    fileChunkArr: Blob[],
    uid: string,
    finishedCount: number
  ): Promise<any[]> {
    const results: any[] = []
    const running: Promise<any>[] = []

    const totalChunkCount = fileChunkArr.length
    logger.info('分片上传进度', { total: totalChunkCount, finished: finishedCount })
    this.computeUploadPercentage(updateUploadProgressFunction, totalChunkCount, finishedCount, uid)

    const runTask = async (task: () => Promise<any>) => {
      const result = await task()
      finishedCount++
      this.computeUploadPercentage(updateUploadProgressFunction, fileChunkArr.length, finishedCount, uid)
      results.push(result)
    }

    let count = 0
    for (const task of tasks) {
      if (!task) {
        continue
      }
      if (count >= maxConcurrency) {
        await Promise.all(running)
        running.length = 0
        count = 0
      }
      const p = runTask(task)
      running.push(p)
      count++
    }

    await Promise.all(running)
    return results
  }
}

// 导出单例实例
export const fileUploadUtils = new FileUploadUtils()

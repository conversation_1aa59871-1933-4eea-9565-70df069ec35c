import type { ToolCallResult } from '~/types/aigc'
import type { QueryAccountStatusParams } from '~/types/api/params/QueryAccountStatusParams'
import type { QueryContactListParams } from '~/types/api/params/QueryContactListParams'
import type { QueryHistoryMsgParams } from '~/types/api/params/QueryHistoryMsgParams'
import type { QueryStatDataParams } from '~/types/api/params/QueryStatDataParams'
import type { SendMessageParams } from '~/types/api/params/SendMessageParams'
import { BaseWsApiService } from '~/utils/http/BaseWsApiService.class'

export class CustomerAgentClientWsService extends BaseWsApiService {
  constructor() {
    super({ serverUri: '/customer/agent', toolCallEventType: 'CUSTOMER_TOOL_CALL' })
  }

  /**
     * 查询历史消息
     * @param params
     * @param subAccountId
     */
  public queryHistoryMsgList(params: QueryHistoryMsgParams, subAccountId: string) {
    this.send<QueryHistoryMsgParams>(params, 'CUSTOMER_QUERY_HISTORY_MSG', this.createForwardMetadata(`${subAccountId}_im`))
  }

  /**
     * 查询联系人列表
     * @param params
     */
  public queryContactList(params: QueryContactListParams) {
    this.send<QueryContactListParams>(params, 'CUSTOMER_QUERY_CONTACT_LIST', undefined, params.group)
  }

  /**
     * 查询子账号接待统计数据
     * @param params
     */
  public queryStatData(params: QueryStatDataParams) {
    this.send<QueryStatDataParams>(params, 'CUSTOMER_QUERY_STAT_DATA')
  }

  public sendMessage(params: SendMessageParams, subAccountId: string, localMessageId: string) {
    const formatMetadata = this.createForwardMetadata(`${subAccountId}_im`)
    const metadata: Record<string, unknown> = { ...formatMetadata }
    metadata['localMessageId'] = localMessageId
    this.send<SendMessageParams>(params, 'CUSTOMER_SEND_NEW_MSG', metadata)
  }

  public recallMessage(cursorData: any, platformMessageId: string, subAccountId: string, conversationId: string): Promise<ToolCallResult<unknown>> {
    return this.executeToolCall({
      name: 'MESSAGE_RECALL_MESSAGE',
      arguments: { cursorData, platformMessageId, conversationId }
    }, this.createForwardMetadata(`${subAccountId}_im`))
  }

  /**
   * 查询账号状态
   * @param params 查询参数，包含 platformId
   */
  public queryAccountStatus(params: QueryAccountStatusParams) {
    this.send<QueryAccountStatusParams>(params, 'QUERY_SUBACCOUNT_STATUS')
  }
}

import type {I18nTextMap} from '~/types/i18n'
import type {Component} from 'vue';

/**
 * 单个导航链接的配置
 */
export interface NavLink {
    /**
     * 链接的目标地址
     */
    href: string
    /**
     * 链接图标的名称 (可选, 例如来自 Icones)
     */
    icon?: string | Component
    /**
     * 链接的显示文本 (国际化)
     * 用于替代原先的 i18n_code，直接存储国际化文本
     */
    title: I18nTextMap
    /**
     * 是否在移动端显示 (可选，默认为true)
     */
    showInMobile?: boolean
    /**
     * 是否在PC端显示 (可选，默认为true)
     */
    showInDesktop?: boolean
    /**
     * 子导航链接 (可选)
     */
    children?: NavLink[]
    /**
     * 是否默认展开 (可选, 用于可折叠的导航项)
     */
    defaultOpen?: boolean
}

/**
 * 导航链接配置数组
 */
export type NavLinksConfig = NavLink[]
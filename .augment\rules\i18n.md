---
type: manual
---

## I18n

- 两类: 处理标准文本(`/i18n/locales`目录)和处理项目配置(`/config`目录)。只执行i18n替换，不允许擅自更改代码逻辑。

### 处理标准文本

适用于可直接使用的、语言key比较固定的(如Error、message.error、登录提示等等)

1. 在`/i18n/locales`目录下的对应语言文件中定义，按照功能或模块添加结构化的Key。推荐`{ "footer": { "copyright": "..." } }`
   ，不推荐 `{ "footer_copyright": "..." }`

2. 使用@index.ts 中tMsg的工具函数获取翻译

### 处理配置驱动文本

适用于内容由 `/config` 目录下的ts文件驱动的部分(例如导航链接、FAQ 列表等)，允许非开发人员通过修改配置来更新内容及其翻译。

1. 定义数据结构: 在配置文件中，将需要翻译的文本组织成特定的映射结构(
   参考[i18nExampleConfig.ts](mdc:config/i18nExampleConfig.ts) 和 [i18nExample.ts](mdc:types/site/i18nExample.ts) 定义成
   {xx: { "en": "English Text", "zh": "中文文本" }}格式)。
2. 为该配置文件定义配置类型: 在`/types/site/` 目录下为配置文件创建详细的TypeScript接口(
   参考[i18nExample.ts](mdc:types/site/i18nExample.ts))，并在接口中引用 [i18n.ts](mdc:types/i18n.ts) (I18nTextMap已经定义好了,
   不要再在ts中重新定义)。
3. 调用/utils/i18n/index.ts#getLocalizedConfigText函数，第一个参数为配置对象中需要翻译的部分，第二个参数为fallbackText(
   英文，写死，默认文本)。即可获取当前语言环境下的文本(参考[I18nExample.vue](mdc:components/examples/I18nExample.vue))。
4. 优势: 将内容和翻译集中在配置文件中，便于管理和非代码修改；通过类型和辅助函数保证了代码的健壮性。
5. 注释使用
    ```
    /**
      * 这是注释
      */
    ```
<script setup lang="ts">
import {getCurrentLanguage, tMsg} from '~/utils/i18n'
import {calculateReadingTime} from '~/utils/article'
import type {ArticleResponse} from '~/types/api'
import {articleApi} from '~/utils/api/articleApi'
import type {ArticleParams} from '~/types/api/params/ArticleParams'
import {Skeleton} from '@/components/ui/skeleton'

const props = defineProps<{
  slug: string | undefined;
}>()

const emit = defineEmits<{
  (e: 'postLoaded', post: ArticleResponse | null): void
  (e: 'loadingStateChanged', isLoading: boolean): void
}>()

const loading = ref(true)
const blogPost = ref<ArticleResponse | null>(null)

const fetchBlogPost = async () => {
    if (!props.slug) {
        loading.value = false
        blogPost.value = null
        emit('loadingStateChanged', false)
        emit('postLoaded', null)
        return
    }

    loading.value = true
    emit('loadingStateChanged', true)
    blogPost.value = null

    const params: ArticleParams = {
        slug: props.slug,
        pageSize: 1,
        pageNum: 1
    }
    const {code, data} = await articleApi.queryArticleList(params)
    if (code === 200 && data && data.result.length > 0) {
        blogPost.value = data.result[0]
        emit('postLoaded', blogPost.value)
    } else {
        blogPost.value = null
        emit('postLoaded', null)
    }
    emit('loadingStateChanged', false)
    loading.value = false
}

fetchBlogPost()

watch(() => props.slug, (newSlug, oldSlug) => {
    if (newSlug !== oldSlug) {
        fetchBlogPost()
    }
})

const showComments = ref(false)
const toggleComments = () => {
    showComments.value = !showComments.value
}
const downloadContent = () => {
    if (!blogPost.value?.content) { return }
    const title = blogPost.value?.title || 'untitled'
    const content = blogPost.value?.content || ''
    const markdownContent = `# ${title}\n\n${content}`
    const blob = new Blob([markdownContent], {type: 'text/markdown;charset=utf-8'})
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(blob)
    downloadLink.download = `${title.replace(/\s+/g, '-').toLowerCase()}.md`
    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)
    URL.revokeObjectURL(downloadLink.href)
}

const formatDate = (dateString?: string) => {
    if (!dateString) { return '' }
    const date = new Date(dateString)
    return date.toLocaleDateString(getCurrentLanguage(), {
        year: 'numeric', month: 'long', day: 'numeric'
    })
}
const readingTime = computed(() => {
    return calculateReadingTime(blogPost.value?.content || '')
})

</script>

<template>
  <div class="max-w-3xl mx-auto">
    <slot name="header-prepend"
          :post="blogPost"
          :loading="loading"
          :not-found="!loading && !blogPost" />

    <div v-if="loading" class="mb-8 animate-pulse">
      <Skeleton class="h-10 w-3/4 mb-6" />
      <div class="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground mb-6">
        <Skeleton class="h-4 w-28" />
        <Skeleton class="h-4 w-20" />
      </div>
      <div class="border-t border-border my-6" />
      <div class="space-y-3">
        <Skeleton class="h-4 w-full" />
        <Skeleton class="h-4 w-full" />
        <Skeleton class="h-4 w-5/6" />
        <Skeleton class="h-4 w-full" />
        <Skeleton class="h-4 w-3/4" />
      </div>
    </div>
    <div v-else>
      <div v-if="blogPost">
        <div class="mb-8">
          <div>
            <slot name="article-header" :post="blogPost">
              <h1 class="text-3xl md:text-4xl font-bold mb-6">{{ blogPost.title }}</h1>
            </slot>
            <div class="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground">
              <div v-if="blogPost.createTime" class="flex items-center">
                <Icon name="heroicons:calendar" class="mr-1 w-4 h-4" />
                {{ tMsg('blog.published_on') }} {{ formatDate(blogPost.createTime?.toString()) }}
              </div>
              <div v-if="readingTime" class="flex items-center">
                <Icon name="heroicons:clock" class="mr-1 w-4 h-4" />
                {{ readingTime }} {{ tMsg('blog.reading_time') }}
              </div>
            </div>
            <slot name="meta-after" :post="blogPost" />
          </div>
          <div class="border-t border-border my-6" />
        </div>

        <div>
          <slot name="content-before" :post="blogPost" />
          <ArticleContent :content="blogPost.content || ''" />
          <slot name="content-after" :post="blogPost" />
        </div>
        <!-- Actions Area -->
        <div>
          <div class="mt-12 pt-6 border-t border-border">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-6">
                <slot name="actions-prepend" :post="blogPost" />
                <button
                  class="flex items-center cursor-pointer gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                  @click="toggleComments">
                  <Icon :name="showComments ? 'heroicons:chat-bubble-left-solid' : 'heroicons:chat-bubble-left'"
                        class="w-5 h-5"
                        :class="{ 'text-primary': showComments }" />
                  <span>{{ blogPost.commentCount ? blogPost.commentCount : 0 }} {{ tMsg('blog.comment_count') }}</span>
                </button>
              </div>
              <div v-if="blogPost.content" class="flex items-center">
                <button class="text-muted-foreground cursor-pointer hover:text-foreground transition-colors"
                        :title="tMsg('blog.download')"
                        @click="downloadContent">
                  <Icon name="heroicons:arrow-down-tray" class="w-5 h-5" />
                </button>
                <slot name="actions-append" :post="blogPost" />
              </div>
            </div>
            <div v-if="showComments" class="border-t border-border my-8" />
          </div>
          <ArticleComments v-if="showComments" :article-id="blogPost.id?.toString() || ''" article-type="blog" />
        </div>
      </div>
      <div v-else>
        <div class="my-8 p-6 text-center">
          <slot name="not-found-actions" :slug="slug">
            <h2 class="text-xl font-medium mb-2">{{ tMsg('blog.article_not_found') }}</h2>
          </slot>
        </div>
      </div>

      <slot name="footer-append" />
    </div>
  </div>
</template>
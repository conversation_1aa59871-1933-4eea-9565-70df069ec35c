// install-components.js
import {execSync} from 'child_process'

const components = [
    'dialog',
    'drawer',
    'dropdown-menu',
    'form',
    'hover-card',
    'input',
    'label',
    'menubar',
    'navigation-menu',
    'number-field',
    'pagination',
    'pin-input',
    'popover',
    'progress',
    'radio-group',
    'range-calendar',
    'resizable',
    'scroll-area',
    'select',
    'separator',
    'skeleton',
    'slider',
    'sonner',
    'stepper',
    'switch',
    'table',
    'tabs',
    'tags-input',
    'textarea',
    'toggle',
    'toggle-group',
    'tooltip'

]

console.log('🚀 开始批量安装 shadcn-vue 组件...')

for (const component of components) {
    console.log(`\n📦 安装组件: ${component}`)
    try {
        execSync(`npx shadcn-vue@latest add  ${component}`, {stdio: 'inherit'})
        console.log(`✅ ${component} 安装成功`)
    } catch (error) {
        console.error(`❌ ${component} 安装失败`)
    }
}

console.log('\n🎉 所有组件安装完成!')
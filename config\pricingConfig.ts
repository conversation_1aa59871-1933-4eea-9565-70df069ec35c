import type {PricingPageConfig} from '@/types/site/pricingConfig'
import {PaymentProviderEnum} from '~/utils/constants/enums/PaymentProviderEnum'

const pricingConfig: PricingPageConfig = {
    sectionHeader: {
        en: 'Pricing',
        zh: '价格方案<span class="text-amber-400">asdf</span>'
    },
    mainTitle: {
        en: 'Pricing that grows with you',
        zh: '与您共同成长的定价方案'
    },
    descriptionText: {
        en: 'Choose an affordable plan that\'s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.',
        zh: '选择一个经济实惠的计划，其中包含吸引受众、建立客户忠诚度和推动销售的最佳功能。'
    },
    queryPayProvider: PaymentProviderEnum.STRIPE,
    productIdList: [21, 22],
    periods: [
        {
            key: 'MONTH',
            showText: {en: 'Monthly', zh: '按月付费'},
            periodSuffixText: {en: 'month', zh: '月'},
            plans: []
        },
        {
            key: 'YEAR',
            showText: {en: 'Annually', zh: '按年付费'},
            periodSuffixText: {en: 'year', zh: '年'},
            plans: []
        },
        {
            key: 'WEEK',
            showText: {en: 'Weeks', zh: '按周付费'},
            periodSuffixText: {en: 'year', zh: '年'},
            plans: []
        }
        // {
        //     key: 'ONE_TIME',
        //     showText: {en: 'Monthly', zh: '按月付费'},
        //     periodSuffixText: {en: 'month', zh: '月'},
        //     plans: []
        // }
    ]
}

export default pricingConfig
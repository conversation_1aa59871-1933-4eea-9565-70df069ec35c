---
description:
globs:
alwaysApply: true
---

核心定位：配置驱动的快速产品开发基础项目

本项目是一个配置驱动的基础，旨在加速不同独立产品开发，采用成熟的前后端分离架构。通过高度依赖配置文件 (`/config` 目录)，开发者可以仅修改配置，快速将此基础适配成一个全新的产品，实现敏捷的产品发布。

前端基于 Nuxt.js, Vue 3, 和 Tailwind CSS 构建，专注于用户界面的高效渲染和与后端 API 的清晰交互。

## 架构特点

-   配置驱动开发: 核心原则 - 对于品牌信息、功能开关、特定文本等需要在不同产品间变化的元素，必须优先通过 `/config` 目录下的配置文件进行管理和读取，避免在组件或逻辑中硬编码。这是实现快速产品适配的关键。
-   前后端分离: 前端 (Nuxt) 负责 UI 渲染和 API 调用，后端独立提供 API 服务。
-   混合渲染策略: 根据不同页面需求采用不同的渲染模式 (SSR/SPA)，兼顾 SEO 和用户体验。
-   组件化开发: 遵循 Vue 3 组件化开发理念，并推荐结合 shadcn-vue 组件库构建界面。
-   集中配置管理: `/config` 目录是所有非 API 获取、且需要在不同产品项目中修改的配置的唯一指定位置。

## 页面渲染策略

### SSR页面

-   渲染模式: `/pages` 目录下除 `/dashboard` 及其子目录外的所有页面，采用服务端渲染。
-   首页 (`index.vue`): 作为应用的入口和 Landing Page，通常由多个可配置组件动态组合而成，负责展示核心产品信息。SSR 模式保证了其内容的快速初始呈现。
-   适用场景与优势: 这些页面通常面向公众或未登录用户。SSR 的主要优势在于提供更快的首屏加载体验和确保初始内容对所有客户端（包括爬虫）立即可用。

### SPA页面

-   渲染模式: `/pages/dashboard` 目录及其子目录下的所有页面，采用客户端渲染模式
-   功能定位: 这些页面通常构成项目的管理后台或用户登录后的主要操作界面。
-   适用场景与优势: 主要面向已登录或内部用户、能够调用后端API接口查询与执行某些操作。

## 编码

-   缩进: TypeScript 缩进为 4 空格 (包括 Vue `setup` 部分), HTML 缩进为 2 空格。
-   注释: 注释使用中文，应写在代码上方，而不是行尾。
-   日志: 统一使用 [logger.ts](mdc:utils/logger.ts)，禁止使用 `console.log`。
-   异常处理: `try...catch` 捕获的异常若未向上抛出，必须使用 [logger.ts](mdc:utils/logger.ts) 记录日志，并包含关键参数。
-   框架规范: 严格遵循 Nuxt.js 项目结构以及框架特征。
-   编码: 只对需求部分的代码进行改动，禁止修改和需求无关的其他部分代码(注释，代码格式等)

## 界面

-   交互思考优先: 在进行界面开发前，应先从产品经理的角度思考用户交互。分析如何设计能带来更直观、流畅、友好的用户体验，考虑用户的行为路径和期望，确保交互设计符合用户直觉。
-   组件库: 优先使用 shadcn-vue 组件库实现UI，所有组件已放在components/ui。
-   颜色系统: 严格使用项目定义的颜色变量系统（[tailwind.css](mdc:assets/css/tailwind.css)），确保 UI 颜色一致性。
-   Dark Mode: 已全局实现暗黑模式切换、无需手动在组件上设置。
-   图标: 设计稿中的图标应统一使用 `<Icon name=... />` 组件（来自于icones.js.org）。
-   间距与排版:
    -   使用Tailwind CSS 提供的间距和排版系统（如 `p-4`, `m-2`, `gap-6`, `text-lg` 等）来保持一致性。
    -   禁止使用自定义像素值（如 `px-[13px]`），除非有不可替代的理由并经过讨论。
-   表单：表单使用Shadcn-vue中的<Form />组件(对vee-validate库的封装)，使用zod进行表单验证

### 规范
-   在Nuxt中，调用navigateTo时，请确保始终使用await或return。

## Chat

-   在编码前，先简单简述一下，完成这个需求，你的思路是什么，然后才开始编码。

import {createApp} from 'vue'
import ConfirmDialog from '@/components/common/ConfirmDialog.vue'
import {useNuxtApp} from '#app'

// 类型定义
type ConfirmType = 'default' | 'success' | 'warning' | 'info' | 'error'
type ConfirmOptions = {
    type?: ConfirmType
    title?: string
    cancelText?: string
    confirmText?: string
    dangerouslyUseHTMLString?: boolean
}

// 创建确认对话框
const createConfirmDialog = (
    message: string,
    options: ConfirmOptions = {}
) => {
    return new Promise<void>((resolve, reject) => {
        // 创建一个容器div
        const container = document.createElement('div')
        document.body.appendChild(container)

        // 创建应用实例
        const app = createApp(ConfirmDialog, {
            message,
            visible: true,
            title: options.title,
            type: options.type || 'default',
            cancelText: options.cancelText,
            confirmText: options.confirmText,
            dangerouslyUseHTMLString: options.dangerouslyUseHTMLString || false,
            onConfirm: () => {
                // 确认按钮回调
                app.unmount()
                document.body.removeChild(container)
                resolve()
            },
            onCancel: () => {
                // 取消按钮回调
                app.unmount()
                document.body.removeChild(container)
                reject()
            }
        })

        // 挂载应用
        app.mount(container)
    })
}

// 默认的翻译文本
const defaultTranslations = {
    'confirm.default_title': '提示',
    'confirm.success_title': '成功',
    'confirm.warning_title': '警告',
    'confirm.info_title': '信息',
    'confirm.error_title': '错误',
    'confirm.confirm_button': '确定',
    'confirm.cancel_button': '取消',
    'confirm.delete_button': '删除'
}

/**
 * 获取翻译文本
 * 在 Nuxt 应用中，如果在 setup 外部使用 i18n，需要通过特殊方式获取
 */
const getTranslation = (key: string): string => {
    try {
        // 只在客户端环境下执行
        if (import.meta.client) {
            // 1. 尝试使用 nuxt 运行时配置获取 i18n
            const nuxtApp = useNuxtApp()
            if (nuxtApp && nuxtApp.$i18n) {
                return nuxtApp.$i18n.t(key)
            }
        }
    } catch (e) {
        console.warn('无法使用 Nuxt i18n，使用默认翻译', e)
    }

    // 2. 回退到默认翻译
    return defaultTranslations[key as keyof typeof defaultTranslations] || key
}

/**
 * 确认框组件，提供各种类型的确认框
 */
export function useConfirm() {
    // 确认框类
    class Confirm {
        success(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void) {
            const defaultTitle = getTranslation('confirm.success_title')
            return this._showConfirm(message, {
                type: 'success',
                title: title || defaultTitle
            }, confirmFunction, cancelFunction)
        }

        warning(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void) {
            const defaultTitle = getTranslation('confirm.warning_title')
            return this._showConfirm(message, {
                type: 'warning',
                title: title || defaultTitle
            }, confirmFunction, cancelFunction)
        }

        info(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void, options?: ConfirmOptions) {
            const defaultTitle = getTranslation('confirm.info_title')
            return this._showConfirm(message, {
                type: 'info',
                title: title || defaultTitle, ...options
            }, confirmFunction, cancelFunction)
        }

        error(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void) {
            const defaultTitle = getTranslation('confirm.error_title')
            return this._showConfirm(message, {
                type: 'error',
                title: title || defaultTitle
            }, confirmFunction, cancelFunction)
        }

        confirm(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void, options: ConfirmOptions = {}) {
            const defaultTitle = getTranslation('confirm.default_title')
            return this._showConfirm(message, {
                ...options,
                title: title || defaultTitle
            }, confirmFunction, cancelFunction)
        }

        private _showConfirm(message: string, options: ConfirmOptions = {}, confirmFunction?: () => void, cancelFunction?: () => void) {
            // 设置默认的按钮文本
            if (!options.confirmText) {
                options.confirmText = getTranslation('confirm.confirm_button')
            }

            if (!options.cancelText) {
                options.cancelText = getTranslation('confirm.cancel_button')
            }

            createConfirmDialog(message, options)
                .then(() => {
                    if (confirmFunction) {
                        confirmFunction()
                    }
                })
                .catch(() => {
                    if (cancelFunction) {
                        cancelFunction()
                    }
                })
        }
    }

    // 创建实例
    const confirm = new Confirm()

    return {
        confirm,
        createConfirmDialog
    }
}

// 单例实例，直接导出以便使用
export const confirm = {
    success(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void) {
        return useConfirm().confirm.success(message, title, confirmFunction, cancelFunction)
    },
    warning(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void) {
        return useConfirm().confirm.warning(message, title, confirmFunction, cancelFunction)
    },
    info(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void, options?: ConfirmOptions) {
        return useConfirm().confirm.info(message, title, confirmFunction, cancelFunction, options)
    },
    error(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void) {
        return useConfirm().confirm.error(message, title, confirmFunction, cancelFunction)
    },
    confirm(message: string, title?: string, confirmFunction?: () => void, cancelFunction?: () => void, options: ConfirmOptions = {}) {
        return useConfirm().confirm.confirm(message, title, confirmFunction, cancelFunction, options)
    }
}
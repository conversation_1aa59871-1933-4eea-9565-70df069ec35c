<script setup lang="ts">
import {cn} from '@/lib/utils'
import type {TestimonialResponse} from '~/types/api'
import TwitterPreview from '@/components/common/socials/TwitterPreview.vue'
import RedditPreview from '@/components/common/socials/RedditPreview.vue'

// 定义 Props
const props = withDefaults(defineProps<{
    testimonial: TestimonialResponse
    // 允许传入外部 class
    class?: string
}>(), {
    class: ''
})

// 移除了 colorMode 和 processedHtmlContent，因为预览组件会自行处理
</script>

<template>
  <Card :class="cn('break-inside-avoid', props.class)">
    <CardContent class="relative">
      <!-- 根据 thirdPlatformType 决定渲染内容 -->
      <template v-if="testimonial.thirdPlatformType === 'twitter' && testimonial.thirdPartPostId">
        <TwitterPreview :post-id="testimonial.thirdPartPostId"/>
      </template>
      <template v-else-if="testimonial.thirdPlatformType === 'reddit' && testimonial.thirdPartPostId">
        <RedditPreview :post-id="testimonial.thirdPartPostId"/>
      </template>

      <!-- 如果不是社交媒体嵌入，则渲染标准布局 -->
      <template v-else>
        <!-- 引用内容插槽 -->
        <slot name="quote">
          <blockquote v-if="testimonial.content" class="text-muted-foreground">
            <p>{{ `"${testimonial.content}"` }}</p>
          </blockquote>
        </slot>

        <!-- 作者信息插槽 -->
        <slot name="author">
          <figcaption v-if="testimonial.nickname || testimonial.account || testimonial.avatar"
                      class="mt-6 flex items-center gap-x-4">
            <!-- 头像插槽 -->
            <slot name="avatar">
              <a v-if="testimonial.href"
                 :href="testimonial.href"
                 target="_blank"
                 rel="noopener noreferrer">
                <Avatar v-if="testimonial.avatar" class="h-10 w-10 bg-muted">
                  <AvatarImage :src="testimonial.avatar" :alt="testimonial.nickname || 'User Avatar'"/>
                  <AvatarFallback>{{ testimonial.nickname?.charAt(0) || 'U' }}</AvatarFallback>
                </Avatar>
              </a>
              <Avatar v-else-if="testimonial.avatar" class="h-10 w-10 bg-muted">
                <AvatarImage :src="testimonial.avatar" :alt="testimonial.nickname || 'User Avatar'"/>
                <AvatarFallback>{{ testimonial.nickname?.charAt(0) || 'U' }}</AvatarFallback>
              </Avatar>
            </slot>

            <!-- 姓名和 Account 插槽 -->
            <slot name="authorInfo">
              <div class="text-sm leading-6">
                <div v-if="testimonial.nickname" class="font-semibold text-foreground">
                  {{ testimonial.nickname }}
                </div>
                <div v-if="testimonial.account" class="text-muted-foreground">
                  {{ testimonial.account }}
                </div>
              </div>
            </slot>

            <!-- Logo 插槽 -->
            <slot name="logo">
              <img v-if="testimonial.logo"
                   class="h-8 w-auto ml-auto"
                   :src="testimonial.logo"
                   alt="Company logo">
            </slot>
          </figcaption>
        </slot>
      </template>

      <!-- 通用内容插槽，允许添加额外内容 -->
      <slot/>

    </CardContent>
  </Card>
</template>
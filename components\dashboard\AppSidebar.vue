<script setup lang="ts">
import type {SidebarProps} from '@/components/ui/sidebar'
import NavMain from '@/components/dashboard/NavMain.vue'
import siteConfig from '~/config/site'
import {getLocalizedConfigText} from '~/utils/i18n'
import NavUser from '~/components/dashboard/NavUser.vue'
import type {DashboardConfig} from '~/types/site/dashboardMenuConfig'

const props = withDefaults(defineProps<SidebarProps & {
    menusConfig: DashboardConfig;
}>(), {
    collapsible: 'icon',
    side: 'left'
})

</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <NuxtLink :to="$localePath({ path: '/' })" class="text-primary">
              <img v-if="siteConfig.logo"
                   :src="siteConfig.logo"
                   alt="Logo"
                   class="w-10 h-10 mr-4 rounded-sm">
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="font-bold">{{ getLocalizedConfigText(siteConfig.title) }}</span>
              </div>
            </NuxtLink>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
    <SidebarContent>
      <NavMain :menu-navigation="props.menusConfig.sidebarMenus"/>
    </SidebarContent>
    <SidebarFooter>
      <NavUser :footer-links="props.menusConfig.sidebarFooterMenus"/>
    </SidebarFooter>
    <SidebarRail/>
  </Sidebar>
</template>

import type {NavLinksConfig} from '~/types/site/navLinks'

// 导航链接配置
const navLinks: NavLinksConfig = [
    {
        href: '/dashboard',
        icon: 'uil:apps',
        title: {
            en: 'Dashboard',
            zh: 'Dashboard'
        }
    },
    {
        href: '/dashboard/profile',
        icon: 'uil:apps',
        title: {
            en: 'Profile',
            zh: 'Profile'
        }
    },
    {
        href: '/admin',
        icon: 'uil:apps',
        title: {
            en: 'Admin',
            zh: 'Admin'
        }
    },
    {
        href: '/admin/user1',
        icon: 'uil:apps',
        title: {
            en: 'AdminUser1',
            zh: 'AdminUser1'
        }
    },
    {
        href: '/posts',
        icon: 'uil:dollar-sign',
        title: {
            en: 'Posts',
            zh: '博客'
        }
    }
]

export default navLinks
import type {UserTagEnum} from '~/utils/constants/enums/UserTagEnum'

/**
 * 博客文章视图类 <br/>
 */
export interface LoginResultResponse {

    /**
     * 用户id
     */
    userId: string

    /**
     * 用户名
     */
    username?: string;

    /**
     * token
     */
    token: string;

    /**
     * 昵称
     */
    nickname?: string

    /**
     * 头像
     */
    avatar?: string

    /**
     * 邮箱(已脱敏)
     */
    email?: string;

    /**
     * 手机号(已脱敏)
     */
    phone?: string;

    /**
     * 项目名
     */
    projectName?: string;

    /**
     * 注册时间
     */
    createTime: Date

    /**
     * 用户标签
     */
    userTag: UserTagEnum,

    /**
     * 第三方认证源名字
     */
    thirdAuthName?: string

    /**
     * 是否已设置密码
     */
    passwordStatus: boolean

}
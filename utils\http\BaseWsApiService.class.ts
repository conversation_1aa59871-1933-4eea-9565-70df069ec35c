/**
 * WebSocket API 服务 - 抽象基类
 *
 * 本文件提供了一个面向对象的解决方案，用于创建和管理多个独立的WebSocket服务实例。
 * - `BaseWsApiService`: 一个抽象基类，封装了所有通用的WebSocket连接和生命周期管理逻辑。
 *
 * @see /utils/http/websocketClient.ts 原始WebSocket客户端
 */
import {createWebSocket, type WebSocketClient, type WebSocketOptions} from './websocketClient'
import type {WebSocketResponse} from '~/utils/http/types'
import type {MCPRequest, MCPResponse, ToolCallParameter, ToolCallResult} from '~/types/aigc'

export type WsEventHandler<T = unknown> = (data: WebSocketResponse<T>) => void

// ============================ 抽象基类 ============================
export abstract class BaseWsApiService {

    protected wsClient: WebSocketClient | null = null
    private readonly baseOptions: Pick<WebSocketOptions, 'serverUri' | 'toolCallEventType'>
    private initStatus: boolean = false
    private toolCallResponseEventType: string

    private readonly requestIdStatusCache: Map<string, number> = new Map()

    protected constructor(baseOptions: Pick<WebSocketOptions, 'serverUri' | 'toolCallEventType'>) {
        this.baseOptions = baseOptions
        this.toolCallResponseEventType = `FORWARD__RESPONSE${baseOptions.toolCallEventType}`
    }

    /**
     * 初始化WebSocket连接。
     * @param options 包含nodeName和可选token的动态配置。
     */
    public init(options: Omit<WebSocketOptions, 'serverUri' | 'toolCallEventType'>): void {
        if (this.wsClient) {
            logger.info('此服务实例的WebSocket客户端已存在，无需重复初始化。')
            return
        }

        if (this.initStatus) {
            logger.info('此服务实例的WebSocket已执行初始化操作，无需重复执行')
            return
        }
        this.initStatus = true

        logger.info('正在初始化WebSocket客户端实例...', {
            options, baseOptions: this.baseOptions, finallyOptions: {
                ...this.baseOptions,
                ...options
            }
        })

        this.wsClient = createWebSocket({
            ...this.baseOptions,
            ...options
        })

        this.wsClient.open().catch(error => {
            logger.error('WebSocket连接失败', error)
            this.wsClient = null
        })
    }

    /**
     * 订阅一个WebSocket事件。
     * @param eventType 要订阅的事件类型。
     * @param handler 事件处理器。
     * @param requestId (可选) 用于精确匹配事件处理器(服务端会原封不不动返回)。
     */
    public on<T = unknown>(eventType: string, handler: WsEventHandler<T>, requestId?: string): void {
        this.wsClient?.registerEventCallback(handler, eventType, requestId)
    }

    /**
     * 订阅一个来自服务端请求转发的WebSocket事件, 该ws客户端充当一个服务器
     * @param eventType 要订阅的事件类型。
     * @param handler 事件处理器。
     */
    public onRequestForward<T = unknown>(eventType: string, handler: WsEventHandler<T>): void {
        this.wsClient?.registerRequestForwardEventCallback(handler, eventType)
    }

    /**
     * 订阅一个来自服务端响应转发的WebSocket事件,
     * @param eventType 要订阅的事件类型。
     * @param handler 事件处理器。
     * @param requestId
     */
    public onResponseForward<T = unknown>(eventType: string, handler: WsEventHandler<T>, requestId?: string): void {
        this.wsClient?.registerResponseForwardEventCallback(handler, eventType, requestId)
    }

    /**
     * 订阅一个来自服务端请求转发的WebSocket事件, 该ws客户端充当一个服务器
     * @param toolMethodName 工具名
     * @param handler 事件处理器。
     */
    public onToolCallRequest<T = unknown>(toolMethodName: string, handler: WsEventHandler<MCPRequest<T>>): void {
        this.wsClient?.registerToolCallEventRequestCallback(handler, toolMethodName)
    }

    /**
     * 订阅一个来自服务端响应转发的WebSocket事件,
     * @param toolMethodName 工具名
     * @param handler 事件处理器。
     * @param requestId
     */
    public onToolCallResponse<T = unknown>(toolMethodName: string, handler: WsEventHandler<MCPResponse<T>>, requestId?: string): void {
        this.wsClient?.registerToolCallEventResponseCallback(handler, toolMethodName, requestId)
    }

    /**
     * 关闭并清理WebSocket连接和订阅。
     */
    public close(): void {
        this.wsClient?.close()
        this.wsClient = null
        logger.info('WebSocket服务实例已关闭。')
    }

    public createForwardMetadata(forwardTargetNode: string): {
        forwardMode: 0 | 1 | 2,
        forwardTargetNode: string,
        ignoreServerResponse: boolean
    } {
        return {forwardMode: 1, forwardTargetNode: forwardTargetNode, ignoreServerResponse: true}
    }

    /**
     * (供子类使用) 发送消息的受保护方法。
     * @param data 要发送的数据。
     * @param eventType 事件类型。
     * @param metadata 元数据 可选
     * @param requestId (可选) 用于追踪响应的请求ID。
     */
    protected send<T>(data: T, eventType: string, metadata?: Record<string, unknown>, requestId?: string): void {
        if (!this.wsClient) {
            logger.warn('WebSocket未连接，无法发送消息')
            return
        }
        this.wsClient.send(data, eventType, metadata, requestId)
    }

    /**
     * 发起一个MCP工具调用
     * @param parameter 工具调用参数
     * @param metadata 元数据
     * @protected
     */
    protected executeToolCall<T = unknown, R = unknown>(parameter: ToolCallParameter<T>, metadata?: Record<string, unknown>): Promise<ToolCallResult<R>> {
        if (!parameter.name) {
            return Promise.reject('执行工具调用失败, 工具名为空')
        }

        // 生成一个requestId
        const toolMethodName = parameter.name
        const methodArguments = parameter.arguments
        const random = `${new Date().getTime()}-${getRandomInt(1, 10000)}`
        const sendRequestId = `${toolMethodName}_${random}`
        this.requestIdStatusCache.set(sendRequestId, 1)

        return new Promise((resolve, reject) => {
            this.wsClient?.registerToolCallEventResponseCallback((data: WebSocketResponse<MCPResponse<ToolCallResult<R>>>) => {
                logger.info(`【工具调用结果】工具: ${toolMethodName} 执行结果为`, {
                    inputArguments: methodArguments,
                    response: data,
                    toolResult: data.data?.result,
                    requestId: sendRequestId
                })

                this.requestIdStatusCache.delete(sendRequestId)
                this.wsClient?.removeCallback(this.toolCallResponseEventType, sendRequestId)

                if (data.code !== 200) {
                    reject(data.message)
                    return
                }

                if (!data.data) {
                    reject('工具调用mcp响应体缺失')
                    return
                }

                const mcpResponse: MCPResponse<ToolCallResult<R>> = data.data
                if (mcpResponse.error) {
                    reject(mcpResponse.error.message)
                    return
                }

                if (!mcpResponse.result) {
                    reject('工具执行响应体缺失')
                    return
                }

                // 如果执行失败
                if (mcpResponse.result.isError) {
                    const {content} = mcpResponse.result
                    if (!content) {
                        logger.error(`【工具调用结果】工具: ${toolMethodName} 执行失败, 但是content字段为空`)
                        reject('未知失败原因')
                        return
                    }

                    reject(content[0].text)
                    return
                }
                resolve(mcpResponse.result)
            }, parameter.name, random)

            const mcpRequest: MCPRequest<ToolCallParameter<T>> = {
                jsonrpc: '2.0',
                method: 'tools/call',
                params: parameter,
                id: null
            }

            logger.info(`【工具调用】正在调用工具 --> ${toolMethodName}`, {
                toolMethodName,
                arguments: methodArguments,
                requestId: sendRequestId
            })

            setTimeout(() => {
                if (this.requestIdStatusCache.delete(sendRequestId)) {
                    logger.warn(`【工具调用】超过10秒未接收到工具 ${toolMethodName} 工具的回调响应, 已从本地移除其回调方法`)
                    this.wsClient?.removeCallback(this.toolCallResponseEventType, sendRequestId)
                }
            }, 10000)
            this.send(mcpRequest, this.baseOptions.toolCallEventType, metadata, sendRequestId)
        })
    }
}

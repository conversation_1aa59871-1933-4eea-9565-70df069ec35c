<script lang="ts" setup>
import type {HTMLAttributes} from 'vue'
import {cn} from '@/lib/utils'
import {useI18n} from 'vue-i18n'
import SignInForm from '@/components/auth/SignInForm.vue'
import ForgotPasswordForm from '@/components/auth/ForgotPasswordForm.vue'

interface FormInfo {
    mode: number,
    contact?: string
}

const props = defineProps<{
    class?: HTMLAttributes['class']
    isOpen?: boolean
}>()

const emits = defineEmits<{
    'update:isOpen': [value: boolean]
    'login-success': []
}>()

const {t} = useI18n()

// 1. 登录 2. 注册 3. 忘记密码
const formMode = reactive<FormInfo>({
    mode: 1,
    contact: ''
})

// 切换到注册模式
const updateFormMode = (value: FormInfo) => {
    formMode.mode = value.mode
    formMode.contact = value.contact || ''
}

// 返回登录表单
const backToLogin = () => {
    formMode.mode = 1
    formMode.contact = ''
}
</script>

<template>

  <div :class="cn('flex flex-col gap-6', props.class)">
    <Card class="overflow-hidden p-0">
      <CardContent class="grid p-0">
        <div class="mx-auto w-full max-w-md p-6">
          <div class="flex flex-col gap-6">
            <!-- 标题 -->
            <div class="flex flex-col items-center text-center">
              <h1 class="text-2xl font-bold mb-2">
                <template v-if="formMode.mode === 3">
                  {{ t('auth.labels.reset_password') }}
                </template>
                <template v-else>
                  {{
                    formMode.mode === 2 ? t('auth.labels.signup_create_account') : t('auth.labels.login_welcome_back')
                  }}
                </template>
              </h1>
              <p class="text-muted-foreground text-balance">
                <template v-if="formMode.mode === 3">
                  {{ t('auth.labels.reset_enter_email') }}
                </template>
                <template v-else>
                  {{ formMode.contact }}
                </template>
              </p>
            </div>

            <!-- 根据模式显示不同的表单 -->
            <template v-if="formMode.mode === 3">
              <ForgotPasswordForm @back-to-login="backToLogin"/>
            </template>
            <template v-else>
              <SignInForm @update-form-mode="updateFormMode"/>
            </template>
          </div>
        </div>
      </CardContent>
    </Card>
    <div
      class="*:[a]:hover:text-primary *:[a]:underline *:[a]:underline-offset-4 text-balance text-center text-xs text-muted-foreground">
      {{ t('auth.login_terms') }}
      <NuxtLink :to="$localePath('/terms-of-service')">{{ t('auth.login_terms_service') }}</NuxtLink>
      {{ t('common.and') }}
      <NuxtLink :to="$localePath('/privacy-policy')">{{ t('auth.login_terms_privacy') }}</NuxtLink>
      .
    </div>

  </div>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue'
import {resolveLocalePath, tMsg} from '~/utils/i18n'
import type {ArticleResponse} from '~/types/api'
import ArticleDetail from '~/components/article/ArticleDetail.vue'

definePageMeta({
    layout: 'default'
})

const route = useRoute()
const slugFromRoute = computed(() => route.params.slug as string | undefined)

const loading = ref(true)
const blogPost = ref<ArticleResponse | null>(null)
const articleNotFound = ref(false)

const handlePostLoaded = (post: ArticleResponse | null) => {
    blogPost.value = post
    articleNotFound.value = !post
}

const handleLoadingStateChanged = (isLoading: boolean) => {
    loading.value = isLoading
}

const isBlogListPage = computed(() => !slugFromRoute.value)

useSeoMeta({
    title: computed(() => {
        if (isBlogListPage.value) {
            return tMsg('blog.page.siteTitle')
        }
        if (loading.value) {
            return tMsg('common.loading')
        }
        if (articleNotFound.value || !blogPost.value) {
            return tMsg('blog.page.articleNotFoundSiteTitle')
        }
        return blogPost.value.title
    }),
    description: computed(() => {
        if (isBlogListPage.value) {
            return tMsg('blog.page.listSiteDescription')
        }
        if (articleNotFound.value || !blogPost.value) {
            return tMsg('blog.page.articleNotFoundSiteTitle')
        }
        return blogPost.value.summary
    }),
    articleModifiedTime: computed(() => {
        if (blogPost.value && !articleNotFound.value) {
            return blogPost.value.updateTime?.toString() || new Date().toISOString()
        }
        return new Date().toISOString()
    })
})
</script>

<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 博客列表页面 -->
    <div v-if="isBlogListPage" class="max-w-4xl mx-auto">
      <div class="mb-10 text-center">
        <h1 class="text-4xl font-bold mb-4">{{ tMsg('blog.title') }}</h1>
      </div>
      <ArticleRecommendations :current-article-id="null"/>
    </div>

    <!-- 博客详情页面 -->
    <ArticleDetail v-else
                   :slug="slugFromRoute"
                   @post-loaded="handlePostLoaded"
                   @loading-state-changed="handleLoadingStateChanged">
      <template #header-prepend>
        <div class="mb-6">
          <NuxtLink :to="resolveLocalePath('/posts')"
                    class="flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors">
            <Icon name="heroicons:arrow-left" class="mr-1 w-4 h-4"/>
            {{ tMsg('blog.back_to_blog') }}
          </NuxtLink>
        </div>
      </template>

      <template #footer-append>
        <!-- 推荐阅读: Ensure it only shows when post is loaded and not in a notFound state -->
        <ArticleRecommendations v-if="!loading"
                                :current-article-id="blogPost ? blogPost.id : null"
                                :page-size="3"/>
      </template>
    </ArticleDetail>
  </div>
</template>

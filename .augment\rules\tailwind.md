---
type: "agent_requested"
description: "Tailwind CSS最佳实践指南，专注于与Vue 3和shadcn-vue组件库集成的样式开发，提供清晰的设计系统实现和性能优化建议。"
---

- 结合shadcn-vue组件库: 优先使用shadcn-vue组件库实现UI，它是基于Tailwind CSS构建的。
- 遵循移动优先原则: 优先为移动设备设计布局，然后使用响应式前缀(`sm:`、`md:`、`lg:`等)
  为更大的屏幕添加样式、如果按照这种方式无法更好的实现响应式的话，可按照可实现的方式实现。
- 使用项目颜色系统: 尊重shadcn-vue定义的颜色变量系统，确保UI颜色一致性。避免添加额外的自定义颜色，除非绝对必要。
- 创建可复用的组件时使用cn()工具函数:
    - 在该组件的顶层html元素中使用cn函数，参考[Input.vue](mdc:components/ui/input/Input.vue)
    - 使用`lib/utils.ts#cn()`辅助函数合并和条件应用类名
    - 这样可以更容易地组合Tailwind类并应用条件样式
  ```vue
    <button :class="cn('bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md', props.class)">按钮</button>
  ```
- 变体和黑暗模式支持:
    - 确保编写的组件在暗模式下表现良好，测试两种模式的视觉效果

## 2. 常见陷阱和最佳实践

- 避免内联样式:
    - 尽量不使用内联样式(`style`属性)，优先使用Tailwind类
    - 如需动态样式，使用类的条件应用而非内联样式


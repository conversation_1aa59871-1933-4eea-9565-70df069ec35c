<script setup lang="ts">
// 文档布局
</script>

<template>
  <div class="min-h-screen flex flex-col">
    <!-- 头部导航 -->
    <header class="border-b border-border sticky top-0 z-40 bg-background">
      <div class="container mx-auto flex items-center justify-between h-16 px-4">
        <div class="flex items-center gap-6">
          <NuxtLink :to="$localePath('/')" class="font-bold text-xl">
            零一创客
          </NuxtLink>
          <nav class="hidden md:flex items-center gap-4">
            <NuxtLink :to="$localePath('/docs')" class="text-sm font-medium hover:text-primary">
              文档
            </NuxtLink>
            <NuxtLink :to="$localePath('/blogs')" class="text-sm font-medium hover:text-primary">
              博客
            </NuxtLink>
            <NuxtLink :to="$localePath('/about')" class="text-sm font-medium hover:text-primary">
              关于
            </NuxtLink>
          </nav>
        </div>

        <div class="flex items-center gap-2">
          <!-- 主题切换 -->
          <button class="p-2 rounded-full hover:bg-accent">
            <Icon name="heroicons:sun" class="w-5 h-5"/>
          </button>

          <!-- 语言切换 -->
          <div class="relative inline-block">
            <button class="p-2 rounded-full hover:bg-accent">
              <Icon name="heroicons:globe-alt" class="w-5 h-5"/>
            </button>
          </div>

          <!-- 登录按钮 -->
          <button class="ml-2 px-4 py-1.5 rounded-md border border-border hover:bg-accent transition-colors">
            登录
          </button>
        </div>
      </div>
    </header>

    <!-- 主内容区 -->
    <main class="flex-1">
      <slot/>
    </main>

    <!-- 页脚 -->
    <footer class="border-t border-border py-8 mt-8 bg-card">
      <div class="container mx-auto px-4 text-center text-sm text-muted-foreground">
        <p>© {{ new Date().getFullYear() }} 零一创客. 保留所有权利。</p>
      </div>
    </footer>
  </div>
</template>
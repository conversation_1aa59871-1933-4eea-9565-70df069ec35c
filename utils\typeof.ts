/**
 * 类型判断工具函数
 */

import SparkMD5 from 'spark-md5'

/**
 * 检查值是否为文件类型
 * @param value 要检查的值
 * @returns 是否为File对象
 */
export function isFile(value: unknown): boolean {
  return typeof File !== 'undefined' && value instanceof File
}

/**
 * 格式化文件大小
 * @param bytes - 文件大小（字节）
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) {
    return '0 Bytes'
  }
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}

/**
 * 检查值是否为Blob类型
 * @param value 要检查的值
 * @returns 是否为Blob对象
 */
export function isBlob(value: unknown): boolean {
  return typeof Blob !== 'undefined' && value instanceof Blob
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 文件扩展名（不包含点号）
 */
export function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex !== -1 ? filename.slice(lastDotIndex + 1).toLowerCase() : ''
}

/**
 * 检查文件是否为图片类型
 * @param file 文件对象或文件名
 * @returns 是否为图片文件
 */
export function isImageFile(file: File | string): boolean {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico']

  if (typeof file === 'string') {
    const extension = getFileExtension(file)
    return imageExtensions.includes(extension)
  }

  // 检查MIME类型
  if (file.type.startsWith('image/')) {
    return true
  }

  // 如果MIME类型不可靠，检查文件扩展名
  const extension = getFileExtension(file.name)
  return imageExtensions.includes(extension)
}

/**
 * 检查文件是否为视频类型
 * @param file 文件对象或文件名
 * @returns 是否为视频文件
 */
export function isVideoFile(file: File | string): boolean {
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp']

  if (typeof file === 'string') {
    const extension = getFileExtension(file)
    return videoExtensions.includes(extension)
  }

  // 检查MIME类型
  if (file.type.startsWith('video/')) {
    return true
  }

  // 如果MIME类型不可靠，检查文件扩展名
  const extension = getFileExtension(file.name)
  return videoExtensions.includes(extension)
}

/**
 * 检查文件是否为音频类型
 * @param file 文件对象或文件名
 * @returns 是否为音频文件
 */
export function isAudioFile(file: File | string): boolean {
  const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a']

  if (typeof file === 'string') {
    const extension = getFileExtension(file)
    return audioExtensions.includes(extension)
  }

  // 检查MIME类型
  if (file.type.startsWith('audio/')) {
    return true
  }

  // 如果MIME类型不可靠，检查文件扩展名
  const extension = getFileExtension(file.name)
  return audioExtensions.includes(extension)
}

/**
 * 检查值是否为FormData类型
 * @param value 要检查的值
 * @returns 是否为FormData对象
 */
export function isFormData(value: unknown): boolean {
  return typeof FormData !== 'undefined' && value instanceof FormData
}

/**
 * 检查值是否为对象类型(不包括null)
 * @param value 要检查的值
 * @returns 是否为对象
 */
export function isObject(value: unknown): value is Record<string, any> {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

/**
 * 检查值是否为空(null或undefined)
 * @param value 要检查的值
 * @returns 是否为空
 */
export function isEmpty(value: unknown): value is null | undefined {
  return value === null || value === undefined
}

/**
 * 检查值是否为日期类型
 * @param value 要检查的值
 * @returns 是否为Date对象
 */
export function isDate(value: unknown): value is Date {
  return value instanceof Date && !isNaN(value.getTime())
}

/**
 * 格式化时间戳
 * 如果是今天，显示 HH:mm
 * 如果不是今天，显示 MM-DD
 * 如果不是今天 && detailTime，显示 MM-DD xx:xx:xx
 * @param timestamp - 时间戳
 * @param detailTime 是否展示详细时间
 */
export function formatHumanTime(timestamp: number, detailTime: boolean = false): string {
  const date = new Date(timestamp)
  const today = new Date()
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  const year = date.getFullYear().toString().padStart(2, '0')
  const currentYear = today.getFullYear().toString().padStart(2, '0')
  let yearValue = ''
  if (date.toDateString() === today.toDateString()) {
    return `${hours}:${minutes}`
  }

  if (year !== currentYear) {
    yearValue = `${year}-`
  }

  if (!detailTime) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${yearValue}${month}-${day}`
  }
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${yearValue}${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 格式化时间戳
 * @param timestamp - 时间戳
 */
export function formatTime(timestamp: number): string {
  const date = new Date(timestamp)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const year = date.getFullYear().toString().padStart(2, '0')
  return `${year}${month}-${day} ${hours}:${minutes}:${seconds}`
}

export function formatDate(date: Date | null, detailTime: boolean = false): string {
  if (!date) {
    return '----'
  }
  return formatHumanTime(date.getTime(), detailTime)
}

export function formatDateStr(date: string | null, detailTime: boolean = false): string {
  if (!date) {
    return '----'
  }
  return formatDate(new Date(date), detailTime)
}

/**
 * 获取一个在最小和最大值之间（包含两者）的随机整数
 * @param min - 最小值
 * @param max - 最大值
 * @returns {number} 返回一个随机整数
 */
export function getRandomInt(min: number, max: number): number {
  // 确保 min 和 max 是整数
  const minCeiled = Math.ceil(min)
  const maxFloored = Math.floor(max)
  // 公式：Math.random() * (max - min + 1) + min
  // Math.floor() 向下取整，确保结果是整数
  return Math.floor(Math.random() * (maxFloored - minCeiled + 1)) + minCeiled
}

/**
 * 计算文件的MD5哈希值
 * @param file 要计算哈希的文件
 * @returns Promise<string> MD5哈希值
 */
export function calculateFileHash(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()

    fileReader.onload = (event) => {
      try {
        const arrayBuffer = event.target?.result as ArrayBuffer
        const hash = SparkMD5.ArrayBuffer.hash(arrayBuffer)
        resolve(hash)
      } catch (error) {
        reject(new Error(`文件哈希计算失败: ${error}`))
      }
    }

    fileReader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    fileReader.readAsArrayBuffer(file)
  })
}

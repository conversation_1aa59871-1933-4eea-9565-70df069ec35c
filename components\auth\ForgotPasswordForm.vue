<script setup lang="ts">
import {toTypedSchema} from '@vee-validate/zod'
import * as z from 'zod'
import {useForm} from 'vee-validate'

import {RegexConstant} from '~/utils/constants/regex'
import {captcha<PERSON>pi} from '~/utils/api/captchaApi'
import {userAccountApi} from '~/utils/api/userAccountApi'
import logger from '~/utils/logger'
import type {ApiResponse} from '~/utils/http/types'
import {message} from '~/composables/useMessage'
import {tMsg} from '~/utils/i18n'
// const { t } = useI18n() // Nuxt 3 auto-imports t

const emits = defineEmits<{
    'back-to-login': []
}>()

// 定义 Zod schema 用于推断类型
const formSchemaZod = z.object({
    email: z
        .string()
        .min(1)
        .email({message: tMsg('validation.email')})
        .regex(RegexConstant.EMAIL, {message: tMsg('validation.email')}),
    verificationCode: z
        .string()
        .min(6, {message: tMsg('validation.error_code_min_length', {length: 6})}),
    newPassword: z
        .string()
        .min(6, {message: tMsg('validation.error_password_min_length', {length: 6})})
        .regex(RegexConstant.PASSWORD, {message: tMsg('validation.password')})
})

// 表单验证 Schema
const formSchema = toTypedSchema(formSchemaZod)

// 从 Zod schema 推断表单值的类型
type FormValues = z.infer<typeof formSchemaZod>;

// 初始化表单
const {handleSubmit, values: formValues, errors: formErrors, validateField} = useForm({
    validationSchema: formSchema,
    initialValues: {
        email: '',
        verificationCode: '',
        newPassword: ''
    }
})

// 用于"重置密码"按钮
const isSubmitting = ref(false)
// 用于"发送验证码"按钮
const isSendingCode = ref(false)
// 倒计时秒数
const countdown = ref(0)

// 发送验证码
const sendVerificationCode = async () => {
    // 首先验证邮箱字段
    const emailValidationResult = await validateField('email')
    if (!emailValidationResult.valid) {
        return
    }

    isSendingCode.value = true
    logger.info('API 调用：captchaApi.sendEmailCaptcha', {email: formValues.email})
    const response = await captchaApi.sendEmailCaptcha({email: formValues.email})
    if (response && response.code === 200) {
        message.info(tMsg('auth.code_sent_success'))
        logger.info('验证码发送成功', response)
        // 开始倒计时
        countdown.value = 60
        const timer = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
                clearInterval(timer)
            }
        }, 1000)
    } else {
        message.error(tMsg('auth.code_sent_failed'), {
            showSupport: true,
            requestId: response.requestId
        })
        logger.error('验证码发送失败', response)
    }
    isSendingCode.value = false
}

// 计算属性：发送按钮的文本
const sendButtonText = computed(() => {
    if (isSendingCode.value) {
        return tMsg('auth.labels.sendingCode')
    }
    if (countdown.value > 0) {
        return tMsg('auth.labels.resendCodeCooldown', {seconds: countdown.value})
    }
    return tMsg('auth.labels.get_code_btn')
})

// 计算属性：发送按钮是否禁用
const isSendButtonDisabled = computed(() => {
    return isSendingCode.value
        || !formValues.email
        || isSubmitting.value
        || countdown.value > 0
})

// 表单提交处理（重置密码）
const onSubmit = handleSubmit(async (data: FormValues) => {
    isSubmitting.value = true
    logger.info('API 调用：userAccountApi.updatePasswordByEmail', {email: data.email, captcha: data.verificationCode})
    const params = {
        email: data.email,
        captcha: data.verificationCode,
        password: data.newPassword
    }
    const response: ApiResponse<null> = await userAccountApi.updatePasswordByEmail(params)

    if (response && response.code === 200) {
        logger.info('密码重置成功', response)
        message.info(tMsg('auth.message_password_reset_success'))
        emits('back-to-login')
    } else {
        logger.error('密码重置失败', response)
        message.error(tMsg('auth.message_password_reset_failed'), {
            showSupport: true,
            requestId: response.requestId
        })
    }
    isSubmitting.value = false
})
</script>

<template>
  <form class="space-y-4" @submit="onSubmit">
    <FormField v-slot="{ componentField }" name="email">
      <FormItem>
        <FormLabel>{{ tMsg('auth.labels.email') }}</FormLabel>
        <FormControl>
          <Input type="email"
                 :placeholder="tMsg('auth.placeholders.email')"
                 v-bind="componentField"
                 :disabled="isSendingCode || isSubmitting"/>
        </FormControl>
      </FormItem>
    </FormField>

    <FormField v-slot="{ componentField }" name="verificationCode">
      <FormItem>
        <FormLabel>{{ tMsg('auth.labels.email_verification_code') }}</FormLabel>
        <div class="flex items-start gap-2">
          <FormControl class="flex-grow">
            <Input type="text"
                   :placeholder="tMsg('auth.placeHolders.verification_code')"
                   v-bind="componentField"
                   :disabled="isSubmitting"/>
          </FormControl>
          <Button type="button"
                  variant="outline"
                  class="shrink-0"
                  :disabled="isSendButtonDisabled"
                  @click="sendVerificationCode">
            {{ sendButtonText }}
          </Button>
        </div>
      </FormItem>
    </FormField>

    <FormField v-slot="{ componentField }" name="newPassword">
      <FormItem>
        <FormLabel>{{ tMsg('auth.labels.reset_new_password') }}</FormLabel>
        <FormControl>
          <Input type="password"
                 :placeholder="tMsg('auth.placeholders.new_password_min_length')"
                 v-bind="componentField"
                 :disabled="isSubmitting"/>
        </FormControl>
      </FormItem>
    </FormField>

    <Button type="submit" class="w-full" :disabled="isSubmitting">
      {{ isSubmitting ? tMsg('auth.labels.status_resetting_password') : tMsg('auth.labels.reset_password') }}
    </Button>

    <div class="flex items-center justify-between border-t border-border pt-4 mt-4">
      <Button variant="link"
              class="h-auto cursor-pointer p-0 text-sm text-muted-foreground hover:text-primary"
              @click.prevent="!isSubmitting && emits('back-to-login')">
        {{ tMsg('auth.labels.reset_back_login') }}
      </Button>
    </div>
  </form>
</template>

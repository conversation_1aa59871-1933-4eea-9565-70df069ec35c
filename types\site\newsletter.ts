import type {I18nTextMap} from '@/types/i18n'

/**
 * @description Newsletter 组件的配置驱动文本定义
 */
export interface NewsletterConfig {
    /**
     * @description 标题文本，可以包含 HTML (例如 <br>)
     */
    titleHtml: I18nTextMap
    /**
     * @description 描述文本 (纯文本)
     */
    descriptionText: I18nTextMap
    /**
     * @description 邮箱输入框的占位文本
     */
    emailPlaceholder: I18nTextMap
    /**
     * @description "订阅"按钮的默认文本
     */
    subscribeButtonText: I18nTextMap
    /**
     * @description "订阅中..."按钮的文本
     */
    subscribingButtonText: I18nTextMap
    /**
     * @description 隐私声明文本，可以包含 HTML (例如 <a> 标签)
     */
    privacyNoticeHtml: I18nTextMap
}
/**
 * 文件哈希工具函数
 * 使用SparkMD5库进行文件MD5哈希计算
 */

import SparkMD5 from 'spark-md5'
import { logger } from '@/utils/logger'

/**
 * 文件哈希计算选项
 */
export interface FileHashOptions {
    /** 分块大小，默认2MB */
    chunkSize?: number
    /** 进度回调函数 */
    onProgress?: (progress: number) => void
}

/**
 * 文件哈希计算结果
 */
export interface FileHashResult {
    /** MD5哈希值 */
    hash: string
    /** 文件大小（字节） */
    size: number
    /** 计算耗时（毫秒） */
    duration: number
}

/**
 * 计算文件的MD5哈希值
 * @param file 要计算哈希的文件
 * @param options 计算选项
 * @returns Promise<FileHashResult> 哈希计算结果
 */
export async function calculateFileHash(
    file: File,
    options: FileHashOptions = {}
): Promise<FileHashResult> {
    const startTime = Date.now()
    const { chunkSize = 2 * 1024 * 1024, onProgress } = options // 默认2MB分块
    
    return new Promise((resolve, reject) => {
        const spark = new SparkMD5.ArrayBuffer()
        const fileReader = new FileReader()
        let currentChunk = 0
        const chunks = Math.ceil(file.size / chunkSize)
        
        logger.info('开始计算文件MD5哈希', {
            fileName: file.name,
            fileSize: file.size,
            chunkSize,
            totalChunks: chunks
        })

        fileReader.onload = (e) => {
            try {
                const arrayBuffer = e.target?.result as ArrayBuffer
                if (arrayBuffer) {
                    spark.append(arrayBuffer)
                    currentChunk++
                    
                    // 更新进度
                    const progress = Math.round((currentChunk / chunks) * 100)
                    onProgress?.(progress)
                    
                    if (currentChunk < chunks) {
                        loadNext()
                    } else {
                        // 计算完成
                        const hash = spark.end()
                        const duration = Date.now() - startTime
                        
                        logger.info('文件MD5哈希计算完成', {
                            fileName: file.name,
                            hash,
                            duration: `${duration}ms`
                        })
                        
                        resolve({
                            hash,
                            size: file.size,
                            duration
                        })
                    }
                }
            } catch (error) {
                logger.error('文件哈希计算过程中发生错误', error)
                reject(new Error(`文件哈希计算失败: ${error}`))
            }
        }

        fileReader.onerror = (error) => {
            logger.error('文件读取失败', error)
            reject(new Error('文件读取失败'))
        }

        function loadNext() {
            const start = currentChunk * chunkSize
            const end = Math.min(start + chunkSize, file.size)
            const blob = file.slice(start, end)
            fileReader.readAsArrayBuffer(blob)
        }

        // 开始读取第一个分块
        loadNext()
    })
}

/**
 * 批量计算多个文件的MD5哈希值
 * @param files 文件数组
 * @param options 计算选项
 * @returns Promise<FileHashResult[]> 哈希计算结果数组
 */
export async function calculateMultipleFileHashes(
    files: File[],
    options: FileHashOptions = {}
): Promise<FileHashResult[]> {
    const results: FileHashResult[] = []
    
    logger.info('开始批量计算文件MD5哈希', { fileCount: files.length })
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i]
        try {
            const result = await calculateFileHash(file, {
                ...options,
                onProgress: (progress) => {
                    // 计算总体进度：当前文件在所有文件中的位置 + 当前文件的进度
                    const totalProgress = Math.round(((i + progress / 100) / files.length) * 100)
                    options.onProgress?.(totalProgress)
                }
            })
            results.push(result)
        } catch (error) {
            logger.error(`文件 ${file.name} 哈希计算失败`, error)
            throw error
        }
    }
    
    logger.info('批量文件MD5哈希计算完成', { 
        fileCount: files.length,
        totalSize: results.reduce((sum, r) => sum + r.size, 0)
    })
    
    return results
}

/**
 * 检查两个文件是否相同（通过MD5哈希比较）
 * @param file1 第一个文件
 * @param file2 第二个文件
 * @param options 计算选项
 * @returns Promise<boolean> 是否相同
 */
export async function compareFiles(
    file1: File,
    file2: File,
    options: FileHashOptions = {}
): Promise<boolean> {
    // 首先比较文件大小，如果大小不同则肯定不相同
    if (file1.size !== file2.size) {
        return false
    }
    
    try {
        const [hash1, hash2] = await Promise.all([
            calculateFileHash(file1, options),
            calculateFileHash(file2, options)
        ])
        
        return hash1.hash === hash2.hash
    } catch (error) {
        logger.error('文件比较过程中发生错误', error)
        throw error
    }
}

/**
 * 从文件数组中查找重复文件
 * @param files 文件数组
 * @param options 计算选项
 * @returns Promise<Map<string, File[]>> 以哈希值为键的重复文件映射
 */
export async function findDuplicateFiles(
    files: File[],
    options: FileHashOptions = {}
): Promise<Map<string, File[]>> {
    const hashMap = new Map<string, File[]>()
    
    try {
        const results = await calculateMultipleFileHashes(files, options)
        
        results.forEach((result, index) => {
            const file = files[index]
            const { hash } = result
            
            if (!hashMap.has(hash)) {
                hashMap.set(hash, [])
            }
            hashMap.get(hash)!.push(file)
        })
        
        // 只返回有重复的文件
        const duplicates = new Map<string, File[]>()
        hashMap.forEach((fileList, hash) => {
            if (fileList.length > 1) {
                duplicates.set(hash, fileList)
            }
        })
        
        logger.info('重复文件检测完成', {
            totalFiles: files.length,
            duplicateGroups: duplicates.size,
            duplicateFiles: Array.from(duplicates.values()).reduce((sum, group) => sum + group.length, 0)
        })
        
        return duplicates
    } catch (error) {
        logger.error('重复文件检测失败', error)
        throw error
    }
}

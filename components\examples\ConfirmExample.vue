<script setup lang="ts">
import {Button} from '@/components/ui/button'
// 使用Nuxt自动导入机制，无需手动导入useConfirm
// import { useConfirm, confirm } from '@/composables/useConfirm'

// 从composables中自动导入
const {confirm} = useConfirm()

// 方式一：使用 confirm 单例
const handleConfirmSuccess = () => {
    confirm.success(
        '操作已成功完成',
        undefined,
        () => {
            console.log('用户点击了确认')
        },
        () => {
            console.log('用户点击了取消')
        }
    )
}

const handleConfirmWarning = () => {
    confirm.warning(
        '确定要执行此操作吗？',
        '警告',
        () => {
            console.log('用户点击了确认')
        },
        () => {
            console.log('用户点击了取消')
        }
    )
}

const handleConfirmInfo = () => {
    confirm.info(
        '这是一条提示信息',
        '提示',
        () => {
            console.log('用户点击了确认')
        }
    )
}

const handleConfirmError = () => {
    confirm.error(
        '操作失败，请稍后再试',
        '错误',
        () => {
            console.log('用户点击了确认')
        }
    )
}

// 方式二：使用组合式API
const {createConfirmDialog} = useConfirm()

const handleCustomConfirm = async () => {
    try {
        await createConfirmDialog('是否确认删除？此操作不可撤销', {
            type: 'error',
            title: '删除确认',
            confirmText: '删除',
            cancelText: '取消'
        })
        console.log('用户确认删除')
        // 执行删除操作
    } catch (error) {
        console.log('用户取消删除')
    }
}

// 使用HTML内容
const handleHTMLConfirm = () => {
    confirm.info(
        '<strong>加粗的文本</strong>和<em>斜体文本</em>',
        'HTML内容示例',
        () => {
            console.log('用户点击了确认')
        },
        undefined,
        {dangerouslyUseHTMLString: true}
    )
}

// 添加默认导出
defineComponent({
    name: 'ConfirmExample'
})
</script>

<template>
  <div class="flex flex-col gap-4 p-4">
    <h2 class="text-xl font-bold">Confirm 对话框示例</h2>

    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Button @click="handleConfirmSuccess">成功确认</Button>
      <Button @click="handleConfirmWarning">警告确认</Button>
      <Button @click="handleConfirmInfo">信息确认</Button>
      <Button @click="handleConfirmError">错误确认</Button>
      <Button @click="handleCustomConfirm">自定义确认</Button>
      <Button @click="handleHTMLConfirm">HTML内容</Button>
    </div>
  </div>
</template>
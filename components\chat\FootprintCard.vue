<script setup lang="ts">
// 定义组件的 props
defineProps<{
    // 商品项数据
    item: {
        // 商品图片URL
        imageUrl: string
        // 标签文本
        tag: string
        // 标签背景色CSS类
        tagClass: string
        // 访问时间（例如 "3天前"）
        daysAgo: string
        // 价格
        price: string
    }
}>()
</script>

<template>
  <div class="flex-shrink-0 w-28 text-center">
    <div class="relative">
      <img :src="item.imageUrl" alt="product" class="w-full h-24 object-cover rounded-md">
      <div class="absolute top-0 left-0 text-white text-xs px-2 py-0.5 rounded-tl-md rounded-br-md"
           :class="item.tagClass">
        {{ item.tag }}
      </div>
    </div>
    <p class="text-xs mt-1 text-muted-foreground">
      {{ item.daysAgo }}
    </p>
    <p class="text-sm font-semibold">
      ¥ {{ item.price }}
    </p>
  </div>
</template>

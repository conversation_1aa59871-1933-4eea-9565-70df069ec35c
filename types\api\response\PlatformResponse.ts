import type {PlatformType} from '~/types/chat'

export interface PlatformAccount {
    nodeName: string | null
    accountId: string
    displayName: string | null
    connectTime: string | null
    disconnectTime: string | null
    nickname: string
    avatar: string | null
}

export interface Platform {
    id: string
    platformId: string
    name: string | null
    logo: string | null
    type: string
    status: boolean | null
    createTime: string | null
    updateTime: string | null
    accountDetailList: PlatformAccount[];
}

export interface PlatformDetail {
    platformType: PlatformType
    list: Platform[];
}

export type PlatformListResponse = PlatformDetail[]

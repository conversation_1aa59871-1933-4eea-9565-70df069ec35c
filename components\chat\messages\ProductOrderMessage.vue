<script setup lang="ts">
import {computed, ref} from 'vue'
import type {ChatMessage, ProductOrderMessage} from '@/types/chat'
import {Icon} from '#components'
import {formatHumanTime} from '~/utils/typeof'
import {orderStatusStyle, orderStatusText} from '~/utils/constants/OrderStatus'
import {copyToClipboard} from '~/composables/useClipboard'
import ProductShowcase from '~/components/chat/messages/ProductShowcase.vue'

// 定义组件的props
const props = defineProps<{
    // 商品消息对象
    message: ChatMessage
}>()

const orderInfo = computed(() => props.message.clientData as ProductOrderMessage)

const isOrderIdCopied = ref(false)

async function handleCopyOrderId() {
    if (!orderInfo.value.orderId)
    {return}
    const success = await copyToClipboard(orderInfo.value.orderId, false)
    if (success) {
        isOrderIdCopied.value = true
        setTimeout(() => {
            isOrderIdCopied.value = false
        }, 2000)
    }
}

function getStatusInfo(status: any) {
    return orderStatusStyle[status] || {icon: 'heroicons:question-mark-circle-20-solid', color: 'text-gray-500'}
}
</script>

<template>
  <div class="bg-white dark:bg-slate-800 rounded-lg p-3 max-w-xs w-80">
    <!-- 意图 -->
    <div v-if="message.list[0]?.messageContent"
         class="text-xs text-muted-foreground border-l-2 border-slate-200 dark:border-slate-700 pl-2 mb-3">
      {{ message.list[0].messageContent }}
    </div>

    <!-- 真实订单 -->
    <div v-if="orderInfo.orderExistStatus">
      <div class="pb-2 mb-2 border-b border-slate-200 dark:border-slate-700">
        <p v-if="orderInfo.orderId" class="text-xs text-muted-foreground flex items-center">
          订单号: {{ orderInfo.orderId }}
          <Icon v-if="!isOrderIdCopied"
                name="lucide:copy"
                class="w-3 h-3 ml-1.5 cursor-pointer hover:text-primary"
                @click="handleCopyOrderId"/>
          <Icon v-else name="heroicons:check-circle-20-solid" class="w-3.5 h-3.5 ml-1.5 text-green-500"/>
        </p>
        <div class="flex items-center justify-between mt-1">
          <div v-if="orderInfo.status" class="flex items-center gap-1.5" :class="getStatusInfo(orderInfo.status).color">
            <Icon :name="getStatusInfo(orderInfo.status).icon" class="h-5 w-5"/>
            <span class="font-semibold">{{ orderStatusText[orderInfo.status] }}</span>
          </div>
          <div v-else class="flex items-center gap-1.5 text-muted-foreground">
            <Icon name="heroicons:question-mark-circle-solid" class="h-5 w-5"/>
            <span class="font-semibold">订单状态未知</span>
          </div>
          <span v-if="orderInfo.createTime" class="text-xs text-muted-foreground">{{
            formatHumanTime(new
              Date(orderInfo.createTime).getTime(), true)
          }}</span>
        </div>
      </div>
    </div>

    <!-- 商品信息 (复用 ProductMessage 的布局) -->
    <!--<div class="flex items-center gap-3">-->
    <!--  <img :src="orderInfo.product.picture" :alt="orderInfo.product.title" class="w-16 h-16 rounded-md">-->
    <!--  <div class="flex flex-col justify-between flex-1">-->
    <!--    <a :href="orderInfo.product.productUrl" target="_blank" class="hover:underline">-->
    <!--      <p class="font-semibold line-clamp-2 text-blue-500">-->
    <!--        {{ orderInfo.product.title }}-->
    <!--      </p>-->
    <!--    </a>-->
    <!--    <div v-if="orderInfo.product.itemList && orderInfo.product.itemList.length > 0">-->
    <!--      <div v-for="item in orderInfo.product.itemList" :key="item.skuId" class="text-sm text-muted-foreground mt-1">-->
    <!--        <p v-if="item.skuTitle">-->
    <!--          规格: {{ item.skuTitle }}-->
    <!--        </p>-->
    <!--        <div class="flex justify-between items-center mt-1">-->
    <!--          <div>-->
    <!--            <span v-if="item.purchasePrice" class="text-red-500 font-bold">-->
    <!--              ¥{{ item.purchasePrice }}-->
    <!--            </span>-->
    <!--            <span v-if="item.originalPrice && item.purchasePrice && item.originalPrice !== item.purchasePrice"-->
    <!--                  class="text-xs text-muted-foreground line-through ml-2">-->
    <!--              ¥{{ item.originalPrice }}-->
    <!--            </span>-->
    <!--          </div>-->
    <!--          <span v-if="item.count" class="text-xs">数量: x{{ item.count }}</span>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--  </div>-->
    <!--</div>-->

    <product-showcase :product-info="orderInfo.product"/>

    <!-- 查看详情按钮 (仅真实订单) -->
    <div v-if="orderInfo.orderExistStatus" class="mt-2 pt-2 border-t border-slate-200 dark:border-slate-700">
      <a :href="orderInfo.orderDetailUrl"
         target="_blank"
         class="flex items-center justify-center text-sm text-blue-500 hover:underline">
        查看订单详情
        <Icon name="heroicons:arrow-right-20-solid" class="h-4 w-4 ml-1"/>
      </a>
    </div>
  </div>
</template>

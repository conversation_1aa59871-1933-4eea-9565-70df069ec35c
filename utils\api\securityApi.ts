import {type ApiResponse, Post} from '~/utils/http'
import {logger} from '~/utils/logger'
import type {ApplyTicketResponse} from '~/types/api/response/ApplyTicketResponse'

/**
 * 创建用户管理API
 */
function createSecurityApi() {

    /**
   * 申请ticket
   * @param serviceType 业务类型
   * @param primaryKey 主键
   * @returns ticket数据
   */
    function applyTicket(serviceType: string, primaryKey: string): Promise<ApiResponse<ApplyTicketResponse>> {
        logger.info('申请ticket', {serviceType, primaryKey})
        return Post<ApplyTicketResponse>('/plat/ticket/apply', {serviceType, primaryKey})
    }

    // 返回合并的API对象
    return {
        applyTicket
    }
}

// 导出用户API
export const securityApi = createSecurityApi()

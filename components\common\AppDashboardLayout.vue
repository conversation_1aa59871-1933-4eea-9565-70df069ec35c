<script setup lang="ts">
import {SidebarInset, SidebarProvider, SidebarTrigger} from '~/components/ui/sidebar'
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator
} from '~/components/ui/breadcrumb'
import {Separator} from '~/components/ui/separator'
import AppSidebar from '~/components/dashboard/AppSidebar.vue'
import {watch} from 'vue'
import {useRoute} from 'vue-router'
import {getLocalizedConfigText, resolveLocalePath} from '~/utils/i18n'
// 导入 DashboardConfig 和相关类型
import type {DashboardBreadcrumbItem} from '~/types/ui'
import type {DashboardConfig, DashboardGroupMenu} from '~/types/site/dashboardMenuConfig'

const props = defineProps<{
    dashboardConfig: DashboardConfig;
}>()

const dashboardBreadcrumbs = useState<DashboardBreadcrumbItem[]>('dashboardBreadcrumbs', () => [])
const route = useRoute()

// calculateBreadcrumbs 函数现在使用 props.dashboardConfig.sidebarMenus
function calculateBreadcrumbs(currentPath: string): DashboardBreadcrumbItem[] {
    const navLinkGroups: DashboardGroupMenu[] = props.dashboardConfig.sidebarMenus
    const newCrumbs: DashboardBreadcrumbItem[] = []

    for (const group of navLinkGroups) {
        const groupLabelText = group.label ? getLocalizedConfigText(group.label) : null

        for (const menuItem of group.menus) {
            const menuItemText = getLocalizedConfigText(menuItem.title)

            // 直接链接的菜单项
            if (menuItem.href && resolveLocalePath(menuItem.href) === currentPath) {
                if (groupLabelText) {
                    newCrumbs.push({text: groupLabelText})
                }
                newCrumbs.push({text: menuItemText, href: resolveLocalePath(menuItem.href)})
                return newCrumbs
            }

            // 带子菜单的项
            if (menuItem.children) {
                for (const subItem of menuItem.children) {
                    if (subItem.href && resolveLocalePath(subItem.href) === currentPath) {
                        if (groupLabelText) {
                            newCrumbs.push({text: groupLabelText})
                        }
                        // 父菜单项的文本（通常不带链接，除非设计如此）
                        newCrumbs.push({text: menuItemText})
                        newCrumbs.push({
                            text: getLocalizedConfigText(subItem.title),
                            href: resolveLocalePath(subItem.href)
                        })
                        return newCrumbs
                    }
                }
            }
        }
    }
    return newCrumbs
}

watch(() => route.path, (newPath) => {
    dashboardBreadcrumbs.value = calculateBreadcrumbs(newPath)
}, {immediate: true})

</script>

<template>
  <div>
    <SidebarProvider>
      <!-- AppSidebar 接收 dashboardConfig -->
      <AppSidebar :menus-config="props.dashboardConfig"/>
      <SidebarInset>
        <header
          class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div class="flex items-center gap-2 px-4">
            <SidebarTrigger class="-ml-1"/>
            <Separator orientation="vertical" class="mr-2 data-[orientation=vertical]:h-4"/>
            <Breadcrumb>
              <BreadcrumbList>
                <template v-if="dashboardBreadcrumbs && dashboardBreadcrumbs.length > 0">
                  <template v-for="(crumb, index) in dashboardBreadcrumbs" :key="crumb.text">
                    <BreadcrumbItem>
                      <BreadcrumbLink v-if="crumb.href && index < dashboardBreadcrumbs.length - 1"
                                      :href="crumb.href">
                        {{ crumb.text }}
                      </BreadcrumbLink>
                      <BreadcrumbPage v-else>
                        {{ crumb.text }}
                      </BreadcrumbPage>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator v-if="index < dashboardBreadcrumbs.length - 1"/>
                  </template>
                </template>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div class="p-4 pt-0 md:pb-4 md:px-12">
          <slot/>
        </div>
      </SidebarInset>
    </SidebarProvider>
  </div>
</template>
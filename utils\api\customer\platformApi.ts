import type {ApiResponse} from '~/utils/http/types'
import type {SubAccountListResponse} from '~/types/api/response/SubAccountResponse'
import type {SubAccountParams} from '~/types/api/params/SubAccountParams'
import type {PlatformListResponse} from '~/types/api/response/PlatformResponse'
import {Post} from '~/utils/http'

const baseUrl = '/customer-agent/user'

function createPlatformApi() {
    return {
        /**
         * 查询平台列表
         * @param options
         */
        getPlatformList(options?: {}): Promise<ApiResponse<PlatformListResponse>> {
            return Post<PlatformListResponse>(`${baseUrl}/queryPlatformList`, {}, options)
        },

        /**
         * 查询店铺的子账号列表
         * @param params
         * @param options
         */
        getSubAccountList(params: SubAccountParams): Promise<ApiResponse<SubAccountListResponse>> {
            // return Post<SubAccountListResponse>(`${baseUrl}/subaccount/list`, params, options)

            // Mock data based on shopId
            const mockData: SubAccountListResponse = []
            if (params.shopId.startsWith('shop-jd')) {
                mockData.push({
                    nodeName: 'jd-node-1',
                    account: '京东客服001',
                    connectTime: Date.now() - 1000 * 60 * 60 * 2, // 2 hours ago
                    nodeDesc: '京东一号节点'
                })
            } else if (params.shopId.startsWith('shop-tb')) {
                mockData.push(
                    {
                        nodeName: '**********',
                        account: '零一创客',
                        connectTime: Date.now() - 1000 * 60 * 30, // 30 mins ago
                        nodeDesc: '淘宝一号节点'
                    },
                    {
                        nodeName: 'tb-node-2',
                        account: '淘宝客服02',
                        connectTime: Date.now() - 1000 * 60 * 60 * 24, // 1 day ago
                        disconnectTime: Date.now() - 1000 * 60 * 55, // 55 mins ago
                        nodeDesc: '淘宝二号节点(已离线)'
                    }
                )
            } else if (params.shopId.startsWith('shop-pdd')) {
                mockData.push({
                    nodeName: 'pdd-node-1',
                    account: '拼多多客服-小丽',
                    connectTime: Date.now() - 1000 * 60 * 5, // 5 mins ago
                    nodeDesc: '拼多多官方节点'
                })
            }

            const response: ApiResponse<SubAccountListResponse> = {
                code: 200,
                message: 'Success',
                data: mockData
            }

            return Promise.resolve(response)
        }
    }
}

export const platformApi = createPlatformApi()

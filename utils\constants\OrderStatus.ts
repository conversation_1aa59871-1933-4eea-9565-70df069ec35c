import {OrderStatus} from '~/types/chat'

/**
 * 订单状态到中文文本的映射
 */
export const orderStatusText: Record<OrderStatus, string> = {
    [OrderStatus.WAIT_BUYER_PAY]: '等待买家付款',
    [OrderStatus.BUYER_PAID]: '等待卖家发货',
    [OrderStatus.SELLER_SHIPPED]: '卖家已发货',
    [OrderStatus.TRADE_SUCCESS]: '交易成功',
    [OrderStatus.TRADE_CLOSED]: '交易关闭',
    [OrderStatus.UNFINISHED]: '未完成的订单',
    [OrderStatus.REFUNDING]: '退款中的订单',
    [OrderStatus.DEPOSIT_PAID]: '定金已付',
    [OrderStatus.ABNORMAL]: '订单异常'
}

/**
 * 订单状态到对应图标和颜色的映射
 */
export const orderStatusStyle: Record<OrderStatus, { icon: string; color: string }> = {
    [OrderStatus.WAIT_BUYER_PAY]: {icon: 'heroicons:clock-20-solid', color: 'text-amber-500'},
    [OrderStatus.BUYER_PAID]: {icon: 'heroicons:archive-box-arrow-down-20-solid', color: 'text-blue-500'},
    [OrderStatus.SELLER_SHIPPED]: {icon: 'heroicons:truck-20-solid', color: 'text-cyan-500'},
    [OrderStatus.TRADE_SUCCESS]: {icon: 'heroicons:check-circle-20-solid', color: 'text-green-500'},
    [OrderStatus.TRADE_CLOSED]: {icon: 'heroicons:x-circle-20-solid', color: 'text-gray-500'},
    [OrderStatus.UNFINISHED]: {icon: 'heroicons:arrow-path-20-solid', color: 'text-gray-500'},
    [OrderStatus.REFUNDING]: {icon: 'heroicons:banknotes-20-solid', color: 'text-purple-500'},
    [OrderStatus.DEPOSIT_PAID]: {icon: 'heroicons:paper-clip-20-solid', color: 'text-indigo-500'},
    [OrderStatus.ABNORMAL]: {icon: 'heroicons:exclamation-triangle-20-solid', color: 'text-red-500'}
}

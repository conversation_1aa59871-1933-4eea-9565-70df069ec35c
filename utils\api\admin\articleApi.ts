import {createCrudApi} from '~/utils/api'
import type {ArticleParams} from '~/types/api/params/ArticleParams'
import type {ArticleResponse} from '~/types/api'

/**
 * <AUTHOR> <br/>
 * @description 博客文章 <br/>
 * @date 2025-05-17 16:16:52
 */
function createArticleApi() {
    const baseUrl = '/plat/admin/article'

    const articleCrudApi = createCrudApi<ArticleParams, ArticleResponse>(baseUrl)
    return {
        ...articleCrudApi
    }
}

// 导出用户API
export const articleApi = createArticleApi()
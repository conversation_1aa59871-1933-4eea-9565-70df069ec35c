<script setup lang="ts">
import {navigateTo, useRoute} from '#app'
import {useMessage} from '~/composables/useMessage'
import type {ApiResponse} from '~/utils/http'
import logger from '~/utils/logger'
import {localStg} from '~/utils/localStorageService'
import {LocalStorageConstant} from '~/utils/constants/localStorage'
import {resolveLocalePath, tMsg} from '~/utils/i18n'
import type {LoginResultResponse} from '~/types/api/response/LoginResultResponse'
import {executeClearUserData} from '~/utils/auth'
import {UserTagEnum} from '~/utils/constants/enums/UserTagEnum'

import {toTypedSchema} from '@vee-validate/zod'
import {useForm} from 'vee-validate'
import * as z from 'zod'
import {vAutoAnimate} from '@formkit/auto-animate/vue'
import {adminLoginApi} from '~/utils/api/adminLoginApi'
import {RegexConstant} from '~/utils/constants/regex'
import {projectConfig} from '~/config/projectConfig'

const {message: displayMessage} = useMessage()
const route = useRoute()

const dynamicLoginAreaHeight = ref<string>('100vh')

const zodSchema = z.object({
    username: z.string()
        .regex(RegexConstant.USERNAME, {message: tMsg('validation.username')}),
    password: z.string()
        .regex(RegexConstant.PASSWORD, {message: tMsg('validation.password')})
})

const loginFormSchema = toTypedSchema(zodSchema)

const {isFieldDirty, handleSubmit} = useForm({
    validationSchema: loginFormSchema,
    initialValues: {
        username: '',
        password: ''
    }
})

const calculateAndSetHeight = () => {
    const headerElement = document.getElementById('header')
    if (headerElement) {
        const headerHeight = headerElement.offsetHeight
        const availableHeight = window.innerHeight - headerHeight
        dynamicLoginAreaHeight.value = `${availableHeight}px`
    } else {
        dynamicLoginAreaHeight.value = '100vh'
    }
}

onMounted(async () => {
    calculateAndSetHeight()
    window.addEventListener('resize', calculateAndSetHeight)

    const localToken = localStg.get(LocalStorageConstant.TOKEN)
    const localUserInfo = localStg.get<LoginResultResponse>(LocalStorageConstant.USER_INFO)

    // 如果本地不存在token或者用户信息，都清除
    if (!localToken || !localUserInfo || UserTagEnum.ORGANIZATION_USER !== localUserInfo.userTag) {
        executeClearUserData()
        return
    }
    navigateTo(resolveLocalePath(projectConfig.dashboardEndpointUrl.admin), {replace: true})
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', calculateAndSetHeight)
})

const handleLogin = handleSubmit(async (values) => {
    logger.info('【admin】Attempting to login with credentials:', {username: values.username})
    const response: ApiResponse<LoginResultResponse> = await adminLoginApi.loginByAccount({
        username: values.username,
        password: values.password
    })

    if (response.code === 200 && response.data && response.data.token && response.data.userId) {
        localStg.set(LocalStorageConstant.TOKEN, response.data.token)
        localStg.set(LocalStorageConstant.USER_INFO, response.data)
        logger.info('【admin】Login successful via form. User details stored.', {details: response.data})
        displayMessage.success(tMsg('auth.login_successful_welcome_back'), {
            duration: 5000
        })
        setTimeout(() => {
            const redirectUri = route.query.redirectUri as string | undefined
            if (redirectUri) {
                navigateTo(redirectUri, {replace: true})
            } else {
                navigateTo(resolveLocalePath(projectConfig.dashboardEndpointUrl.admin), {replace: true})
            }
        }, 500)
    } else {
        logger.error('【admin】Login via form failed or API returned an error or data incomplete.', response)
        displayMessage.error(
            response.message,
            {
                requestId: response.requestId,
                showSupport: true,
                duration: 5000
            }
        )
    }
})

</script>

<template>
  <div :style="{ height: dynamicLoginAreaHeight }"
       class="flex items-center justify-center bg-background overflow-hidden">
    <div class="w-full max-w-md">
      <div class="flex flex-col gap-6">
        <Card class="overflow-hidden p-0">
          <CardContent class="grid p-0">
            <div class="mx-auto w-full max-w-md p-6">
              <form @submit.prevent="handleLogin">
                <div class="flex flex-col gap-6">
                  <div class="flex flex-col items-center text-center">
                    <h1 class="text-2xl font-bold mb-2">
                      {{ tMsg('auth.labels.login_welcome_back') }}
                    </h1>
                  </div>
                  <FormField v-slot="{ componentField }" name="username" :validate-on-blur="!isFieldDirty">
                    <FormItem v-auto-animate>
                      <FormLabel for="username">{{ tMsg('auth.labels.username') }}</FormLabel>
                      <FormControl>
                        <Input id="username"
                               type="text"
                               :placeholder="tMsg('auth.placeholders.username', {}, 'your username')"
                               v-bind="componentField"/>
                      </FormControl>
                      <FormMessage/>
                    </FormItem>
                  </FormField>

                  <FormField v-slot="{ componentField }" name="password" :validate-on-blur="!isFieldDirty">
                    <FormItem v-auto-animate>
                      <FormLabel for="password">{{ tMsg('auth.labels.password') }}</FormLabel>
                      <FormControl>
                        <Input id="password"
                               type="password"
                               :placeholder="tMsg('auth.placeholders.password', {}, 'your password')"
                               autocomplete="current-password"
                               v-bind="componentField"/>
                      </FormControl>
                      <FormMessage/>
                    </FormItem>
                  </FormField>

                  <Button type="submit" class="w-full cursor-pointer">
                    {{ tMsg('auth.labels.loginText', {}, 'Login') }}
                  </Button>
                </div>
              </form>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

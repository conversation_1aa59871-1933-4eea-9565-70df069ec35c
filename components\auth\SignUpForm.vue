<script setup lang="ts">
import {useI18n} from 'vue-i18n'
import {RegexConstant} from '~/utils/constants/regex'

const emits = defineEmits<{
		'toggle-mode': []
}>()

const {t} = useI18n()

// 表单数据
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const isSubmitting = ref(false)

// 验证
const emailRegex = RegexConstant.EMAIL
const isEmailValid = computed(() => {
    if (!email.value) {
        return true
    }
    return emailRegex.test(email.value)
})

const isPasswordMatch = computed(() => {
    if (!confirmPassword.value) {
        return true
    }
    return password.value === confirmPassword.value
})

// 处理注册表单提交
const handleSignUpSubmit = (e: Event) => {
    e.preventDefault()

    if (!email.value || !password.value || !confirmPassword.value) {
        return
    }

    if (!isEmailValid.value || !isPasswordMatch.value) {
        return
    }

    isSubmitting.value = true

    // 模拟注册API调用
    setTimeout(() => {
        console.log('注册信息:', email.value, password.value)
        isSubmitting.value = false

        // 注册成功后重置表单
        email.value = ''
        password.value = ''
        confirmPassword.value = ''

        // 切换到登录模式
        emits('toggle-mode')
    }, 1000)
}
</script>

<template>
  <form @submit="handleSignUpSubmit">
    <div class="flex flex-col gap-6">
      <!-- 邮箱输入框 -->
      <div class="grid gap-3">
        <Label for="signup-email">{{ t('auth.placeholders.email') }}</Label>
        <Input
          id="signup-email"
          v-model="email"
          type="email"
          placeholder="<EMAIL>"
          required
          :class="{ 'border-red-500': email && !isEmailValid }"
        />
        <p v-if="email && !isEmailValid" class="text-sm text-red-500 mt-1">
          {{ t('validation.email') }}
        </p>
      </div>

      <!-- 密码输入框 -->
      <div class="grid gap-3">
        <Label for="signup-password">{{ t('auth.login_password') }}</Label>
        <Input
          id="signup-password"
          v-model="password"
          type="password"
          required
        />
      </div>

      <!-- 确认密码输入框 -->
      <div class="grid gap-3">
        <Label for="confirm-password">{{ t('auth.labels.reset_confirm_password') }}</Label>
        <Input
          id="confirm-password"
          v-model="confirmPassword"
          type="password"
          required
          :class="{ 'border-red-500': confirmPassword && !isPasswordMatch }"
        />
        <p v-if="confirmPassword && !isPasswordMatch" class="text-sm text-red-500 mt-1">
          {{ t('auth.labels.reset_password_mismatch') }}
        </p>
      </div>

      <!-- 注册按钮 -->
      <Button type="submit" class="w-full" :disabled="isSubmitting">
        {{ isSubmitting ? t('auth.labels.resetSubmitting') : t('auth.signup_btn') }}
      </Button>

      <!-- 登录链接 -->
      <div class="text-center text-sm">
        {{ t('auth.labels.signup_have_account') }}
        <a href="#" class="underline underline-offset-4" @click.prevent="emits('toggle-mode')">
          {{ t('auth.labels.loginText') }}
        </a>
      </div>
    </div>
  </form>
</template>

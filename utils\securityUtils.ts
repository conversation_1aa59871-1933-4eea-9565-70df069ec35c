import {security<PERSON>pi} from '~/utils/api/securityApi'
import type {ApplyTicketResponse} from '~/types/api/response/ApplyTicketResponse'
import siteConfig from '~/config/site'
import {type ApiResponse, REQUEST_HEADERS, type RequestOptions} from '~/utils/http'
import {useCaptcha} from '~/composables/useCaptcha'
import {nextTick} from 'vue'
import {tMsg} from '~/utils/i18n'
import {CaptchaTypeEnum} from '~/utils/constants/enums/CaptchaTypeEnum'
import {projectConfig} from '~/config/projectConfig'

declare global {
    interface Window {
        TAC: any;
    }
}

export function applyTicket(serviceType: string, primaryKey: string, options: RequestOptions,
    captchaType: CaptchaTypeEnum = CaptchaTypeEnum.RANDOM): Promise<void> {
    const {openCaptchaDialog, closeCaptchaDialog} = useCaptcha()

    return new Promise<void>((resolve, reject) => {
        (async () => {
            if (!options) {
                message.error('尚未传递options')
                reject(new Error('options is required for applyTicket'))
                return
            }
            let header: Record<string, string> | undefined = options.headers
            if (!header) {
                header = {}
                options.headers = header
            }

            const currentHeaders = header
            const response: ApiResponse<ApplyTicketResponse> = await securityApi.applyTicket(serviceType, primaryKey)

            if (response.code !== 200) {
                message.error(response.message)
                reject(new Error(response.message))
                return
            }

            if (!response.data) {
                const errMsg = 'ApplyTicketResponse data is missing'
                message.error(errMsg)
                reject(new Error(errMsg))
                return
            }

            const status = response.data?.captchaStatus
            const ticket = response.data?.ticket

            if (ticket) {
                currentHeaders[REQUEST_HEADERS.TICKET] = ticket
            } else {
                const errMsg = 'Ticket is missing in ApplyTicketResponse'
                message.error(errMsg)
                reject(new Error(errMsg))
                return
            }

            if (status === undefined || !status) {
                resolve()
                return
            }

            if (projectConfig.captchaVerify.useDialog) {
                openCaptchaDialog()
            }
            await nextTick()

            const style = {
                logoUrl: siteConfig.logo,
                concatText: tMsg('captcha.concatText'),
                imageClickText: tMsg('captcha.imageClickText'),
                rotateText: tMsg('captcha.rotateText'),
                sliderText: tMsg('captcha.sliderText'),
                backgroundColor: 'var(--card)',
                tipTextColor: 'var(--card-foreground)',
                showBoxShadow: false
            }

            const {apiBase} = useRuntimeConfig().public
            const config = {
                requestCaptchaDataUrl: `${apiBase}/plat/captcha/generate`,
                validCaptchaUrl: `${apiBase}/plat/captcha/check`,
                bindEl: '#captcha-box',
                requestHeaders: {
                    ticket: ticket
                },
                paramData: {
                    captchaType: captchaType
                },
                validSuccess: (res: any, c: any, tac: any) => {
                    currentHeaders[REQUEST_HEADERS.CAPTCHA_ID] = res.data
                    resolve()
                    tac.destroyWindow()
                    if (projectConfig.captchaVerify.useDialog) {
                        closeCaptchaDialog()
                    }
                },
                btnCloseFun: (el: any, tac: any) => {
                    reject(new Error('Captcha closed by user'))
                    tac.destroyWindow()
                    if (projectConfig.captchaVerify.useDialog) {
                        closeCaptchaDialog()
                    }
                },
                generateFail: (res: any) => {
                    message.error(res.message)
                    reject(new Error(res.message))
                    if (projectConfig.captchaVerify.useDialog) {
                        closeCaptchaDialog()
                    }
                }
            }

            if (typeof window.TAC !== 'undefined') {
                new window.TAC(config, style).init()
            } else {
                const errMsg = 'TAC component is not loaded.'
                message.error(errMsg)
                reject(new Error(errMsg))
                if (projectConfig.captchaVerify.useDialog) {
                    closeCaptchaDialog()
                }
            }
        })()
    })
}

/* 
 * 自定义滚动条样式
 * 用于优化侧边栏和全局滚动条的外观
 */

/* WebKit 浏览器（Chrome、Safari、Edge等）的滚动条样式 */
::-webkit-scrollbar {
  width: 4px; /* 滚动条宽度减半 */
  height: 4px; /* 横向滚动条高度 */
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3); /* 使用半透明灰色 */
  border-radius: 20px;
  min-height: 40px;
  max-height: 50%; /* 长度减半 */
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* 侧边栏滚动条特殊样式 */
[data-sidebar="content"]::-webkit-scrollbar-thumb {
  max-height: 30%; /* 侧边栏滚动条更短 */
}

/* Firefox 浏览器的滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

/* 侧边栏在Firefox中的特殊样式 */
[data-sidebar="content"] {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
} 
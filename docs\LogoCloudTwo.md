# LogoCloudTwo 组件使用文档

`LogoCloudTwo` 是一个用于展示合作伙伴Logo的组件，采用左右两栏布局（大屏幕）。左侧通常用于展示标题、描述和行动号召按钮，右侧展示Logo列表。组件支持响应式设计，可在移动设备上切换Logo的显示模式（网格或滚动），并允许通过配置文件进行内容管理。

## Props (属性)

以下是 `LogoCloudTwo` 组件可接受的 props列表：

| Prop 名称             | 类型                     | 描述                                                                             | 可选值                  | 默认值      |
|:--------------------|:-----------------------|:-------------------------------------------------------------------------------|:---------------------|:---------|
| `mobileDisplayMode` | `'grid'` \| `'scroll'` | 控制移动设备上右侧Logo区域的显示模式。"grid" 表示网格布局，"scroll" 表示自动无限滚动。在非移动设备上，右侧Logo区域通常固定为网格布局。 | `'grid'`, `'scroll'` | `'grid'` |

## 数据配置

`LogoCloudTwo` 组件的内容主要由 `/config/logoCloud.ts` (或同名 `.json` 文件)驱动。

在该配置文件中，通常包含：

- `title`: `I18nTextMap` 类型，用于设置组件左侧的默认主标题。
- `description?`: `I18nTextMap` 类型 (可选)，用于设置组件左侧的默认描述文本。
- `logos`: 一个对象数组，每个Logo对象通常包含：
    - `name`: `string` (Logo的名称或品牌名，也用作`alt`文本)
    - `svgUrl`: `string` (Logo图片的URL，推荐使用SVG)

组件内部会使用 `getLocalizedConfigText` 工具函数根据当前i18n语言环境自动加载对应的标题和描述文本。

## 插槽 (Slots)

`LogoCloudTwo` 提供了丰富的插槽来自定义其各个部分：

- **`before_layout` (作用域插槽)**: 在组件整体布局（即左右两栏的父容器）之前插入内容。
    - **作用域数据**:
        - `logoConfig`: `LogoCloudConfig` - 从配置文件加载的完整Logo配置对象。

- **`title` (作用域插槽)**: 自定义左侧内容区域的主标题。会覆盖默认的标题显示。
    - **作用域数据**:
        - `localizedTitle`: `string` - 根据当前语言环境本地化后的标题文本。
        - `logoConfig`: `LogoCloudConfig` - 完整的Logo配置对象。

- **`description` (作用域插槽)**: 自定义左侧内容区域的描述文本。会覆盖默认的描述显示。
    - **作用域数据**:
        - `localizedDescription`: `string` - 根据当前语言环境本地化后的描述文本 (如果配置文件中提供了description)。
        - `logoConfig`: `LogoCloudConfig` - 完整的Logo配置对象。

- **`action`**: 在左侧内容区域的描述文本之后插入内容，通常用于放置行动号召按钮等。

- **`before_logo` (作用域插槽)**: 在右侧Logo展示区域（滚动或网格容器）之前，但在主两栏布局的右栏之内插入内容。
    - **作用域数据**:
        - `logoConfig`: `LogoCloudConfig` - 完整的Logo配置对象。

- **`scroll_logo_item` (作用域插槽)**: 自定义在移动设备"滚动"(`scroll`)模式下，右侧区域单个Logo项的渲染。
    - **作用域数据**:
        - `logo`: `{ name: string, svgUrl: string, ... }` - 当前正在迭代的单个Logo对象。

- **`grid_logo_item` (作用域插槽)**: 自定义在"网格"(`grid`)模式下（桌面端或移动端选择grid模式时），右侧区域单个Logo项的渲染。
    - **作用域数据**:
        - `logo`: `{ name: string, svgUrl: string, ... }` - 当前正在迭代的单个Logo对象。

- **`after_layout` (作用域插槽)**: 在组件整体布局之后插入内容。
    - **作用域数据**:
        - `logoConfig`: `LogoCloudConfig` - 完整的Logo配置对象。

## 基本用法示例

```vue
<template>
  <div>
    <!-- 移动端Logo使用滚动模式 -->
    <LogoCloudTwo mobile-display-mode="scroll">
      <!-- 必须提供 action 插槽内容，即使是空的，或者按需填充 -->
      <template #action>
        <NuxtLink to="/contact">
          <Button>联系我们</Button>
        </NuxtLink>
      </template>
    </LogoCloudTwo>

    <!-- 移动端Logo使用网格模式 (默认) -->
    <LogoCloudTwo />
  </div>
</template>

<script setup lang="ts">
// LogoCloudTwo 组件通常会自动导入
// Button 和 NuxtLink 也可能需要从 #components 或对应库导入，如果它们没有被全局注册
// import { Button } from '@/components/ui/button'
// import { NuxtLink } from '#components'
</script>
```

## 使用自定义插槽示例

```vue
<template>
  <LogoCloudTwo mobile-display-mode="scroll">
    <template #before_layout="{ logoConfig }">
      <div class="p-3 bg-blue-50 dark:bg-blue-900 text-center rounded-t-md">
        <p class="text-sm text-blue-700 dark:text-blue-300">
          即将展示 {{ logoConfig.logos?.length }} 个重要伙伴 (来自 before_layout)
        </p>
      </div>
    </template>

    <template #title="{ localizedTitle, logoConfig }">
      <h2 class="text-5xl font-bold tracking-tight text-blue-600 dark:text-blue-400">
        {{ localizedTitle }}
      </h2>
      <p class="text-sm text-gray-500 mt-1">共 {{ logoConfig.logos?.length }} 家</p>
    </template>

    <template #description="{ localizedDescription }">
      <p class="mt-4 text-lg leading-relaxed text-gray-700 dark:text-gray-300 italic">
        "{{ localizedDescription }}" - 我们的座右铭 (来自 description slot)。
      </p>
    </template>

    <template #action>
      <div class="mt-10 flex items-center gap-x-6">
        <NuxtLink to="/register">
          <Button size="lg" class="bg-blue-600 hover:bg-blue-700 text-white">
            立即加入
            <Icon name="lucide:arrow-right" class="ml-2" />
          </Button>
        </NuxtLink>
        <NuxtLink to="/about-us" class="text-sm font-semibold leading-6 text-gray-900 dark:text-gray-100">
          了解更多 <span aria-hidden="true">→</span>
        </NuxtLink>
      </div>
    </template>

    <template #before_logo="{ logoConfig }">
      <p class="text-xs text-gray-400 dark:text-gray-500 mb-2 text-right pr-2">
        精选 {{ logoConfig.logos?.length }} 个Logo (来自 before_logo):
      </p>
    </template>

    <template #scroll_logo_item="{ logo }">
      <div class="flex items-center justify-center h-16 w-auto p-2 mx-3 my-2 
                  bg-white dark:bg-gray-700 rounded-full shadow-lg 
                  border border-transparent hover:border-blue-500 transition-all">
        <img :src="logo.svgUrl" :alt="logo.name" class="max-h-8 object-contain">
      </div>
    </template>

    <template #grid_logo_item="{ logo }">
      <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg 
                  hover:shadow-xl transition-shadow duration-300 ease-in-out">
        <img :src="logo.svgUrl" :alt="logo.name" 
             class="max-h-10 w-full object-contain 
                    filter grayscale hover:grayscale-0 transition-all duration-300"
             width="150" height="40">
      </div>
    </template>

    <template #after_layout="{ logoConfig }">
      <div class="p-3 bg-blue-50 dark:bg-blue-900 text-center rounded-b-md mt-4">
        <p class="text-xs text-blue-600 dark:text-blue-400">
          感谢 {{ logoConfig.logos?.length }} 位合作伙伴的支持! (来自 after_layout)
        </p>
      </div>
    </template>
  </LogoCloudTwo>
</template>

<script setup lang="ts">
// import LogoCloudTwo from '~/components/market/logo-cloud/LogoCloudTwo.vue';
// import { Button } from '@/components/ui/button';
// import { Icon } from '#components'; // 或者具体的Icon组件库
// import { NuxtLink } from '#components';
</script>
```

**注意**: 上述插槽示例中的CSS类名主要基于Tailwind CSS。请根据您项目的实际样式系统进行调整。 
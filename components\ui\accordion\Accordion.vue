<script setup lang="ts">
import {AccordionRoot, type AccordionRootEmits, type AccordionRootProps, useForwardPropsEmits} from 'reka-ui'

const props = defineProps<AccordionRootProps>()
const emits = defineEmits<AccordionRootEmits>()

const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <AccordionRoot data-slot="accordion" v-bind="forwarded">
    <slot/>
  </AccordionRoot>
</template>

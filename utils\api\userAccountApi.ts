import {type ApiResponse, Post} from '~/utils/http'
import logger from '~/utils/logger'
import {tMsg} from '~/utils/i18n'
import type {
    Captcha,
    EmailWebPojo,
    LoginRegister,
    UpdatePasswordPojo,
    UserAccountPojo,
    UserDetailsPojo
} from '~/types/api'
import type {LoginResultResponse} from '~/types/api/response/LoginResultResponse'

const API_BASE_URL = '/app'

/**
 * 验证参数是否为空或未定义
 * @param value 需要验证的值
 * @returns 如果值为空/未定义/空字符串则返回 true
 */
function isNullOrEmpty(value: unknown): boolean {
    return value === undefined || value === null || value === ''
}

/**
 * 验证必填参数
 * @param params 参数对象
 * @param requiredFields 必填字段数组
 * @returns 验证结果，包含是否有效和错误消息
 */
function validateRequiredParams<T>(params: T, requiredFields: string[]): { isValid: boolean; errorMessage?: string } {
    if (!params) {
        return {isValid: false, errorMessage: '参数对象不能为空'}
    }

    for (const field of requiredFields) {
        if (isNullOrEmpty((params as Record<string, unknown>)[field])) {
            return {
                isValid: false,
                errorMessage: `${field} 是必填参数`
            }
        }
    }

    return {isValid: true}
}

function createUserAccountApi() {
    /**
     * 通过联系信息(account, phone, email)查询账号信息
     * 必填参数: account, phone, email
     */
    function queryAccountInfoByContact(params: UserAccountPojo): Promise<ApiResponse<LoginResultResponse>> {
        logger.info('Calling queryAccountInfoByContact', {
            username: params.username,
            phone: params.phone,
            email: params.email
        })
        return Post<LoginResultResponse>(`${API_BASE_URL}/queryAccountInfoByContact`, params)
    }

    /**
     * 通过联系信息(account, phone, email)验证验证码
     * 必填参数: account, phone, email
     */
    function judgeCaptchaByContact(params: Captcha): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['username', 'phone', 'email'])
        if (!validation.isValid) {
            logger.error('judgeCaptchaByContact 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: validation.errorMessage || tMsg('api.request_params_invalid'),
                data: null
            })
        }

        logger.info('Calling judgeCaptchaByContact', {
            username: params.username,
            phone: params.phone,
            email: params.email
        })
        return Post<null>(`${API_BASE_URL}/judgeCaptchaByContact`, params)
    }

    /**
     * 注销账户
     * 必填参数: token (在 Header 中，不需要在 body 中传递)
     */
    function deleteAccount(params?: UserDetailsPojo): Promise<ApiResponse<null>> {
        logger.info('Calling deleteAccount')
        return Post<null>(`${API_BASE_URL}/deletePlayer`, params || {})
    }

    /**
     * 修改当前登录用户的密码
     * 必填参数: username(account), password, newPassword
     */
    function updatePasswordByAccount(params: UpdatePasswordPojo): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['username', 'password', 'newPassword'])
        if (!validation.isValid) {
            logger.error('updatePasswordByAccount 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: validation.errorMessage || tMsg('api.request_params_invalid'),
                data: null
            })
        }

        logger.info('Calling updatePasswordByAccount', {
            username: params.username
        })
        return Post<null>(`${API_BASE_URL}/updatePassword`, params)
    }

    /**
     * 通过手机号，验证码修改密码
     * 必填参数: phone, captcha, password
     */
    function updatePasswordByPhone(params: LoginRegister): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['phone', 'captcha', 'password'])
        if (!validation.isValid) {
            logger.error('updatePasswordByPhone 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: validation.errorMessage || tMsg('api.request_params_invalid'),
                data: null
            })
        }

        logger.info('Calling updatePasswordByPhone', {
            phone: params.phone,
            captcha: params.captcha,
            password: '******'
        })
        return Post<null>(`${API_BASE_URL}/updatePwdByPhone`, params)
    }

    /**
     * 通过邮箱，验证码修改密码
     * 必填参数: email, captcha, password
     */
    function updatePasswordByEmail(params: LoginRegister): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['email', 'captcha', 'password'])
        if (!validation.isValid) {
            logger.error('updatePasswordByEmail 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: validation.errorMessage || tMsg('api.request_params_invalid'),
                data: null
            })
        }

        logger.info('Calling updatePasswordByEmail', {
            email: params.email,
            captcha: params.captcha,
            password: '******'
        })
        return Post<null>(`${API_BASE_URL}/updatePasswordByEmail`, params)
    }

    /**
     * 添加或更改新手机号
     * 必填参数: phone, captcha
     */
    function updateOrAddPhone(params: LoginRegister): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['phone', 'captcha'])
        if (!validation.isValid) {
            logger.error('updateOrAddPhone 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: validation.errorMessage || tMsg('api.request_params_invalid'),
                data: null
            })
        }

        logger.info('Calling updateOrAddPhone', {
            phone: params.phone,
            captcha: params.captcha
        })
        return Post<null>(`${API_BASE_URL}/updateOrAddPhone`, params)
    }

    /**
     * 添加或更改新邮箱
     * 必填参数: email, captcha
     */
    function updateOrAddEmail(params: LoginRegister): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['email', 'captcha'])
        if (!validation.isValid) {
            logger.error('updateOrAddEmail 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: validation.errorMessage || tMsg('api.request_params_invalid'),
                data: null
            })
        }

        logger.info('Calling updateOrAddEmail', {
            email: params.email,
            captcha: params.captcha
        })
        return Post<null>(`${API_BASE_URL}/updateOrAddEmail`, params)
    }

    /**
     * 通过token查询用户详细信息
     * 必填参数: token (在 Header 中，不需要在 body 中传递)
     */
    function queryUserDetailInfo(): Promise<ApiResponse<LoginResultResponse>> {
        logger.info('Calling queryUserDetailInfo')
        return Post<LoginResultResponse>(`${API_BASE_URL}/queryUserDetailInfo`)
    }

    /**
     * 修改用户信息
     * 必填参数: userId
     */
    function updateUserInfo(params: UserDetailsPojo): Promise<ApiResponse<null>> {
        logger.info('Calling updateUserInfo', {
            userId: params.userId,
            username: params.username,
            tag: params.tag,
            phone: params.phone,
            email: params.email,
            avatar: params.avatar
        })
        return Post<null>(`${API_BASE_URL}/web/updateUserInfo`, params)
    }

    /**
     * 绑定邮箱
     * 必填参数: email
     */
    function bindEmail(params: EmailWebPojo): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['email'])
        if (!validation.isValid) {
            logger.error('bindEmail 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: validation.errorMessage || tMsg('api.request_params_invalid'),
                data: null
            })
        }

        logger.info('Calling bindEmail', {
            email: params.email
        })
        return Post<null>(`${API_BASE_URL}/web/account/bindEmail`, params)
    }

    /**
     * 解绑邮箱
     * 必填参数: email
     */
    function unbindEmail(params: EmailWebPojo): Promise<ApiResponse<null>> {
        // 验证必填参数
        const validation = validateRequiredParams(params, ['email'])
        if (!validation.isValid) {
            logger.error('unbindEmail 参数验证失败', {errorMessage: validation.errorMessage, params})
            return Promise.resolve({
                code: 400,
                message: validation.errorMessage || tMsg('api.request_params_invalid'),
                data: null
            })
        }

        logger.info('Calling unbindEmail', {
            email: params.email
        })
        return Post<null>(`${API_BASE_URL}/web/account/unbindEmail`, params)
    }

    // 返回合并的API对象
    return {
        queryAccountInfoByContact,
        judgeCaptchaByContact,
        deleteAccount,
        updatePasswordByAccount,
        updatePasswordByPhone,
        updatePasswordByEmail,
        updateOrAddPhone,
        updateOrAddEmail,
        queryUserDetailInfo,
        updateUserInfo,
        bindEmail,
        unbindEmail
    }
}

// 导出用户账号API
export const userAccountApi = createUserAccountApi()
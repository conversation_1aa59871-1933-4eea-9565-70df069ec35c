---
type: manual
---

## 后端接口调用流程

1. 分析业务需求：明确功能、数据结构和未来可能的扩展
2. 定义类型：在 `types/api` 目录下创建类型
    - 所有请求参数类型放在 `params` 目录，命名为 `实体名Params.ts`
      ，如 [UserExampleParams.ts](mdc:types/api/params/UserExampleParams.ts)
    - 所有响应数据类型放在 `response` 目录，命名为 `实体名Response.ts`
      ，如 [UserExampleResponse.ts](mdc:types/api/response/UserExampleResponse.ts)
3. 实现API：在 `utils/api` 目录下创建API文件
    - 标准RESTful CRUD操作使用 `createCrudApi` [crud.ts](mdc:utils/api/crud.ts)，
    - 非RESTful接口 使用封装的 [index.ts](mdc:utils/http/index.ts) Post, Get等方法，并在调用前添加日志，一律返回Promise，不要await
    - 无论是调用第三方的还是项目后端提供的接口, 接口响应体类型一定是ApiResponse<T>泛型, 如果是调用第三方的接口,
      则需要创建responseTransformer对响应体进行转换
    - xxxApi.ts中的格式必须参考[userExampleApi.ts](mdc:utils/api/userExampleApi.ts)，也就是形如下面这种
      ```
      function createXXXApi() {
 
          // 定义的接口
          return {
              ....
          }
      }
      export const userXXXApi = createXXXApi()
      ```
4. 使用API：在组件中调用API，需要对响应体进行空值校验，在调用时，不需要try catch(底层封装已做)
   。可参考[UserExampleApiUsage.vue](mdc:components/examples/UserExampleApiUsage.vue)

## 接口调用状态判断

接口的响应格式为[types.ts](mdc:utils/http/types.ts) #ApiResponse, 是否请求成功都是通过code来判断的。其中只要code不等于200,
都可以认为后端未能成功处理请求.

## 日志记录

- 使用 [logger.ts](mdc:utils/logger.ts) 记录关键操作和异常
- 捕获的异常必须记录日志
- 不能在日志中打印用户敏感信息

## 组件中使用接口

- 接口的响应格式都是{code: integer, message: string(localizedMsg), data: <T>}, 判断请求是否成功通过code ==
  200进行判断。封装的接口内部使用useFetch()，可以类似于
  const { data, code, message } = await ourDefinedApi(requestParam)这样使用

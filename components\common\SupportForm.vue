<script lang="ts" setup>
import {useI18n} from 'vue-i18n'
import {RegexConstant} from '~/utils/constants/regex'

// 报告类型常量
const FEEDBACK = 'feedback'
const ERROR = 'error'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: '',
        validator: (value: string) => {
            return value === ''
                || value === 'feedback'
                || value === 'error'
        }
    },
    // 新增：是否支持 Request ID
    enableRequestId: {
        type: Boolean,
        default: false
    },
    // 新增：默认 Request ID 值
    requestIdValue: {
        type: String,
        default: ''
    },
    // 新增：Request ID 是否可编辑
    requestIdEditable: {
        type: Boolean,
        default: false
    },
    initValue: {
        type: String,
        default: ''
    }
})

const emits = defineEmits(['update:isOpen'])

const {t} = useI18n()

// 表单数据
const reportType = ref(props.type || '')
const description = ref('')
const email = ref('')
const requestId = ref(props.requestIdValue || '')
const isSubmitting = ref(false)
const isSuccess = ref(false)

// 用于选择组件的常量对象
const REPORT_TYPE = {
    FEEDBACK,
    ERROR
}

// 监听props.type变化
watch(() => props.type, (newType: string) => {
    if (newType) {
        reportType.value = newType
    }
})

// 监听props.requestIdValue变化
watch(() => props.requestIdValue, (newValue: string) => {
    requestId.value = newValue
})

// 监听props.isOpen变化，当打开对话框时重置表单
watch(() => props.isOpen, (newValue: boolean) => {
    if (newValue) {
        resetForm()
    }
})

watch(() => props.initValue, (newValue: string) => {
    if (newValue) {
        description.value = newValue
    }
})

// 判断表单是否可提交
const canSubmit = computed(() => {
    // 必填项：报告类型和描述
    const requiredFieldsFilled = reportType.value && description.value
    // 如果邮箱有值，则需要验证格式
    const emailValid = !email.value || isValidEmail(email.value)

    return requiredFieldsFilled && emailValid
})

// 验证邮箱
const emailRegex = RegexConstant.EMAIL
const isEmailValid = computed(() => {
    if (!email.value) {
        return true
    }
    return emailRegex.test(email.value)
})

const isFormSubmitted = ref(false)

// 验证邮箱函数
function isValidEmail(email: string): boolean {
    return emailRegex.test(email)
}

// 处理表单提交
const handleSubmit = () => {
    isFormSubmitted.value = true

    if (!canSubmit.value) {
        return
    }

    isSubmitting.value = true

    // 获取当前URL（不包含查询参数）
    const currentUrl = window.location.pathname

    // 模拟API调用
    setTimeout(() => {
        console.log('提交报告:', {
            type: reportType.value,
            description: description.value,
            // 如果邮箱为空，则发送undefined
            email: email.value || undefined,
            requestId: props.enableRequestId ? requestId.value : undefined,
            // 添加当前URL
            url: currentUrl
        })

        isSubmitting.value = false
        isSuccess.value = true

        // 移除自动关闭对话框的定时器
        // setTimeout(() => {
        //     closeDialog()
        //     resetForm()
        // }, 3000)
    }, 1000)
}

// 关闭对话框
const closeDialog = () => {
    emits('update:isOpen', false)
    // 休眠2秒再重置 否则会存在闪动问题
    setTimeout(() => {
        resetForm()
    }, 1500)
}

// 重置表单
const resetForm = () => {
    reportType.value = props.type || ''
    description.value = ''
    email.value = ''
    requestId.value = props.requestIdValue || ''
    isFormSubmitted.value = false
    isSuccess.value = false

    if (props.initValue) {
        description.value = props.initValue
    }
}

// 当点击外部区域或按下 Esc 键时关闭弹窗
const handleOutsideInteraction = () => {
    if (!isSubmitting.value) {
        closeDialog()
    }
}

</script>

<template>
  <AlertDialog
    :open="props.isOpen"
    @update:open="(value) => emits('update:isOpen', value)"
  >
    <AlertDialogContent
      class="sm:max-w-[425px]"
      @pointer-down-outside="handleOutsideInteraction"
      @escape-key-down="handleOutsideInteraction"
    >
      <div class="p-6 mx-auto w-full max-w-md">
        <!-- 成功消息 -->
        <div v-if="isSuccess" class="flex flex-col gap-6">
          <div class="text-center text-green-500">
            <h1 class="text-2xl font-bold text-foreground mb-4">{{ t('support.title') }}</h1>
            <p>{{ t('support.success') }}</p>
          </div>

          <!-- 关闭按钮 -->
          <Button
            type="button"
            class="w-full"
            @click="closeDialog"
          >
            {{ t('common.close') || 'Close' }}
          </Button>
        </div>

        <!-- 表单 -->
        <div v-else class="flex flex-col gap-6">
          <!-- 标题 -->
          <div class="flex flex-col items-center text-center">
            <h1 class="text-2xl font-bold mb-2 md:mb-4">
              {{ t('support.title') }}
            </h1>
            <p class="text-muted-foreground text-balance">
              {{ t('support.description') }}
            </p>
          </div>

          <form class="flex flex-col gap-4" @submit.prevent="handleSubmit">
            <!-- 单行布局的报告类型选择 -->
            <div class="flex items-center justify-between mb-4">
              <Label for="report-type" class="font-medium">{{ t('support.type') }}</Label>
              <div class="w-1/2">
                <Select v-model="reportType" required>
                  <SelectTrigger id="report-type" class="w-full">
                    <SelectValue :placeholder="t('support.select_type')"/>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem :value="REPORT_TYPE.FEEDBACK">
                      {{ t('support.type_feedback') }}
                    </SelectItem>
                    <SelectItem :value="REPORT_TYPE.ERROR">
                      {{ t('support.type_error') }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <p v-if="isFormSubmitted && !reportType" class="text-sm text-red-500 -mt-3 mb-3">
              {{ t('support.type_required') }}
            </p>

            <!-- Request ID 输入框（根据props.enableRequestId显示） -->
            <div v-if="props.enableRequestId" class="grid gap-4">
              <Label for="request-id">Request ID <span
                class="text-muted-foreground text-xs">{{
                props.requestIdValue && !props.requestIdEditable ? '' : `(${t('optional') || 'Optional'})`
              }}</span></Label>
              <Input
                id="request-id"
                v-model="requestId"
                type="text"
                placeholder="Request ID"
                :disabled="Boolean(props.requestIdValue && props.requestIdValue.length > 0 && !props.requestIdEditable)"
                :class="{'bg-muted': props.requestIdValue && !props.requestIdEditable}"
              />
            </div>

            <!-- 描述输入框 -->
            <div class="grid gap-4">
              <Label for="description">{{ t('support.description_label') }}</Label>
              <Textarea
                id="description"
                v-model="description"
                :placeholder="t('support.description_placeholder')"
                rows="4"
                required
                :class="{ 'border-red-500': isFormSubmitted && !description }"
              />
              <p v-if="isFormSubmitted && !description" class="text-sm text-red-500 mt-1">
                {{ t('support.description_required') }}
              </p>
            </div>

            <!-- 邮箱输入框 - 非必填 -->
            <div class="grid gap-4">
              <Label for="email">{{ t('email.text') }} <span
                class="text-muted-foreground text-xs">({{ t('optional') || 'Optional' }})</span></Label>
              <Input
                id="email"
                v-model="email"
                type="email"
                placeholder="<EMAIL>"
                :class="{ 'border-red-500': isFormSubmitted && email && !isEmailValid }"
              />
              <p v-if="isFormSubmitted && email && !isEmailValid" class="text-sm text-red-500 mt-1">
                {{ t('message.reset_email_invalid') }}
              </p>
            </div>

            <!-- 提交按钮 -->
            <Button
              type="submit"
              class="w-full mt-2"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? t('support.sending') : t('support.send') }}
            </Button>
          </form>
        </div>
      </div>
    </AlertDialogContent>
  </AlertDialog>
</template>

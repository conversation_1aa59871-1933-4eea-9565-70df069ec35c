<script setup lang="ts">
import {getLocalizedConfigText} from '@/utils/i18n'
import logoConfig from '@/config/logoCloud'
import AutoScrollContainer from '~/components/common/AutoScrollContainer.vue'
import type {LogoCloudConfig} from '~/types/site/logoCloud'

const props = withDefaults(defineProps<{
    mobileDisplayMode?: 'grid' | 'scroll'
}>(), {
    mobileDisplayMode: 'grid'
})

// 断言为配置类型，确保访问新增字段时类型安全
const config = logoConfig as LogoCloudConfig

// 获取本地化文本
const localizedTitle = computed(() => getLocalizedConfigText(config.title))
const localizedDescription = computed(() => config.description ? getLocalizedConfigText(config.description) : '')

// Logo 列表 (使用 logosTwo)
const logos = computed(() => config.logos || [])

</script>

<template>
  <div class="py-16">
    <div class="mx-auto max-w-7xl">
      <slot name="before_layout" :logo-config="config"/>

      <div class="lg:grid lg:grid-cols-2 lg:items-center lg:gap-x-16 lg:gap-y-10">
        <!-- Left side content -->
        <div class="max-w-2xl lg:max-w-none lg:col-span-1">
          <!-- Slot for title customization -->
          <slot name="title" :localized-title="localizedTitle" :logo-config="config">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-4 md:mb-6">
              {{ localizedTitle }}
            </h2>
          </slot>

          <!-- Slot for description customization -->
          <slot name="description" :localized-description="localizedDescription" :logo-config="config">
            <p v-if="localizedDescription" class="text-lg leading-8 text-muted-foreground">
              {{ localizedDescription }}
            </p>
          </slot>

          <slot name="action"/>
        </div>

        <slot name="before_logo" :logo-config="config"/>
        <div class="mt-8 lg:mt-0 lg:col-span-1">
          <!-- Scroll mode (Mobile) -->
          <div v-if="props.mobileDisplayMode === 'scroll'" class="lg:hidden">
            <AutoScrollContainer :pause-on-hover="true">
              <template v-for="(logo, index) in logos" :key="logo.name + '-scroll-' + index">
                <slot name="scroll_logo_item" :logo="logo">
                  <img class="h-10 flex-none object-contain px-4 sm:px-5" :src="logo.svgUrl" :alt="logo.name">
                </slot>
              </template>
            </AutoScrollContainer>
          </div>

          <!-- Grid mode (Mobile/Default & Desktop) -->
          <div :class="[
            'grid grid-cols-2 items-center justify-items-center gap-x-8 gap-y-10 sm:gap-x-10',
            props.mobileDisplayMode === 'scroll' ? 'hidden lg:grid' : 'grid'
          ]">
            <template v-for="(logo, index) in logos" :key="logo.name + '-grid-' + index">
              <slot name="grid_logo_item" :logo="logo">
                <img class="max-h-10 w-full object-contain"
                     :src="logo.svgUrl"
                     :alt="logo.name"
                     height="48">
              </slot>
            </template>
          </div>
        </div>
      </div>

      <slot name="after_layout" :logo-config="config"/>
    </div>
  </div>
</template>
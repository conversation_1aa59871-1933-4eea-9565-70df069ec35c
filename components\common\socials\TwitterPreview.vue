<template>
  <!-- 使用一个父元素来辅助重新创建 blockquote -->
  <div ref="widgetParent" class="-mt-[10px] -mb-[10px]"/>
</template>

<script setup lang="ts">
import {getCurrentLanguage} from '~/utils/i18n'

// 为 Twitter widgets 定义一个基本接口
interface TwttrWidgets {
    load: (element?: HTMLElement) => void;
}

// 扩展 Window 类型
interface WindowWithTwttr extends Window {
    twttr?: {
        widgets?: TwttrWidgets;
    };
}

const props = defineProps({
    postId: {
        type: String,
        required: true
    }
})

const colorMode = useColorMode()
const currentLanguage = ref(getCurrentLanguage())
const widgetParent = ref<HTMLElement | null>(null)

const ensureTwitterScriptLoaded = (callback: () => void) => {
    if (!import.meta.client) {
        return
    }
    const win = window as WindowWithTwttr
    if (win.twttr && win.twttr.widgets && typeof win.twttr.widgets.load === 'function') {
        callback()
    } else {
        // 如果 widgets.js 还没有加载，动态加载它
        const scriptId = 'twitter-widgets-script'
        if (document.getElementById(scriptId)) {
            // 脚本正在加载或已加载但 twttr 对象未立即可用，等待一会
            setTimeout(() => ensureTwitterScriptLoaded(callback), 100)
            return
        }

        const script = document.createElement('script')
        script.id = scriptId
        script.src = '/js/twitterWidgets.js'
        script.async = true
        script.charset = 'utf-8'
        script.onload = () => {
            callback()
        }
        script.onerror = () => {
            logger.error('Failed to load Twitter widgets.js')
        }
        document.head.appendChild(script)
    }
}

const loadTwitterWidget = () => {
    if (!import.meta.client || !widgetParent.value) {
        return
    }

    // 清空父容器
    widgetParent.value.innerHTML = ''

    // 重新创建 blockquote 元素
    const blockquote = document.createElement('blockquote')
    blockquote.className = 'twitter-tweet'
    blockquote.setAttribute('data-lang', currentLanguage.value)
    blockquote.setAttribute('data-theme', colorMode.value)

    const link = document.createElement('a')
    link.href = `https://twitter.com/x/status/${props.postId}`
    blockquote.appendChild(link)

    widgetParent.value.appendChild(blockquote)

    const win = window as WindowWithTwttr
    // 再次确认twttr.widgets.load是否可用
    if (win.twttr && win.twttr.widgets && typeof win.twttr.widgets.load === 'function') {
        win.twttr.widgets.load(widgetParent.value)
        logger.debug('twttr.widgets.load called on widgetParent.')
    } else {
        logger.warn('Twitter widgets.js not ready or load function is missing, retrying after script load ensured.')
    }
}

onMounted(() => {
    ensureTwitterScriptLoaded(() => {
        logger.debug('Twitter script ensured, calling loadTwitterWidget in onMounted.')
        loadTwitterWidget()
    })
})

// 监视 props、colorMode 和 currentLanguage 的变化
watch([() => props.postId, colorMode, currentLanguage], ([_newPostId, _newColorMode, _newLang], [_oldPostId, _oldColorMode, _oldLang]) => {
    // 确保脚本已加载再执行widget加载
    ensureTwitterScriptLoaded(() => {
        logger.debug('Twitter script ensured, calling loadTwitterWidget in watch.')
        loadTwitterWidget()
    })
}, {deep: true, immediate: false})

</script>
# AvatarCircles 组件使用文档

`AvatarCircles` 是一个用于展示一系列重叠头像的 Vue 组件，常用于表示用户群体、贡献者列表等。它具有灵活的尺寸控制和高度的可定制性。

## Props (属性)

以下是 `AvatarCircles` 组件可接受的 props列表：

| Prop 名称             | 类型                        | 描述                                                                     | 默认值         |
|:--------------------|:--------------------------|:-----------------------------------------------------------------------|:------------|
| `class`             | `HTMLAttributes['class']` | 应用于组件根 `div` 元素的额外 CSS 类。                                              | `undefined` |
| `numPeople`         | `number`                  | 除了直接在 `avatarUrls` 中列出的头像外，额外需要表示的人数。组件会将其格式化（例如，5334 显示为 5.33K）。      | `undefined` |
| `avatarUrls`        | `Avatar[]` (必填)           | 头像数据数组。每个 `Avatar` 对象包含 `imageUrl: string` 和 `profileUrl: string`。     | `[]`        |
| `avatarHeightClass` | `string`                  | 用于控制每个头像高度的 Tailwind CSS 类名 (例如 `'h-10'`, `'h-16'`)。组件会从此值中提取数字部分用于计算。 | `'h-10'`    |
| `avatarWidthClass`  | `string`                  | 用于控制每个头像宽度的 Tailwind CSS 类名 (例如 `'w-10'`, `'w-16'`)。                   | `'w-10'`    |

**Avatar 接口定义:**

```typescript
interface Avatar {
    imageUrl: string;  // 头像图片的URL
    profileUrl: string; // 点击头像后跳转的个人资料页URL
}
```

## 内部计算属性 (供理解，非直接配置)

组件内部有一些重要的计算属性，它们基于传入的 props 工作：

- `numericClassValue`: 从 `avatarHeightClass` (例如 `'h-10'`) 中提取数字部分 (`10`)。如果提取失败，默认为 `10`。
- `calculatedImgDimension`: 基于 `numericClassValue` 计算出 `<img>` 标签实际的 `width` 和 `height` 像素值 (公式:
  `numericClassValue * 4`)。
- `formattedNumPeopleDisplay`: 将 `numPeople` prop 格式化为一个紧凑的字符串 (例如，`5334` 会变成 `"5.33K"`)。
- `calculatedFontSize`: 根据 `numericClassValue` 动态计算"额外人数"指示器中数字文本的 `font-size` (公式:
  `Math.max(numericClassValue * 0.8, 8) + 'px'`)。

## 插槽 (Slots)

`AvatarCircles` 组件提供了丰富的插槽来自定义其内容和行为：

1. **`before` (作用域插槽)**
    - **描述**: 在整个头像组（包括所有头像和可能的溢出指示器）之前插入自定义内容。
    - **作用域数据**:
        - `numPeople: number | undefined` - 传入的 `numPeople` prop。
        - `avatarUrls: Avatar[]` - 传入的 `avatarUrls` prop。

2. **`avatars-list` (作用域插槽)**
    - **描述**: 完全自定义头像列表的渲染方式。如果提供了此插槽，默认的头像迭代渲染逻辑将被覆盖。
    - **作用域数据**:
        - `avatars: Avatar[]` - 即 `avatarUrls` 数组。
        - `avatarHeightClass: string | undefined` - 传入的 `avatarHeightClass`。
        - `avatarWidthClass: string | undefined` - 传入的 `avatarWidthClass`。
        - `calculatedImgDimension: number` - 组件内部计算出的、推荐用于自定义 `<img>` 标签的宽度/高度像素值。

3. **`overflow` (作用域插槽)**
    - **描述**: 自定义当 `numPeople` 大于 0 时，如何显示"额外人数"的指示器。如果提供了此插槽，默认的圆形计数器将被覆盖。
    - **作用域数据**:
        - `countDisplay: string` - 格式化后的数字字符串 (例如 `"5.33K"`)。
        - `numPeople: number | undefined` - 传入的原始 `numPeople` prop。

4. **`after` (作用域插槽)**
    - **描述**: 在整个头像组（包括所有头像和可能的溢出指示器）之后插入自定义内容。
    - **作用域数据**:
        - `numPeople: number | undefined` - 传入的 `numPeople` prop。
        - `avatarUrls: Avatar[]` - 传入的 `avatarUrls` prop。

## 基本用法示例

```vue

<template>
		<AvatarCircles
						:avatar-urls="sampleAvatars"
						:num-people="1250"
						avatar-height-class="h-12"
						avatar-width-class="w-12"
						class="my-custom-avatars-group"
		/>
</template>

<script setup lang="ts">
		import {ref} from 'vue';
		// AvatarCircles 组件通常会自动导入
		
		const sampleAvatars = ref([
				{imageUrl: 'https://example.com/avatar1.jpg', profileUrl: 'https://example.com/user1'},
				{imageUrl: 'https://example.com/avatar2.jpg', profileUrl: 'https://example.com/user2'},
				{imageUrl: 'https://example.com/avatar3.jpg', profileUrl: 'https://example.com/user3'},
		]);
</script>

<style scoped>
		.my-custom-avatars-group {
				padding: 1rem;
				background-color: #f9f9f9;
				border-radius: 8px;
		}
</style>
```

## 使用自定义插槽示例

```vue

<template>
		<AvatarCircles
						:avatar-urls="teamMembers"
						:num-people="88"
						avatar-height-class="h-16"
						avatar-width-class="w-16"
		>
				<template #before="{ avatarUrls }">
						<div class="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
								我们团队有 {{ avatarUrls.length }} 位核心成员，以及更多贡献者：
						</div>
				</template>
				
				<template #avatars-list="{ avatars, avatarHeightClass, avatarWidthClass, calculatedImgDimension }">
						<div
										class="flex items-center gap-1 p-1 border border-dashed border-blue-500 rounded-md bg-blue-50 dark:bg-blue-900/30">
								<p class="text-xs text-blue-700 dark:text-blue-300 mr-1">自定义列表:</p>
								<div v-for="avatar in avatars.slice(0, 2)" :key="avatar.imageUrl" class="text-center">
										<img
														:src="avatar.imageUrl"
														alt="成员头像"
														:class="[avatarHeightClass, avatarWidthClass, 'border-2', 'border-blue-400', '!rounded-md']"
														:style="{ 
              width: calculatedImgDimension + 'px', 
              height: calculatedImgDimension + 'px',
              objectFit: 'cover'
            }"
										/>
										<a :href="avatar.profileUrl" target="_blank" class="text-xs text-blue-600 dark:text-blue-400 hover:underline">
												资料
										</a>
								</div>
								<span v-if="avatars.length > 2" class="text-xs text-blue-500 dark:text-blue-400 ml-1">
          (+{{ avatars.length - 2 }}位未在此显示)
        </span>
						</div>
				</template>
				
				<template #overflow="{ countDisplay, numPeople }">
						<div
										class="flex items-center justify-center h-full w-full bg-green-500 text-white text-xs font-bold rounded-full border-2 border-white dark:border-gray-700 shadow-md px-1"
										title="更多贡献者"
						>
								+{{ countDisplay }}!
						</div>
				</template>
				
				<template #after>
						<div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
								感谢所有参与者。
						</div>
				</template>
		</AvatarCircles>
</template>

<script setup lang="ts">
		import {ref} from 'vue';
		
		const teamMembers = ref([
				{imageUrl: 'https://randomuser.me/api/portraits/men/32.jpg', profileUrl: '#/men32'},
				{imageUrl: 'https://randomuser.me/api/portraits/women/44.jpg', profileUrl: '#/women44'},
				{imageUrl: 'https://randomuser.me/api/portraits/men/56.jpg', profileUrl: '#/men56'},
		]);
</script>
``` 
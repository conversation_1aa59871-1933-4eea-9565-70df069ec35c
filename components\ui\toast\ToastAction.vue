<script setup>
import {Button} from '../button'

defineProps({
    altText: {
        type: String,
        required: true
    },
    variant: {
        type: String,
        default: 'secondary'
    },
    size: {
        type: String,
        default: 'sm'
    }
})
</script>

<template>
  <Button
    class="toast-action"
    :variant="variant"
    :size="size"
    :aria-label="altText"
  >
    <slot/>
  </Button>
</template>
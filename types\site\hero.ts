import type {I18nTextMap} from '@/types/i18n'

/**
 * 视频配置的类型
 */
export interface HeroConfigVideo {

    /**
     * 视频封面图地址
     */
    thumbnailSrc: string;

    /**
     * 视频地址
     */
    videoSrc: string;

    /**
     * 视频播放方式
     */
    playbackMode: 'dialog' | 'new-tab';
}

/**
 * 客户评价部分的类型
 */
export interface HeroConfigTestimonial {

    /**
     * 头像列表
     */
    avatars: Array<{ imageUrl: string; profileUrl: string }>;

    /**
     * 总人数
     */
    totalCount: number;

    /**
     * 客户评价的描述文本，支持国际化
     */
    descText: I18nTextMap;
}

/**
 * Hero 组件配置的完整类型
 */
export interface HeroConfig {

    /**
     * 主标题，支持HTML和国际化
     */
    headline: I18nTextMap;

    /**
     * 副标题，支持HTML和国际化
     */
    subHeadline: I18nTextMap;

    /**
     * 静态英雄图片路径 (可选，如果提供了视频则可能不显示)
     */
    heroImage?: string;

    /**
     * 英雄视频配置 (可选)
     */
    heroVideo?: HeroConfigVideo;

    /**
     * 行动号召按钮文本，支持国际化
     */
    ctaText: I18nTextMap;

    /**
     * 客户评价信息
     */
    testimonial: HeroConfigTestimonial;
}
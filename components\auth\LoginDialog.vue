<script lang="ts" setup>
import type {HTMLAttributes} from 'vue'
import LoginForm from '~/components/auth/LoginForm.vue'

const props = defineProps<{
    class?: HTMLAttributes['class']
    isOpen?: boolean
}>()

const emits = defineEmits<{
    'update:isOpen': [value: boolean]
    'login-success': []
}>()

// 状态管理
const isSignUpMode = ref(false)
const isForgotPasswordMode = ref(false)

// 关闭对话框
const closeDialog = () => {
    emits('update:isOpen', false)
    // 重置回登录模式，确保下次打开时默认显示登录表单
    isSignUpMode.value = false
    isForgotPasswordMode.value = false
}

// 当点击外部区域或按下 Esc 键时关闭弹窗
const handleOutsideInteraction = () => {
    closeDialog()
}
</script>

<template>
  <AlertDialog
    :open="props.isOpen"
    @update:open="(value) => emits('update:isOpen', value)"
  >
    <AlertDialogContent
      @pointer-down-outside="handleOutsideInteraction"
      @escape-key-down="handleOutsideInteraction"
    >
      <LoginForm/>
    </AlertDialogContent>
  </AlertDialog>
</template>

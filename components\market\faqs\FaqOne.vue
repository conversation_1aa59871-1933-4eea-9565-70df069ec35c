<script setup lang="ts">
import SupportForm from '@/components/common/SupportForm.vue'
import faqConfig from '@/config/faqs'
import {getLocalizedConfigText} from '@/utils/i18n/utils'
import type {FaqConfig} from '~/types/site/faq'

// 为导入的JSON配置应用类型
const faqConfigTyped = faqConfig as FaqConfig

// 检查FAQ列表中是否至少有一个项目包含图片
const hasAnyImage = computed(() => faqConfigTyped.items.some(item => !!item.image))

const props = withDefaults(defineProps<{
  expandAll?: boolean
    contentPosition?: 'left' | 'right'
}>(), {
    expandAll: false,
    contentPosition: 'left'
})

const isSupportFormOpen = ref(false)

// Generate an array of all item values like ['item-0', 'item-1', ...]
const allItemValues = computed(() => faqConfigTyped.items.map((_, index) => `item-${index}`))

// Determine initial open items based on expandAll prop
const initialOpenItems = computed(() => {
    if (props.expandAll) {
        return allItemValues.value
    }
    // Ensure faqList is not empty before accessing index 0
    return faqConfigTyped.items.length > 0 ? ['item-0'] : []
})

// State to track open accordion items
const openItems = ref<string | string[]>(initialOpenItems.value)

// Compute the image URL for the first open item (for PC view)
const selectedFaqImage = computed(() => {
    const currentOpenItems = Array.isArray(openItems.value) ? openItems.value : (openItems.value ? [openItems.value] : [])
    if (currentOpenItems.length === 0) {
        return null
    }
    // e.g., 'item-1'
    const firstOpenValue = currentOpenItems[0]
    const index = parseInt(firstOpenValue.split('-')[1], 10)
    if (!isNaN(index) && index >= 0 && index < faqConfigTyped.items.length) {
        return faqConfigTyped.items[index].image
    }
    return null
})
</script>

<template>
  <div class="py-16">
    <!-- Slot before main content -->
    <slot name="before-main-content" :faq-config="faqConfigTyped"/>
    <div class="lg:grid lg:grid-cols-12 lg:gap-8">
      <template v-if="props.contentPosition === 'left'">
        <div class="lg:col-span-5 mb-4 lg:mb-0">
          <slot name="header"
                :title="getLocalizedConfigText(faqConfigTyped.title)"
                :description="getLocalizedConfigText(faqConfigTyped.description)">
            <h2 class="text-2xl font-bold leading-10 tracking-tight text-foreground mb-3">
              {{ getLocalizedConfigText(faqConfigTyped.title) }}
            </h2>
            <p class="text-base leading-7 text-muted-foreground mb-3">
              {{ getLocalizedConfigText(faqConfigTyped.description) }}
            </p>
          </slot>
          <slot name="pc-image-display" :selected-faq-image="selectedFaqImage" :has-any-image="hasAnyImage">
            <div v-if="hasAnyImage" class="hidden lg:block relative aspect-[4/3] w-full rounded-lg overflow-hidden">
              <Transition name="fade">
                <img v-if="selectedFaqImage"
                     :key="selectedFaqImage"
                     :src="selectedFaqImage"
                     class="absolute inset-0 h-full w-full object-cover"
                     alt="FAQ Image">
              </Transition>
            </div>
          </slot>
        </div>
        <div class="lg:col-span-7 lg:mt-0">
          <Accordion v-model="openItems"
                     :type="props.expandAll ? 'multiple' : 'single'"
                     class="w-full"
                     collapsible
                     :default-value="props.expandAll ? allItemValues : (faqConfigTyped.items.length > 0 ? ['item-0'] : [])">
            <AccordionItem v-for="(faq, index) in faqConfigTyped.items" :key="index" :value="`item-${index}`">
              <AccordionTrigger class="text-base font-semibold leading-7 text-left no-underline hover:no-underline">
                <slot name="accordion-trigger"
                      :faq-item="faq"
                      :localized-question="getLocalizedConfigText(faq.content.question)">
                  {{ getLocalizedConfigText(faq.content.question) }}
                </slot>
              </AccordionTrigger>
              <AccordionContent class="text-base leading-7 text-muted-foreground">
                <slot name="accordion-content"
                      :faq-item="faq"
                      :localized-answer="getLocalizedConfigText(faq.content.answer)">
                  <div class="space-y-4">
                    <p>{{ getLocalizedConfigText(faq.content.answer) }}</p>
                    <slot name="mobile-image-display" :image-src="faq.image" :faq-item="faq">
                      <img v-if="faq.image"
                           :src="faq.image"
                           class="block aspect-video w-full rounded-lg object-cover shadow-md lg:hidden"
                           alt="FAQ Item Image">
                    </slot>
                  </div>
                </slot>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </template>
      <template v-else>
        <div class="lg:col-span-7 lg:mt-0">
          <Accordion v-model="openItems"
                     :type="props.expandAll ? 'multiple' : 'single'"
                     class="w-full"
                     collapsible
                     :default-value="props.expandAll ? allItemValues : (faqConfigTyped.items.length > 0 ? ['item-0'] : [])">
            <AccordionItem v-for="(faq, index) in faqConfigTyped.items" :key="index" :value="`item-${index}`">
              <AccordionTrigger class="text-base font-semibold leading-7 text-left no-underline hover:no-underline">
                <slot name="accordion-trigger"
                      :faq-item="faq"
                      :localized-question="getLocalizedConfigText(faq.content.question)">
                  {{ getLocalizedConfigText(faq.content.question) }}
                </slot>
              </AccordionTrigger>
              <AccordionContent class="text-base leading-7 text-muted-foreground">
                <slot name="accordion-content"
                      :faq-item="faq"
                      :localized-answer="getLocalizedConfigText(faq.content.answer)">
                  <div class="space-y-4">
                    <p>{{ getLocalizedConfigText(faq.content.answer) }}</p>
                    <slot name="mobile-image-display" :image-src="faq.image" :faq-item="faq">
                      <img v-if="faq.image"
                           :src="faq.image"
                           class="block aspect-video w-full rounded-lg object-cover shadow-md lg:hidden"
                           alt="FAQ Item Image">
                    </slot>
                  </div>
                </slot>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
        <div class="lg:col-span-5 mb-4 lg:mb-0">
          <slot name="header"
                :title="getLocalizedConfigText(faqConfigTyped.title)"
                :description="getLocalizedConfigText(faqConfigTyped.description)">
            <h2 class="text-2xl font-bold leading-10 tracking-tight text-foreground mb-3">
              {{ getLocalizedConfigText(faqConfigTyped.title) }}
            </h2>
            <p class="text-base leading-7 text-muted-foreground mb-3">
              {{ getLocalizedConfigText(faqConfigTyped.description) }}
            </p>
          </slot>
          <slot name="pc-image-display" :selected-faq-image="selectedFaqImage" :has-any-image="hasAnyImage">
            <div v-if="hasAnyImage" class="hidden lg:block relative aspect-[4/3] w-full rounded-lg overflow-hidden">
              <Transition name="fade">
                <img v-if="selectedFaqImage"
                     :key="selectedFaqImage"
                     :src="selectedFaqImage"
                     class="absolute inset-0 h-full w-full object-cover"
                     alt="FAQ Image">
              </Transition>
            </div>
          </slot>
        </div>
      </template>
    </div>
    <!-- Slot after main content -->
    <slot name="after-main-content" :faq-config="faqConfigTyped"/>
  </div>
  <SupportForm v-model:is-open="isSupportFormOpen"/>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
<script setup lang="ts">
import type {ArticleResponse} from '~/types/api'
import {getCurrentLocale, resolveLocalePath} from '~/utils/i18n/utils'
import {stripMarkdown} from '~/utils/stringUtils'
import {cn} from '~/lib/utils'
import type {HTMLAttributes} from 'vue'

// 定义 Props
interface Props {
    article: ArticleResponse;
    class?: HTMLAttributes['class'];
}

const props = withDefaults(defineProps<Props>(), {})
const slots = useSlots()

// 根据日期格式化函数
const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const currentLocale = getCurrentLocale()
    // 使用动态获取的 locale
    return date.toLocaleDateString(currentLocale, {month: 'short', day: 'numeric', year: 'numeric'})
}
</script>

<template>
  <!-- 默认样式 ('default') -->
  <slot name="before-layout" :article="article"/>

  <article :class="cn('flex flex-col bg-card overflow-hidden group', props.class)">
    <slot name="article-cover" :article="article">
      <img v-if="article.cover"
           :src="article.cover"
           :alt="article.title"
           class="w-full h-full object-cover transition-transform duration-300 rounded-lg mb-4">
    </slot>

    <slot name="article-detail" :article="article">
      <div class="flex flex-col flex-grow">
        <slot name="article-attr" :article="article">
          <div class="text-xs text-muted-foreground mb-2">
            <time :datetime="article.createTime">{{ formatDate(article.createTime) }}</time>
          </div>
        </slot>
        <slot name="article-content" :article="article">
          <h3 class="text-base font-semibold text-foreground mb-2 leading-tight">
            <NuxtLink :to="resolveLocalePath(`/posts/${article.slug}`)"
                      class="hover:text-primary transition-colors duration-200 line-clamp-2">
              {{ article.title }}
            </NuxtLink>
          </h3>
          <p class="text-sm text-muted-foreground flex-grow line-clamp-2">
            {{ stripMarkdown(article.content) }}
          </p>
        </slot>

        <!-- #article-default 插槽 -->
        <div :class="{ 'mt-4': slots['after-article'] }">
          <slot name="after-article" :article="article"/>
        </div>
      </div>
    </slot>
  </article>

  <slot name="after-layout" :article="article"/>
</template>
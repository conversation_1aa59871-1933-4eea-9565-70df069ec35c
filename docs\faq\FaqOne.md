# FaqOne 组件使用文档

`FaqOne` 是一个用于展示常见问题解答 (FAQ) 的 Vue 组件。它以可折叠的手风琴样式显示问题和答案，并支持在桌面端和移动端展示与问题相关的图片。组件内容和文本支持国际化。

## Props (属性)

以下是 `FaqOne` 组件可接受的 props列表：

| Prop 名称     | 类型        | 描述                                  | 可选值             | 默认值     |
|:------------|:----------|:------------------------------------|:----------------|:--------|
| `expandAll` | `boolean` | 是否默认展开所有FAQ项。如果为 `false`，则默认只展开第一项。 | `true`, `false` | `false` |

## 数据配置

`FaqOne` 组件的内容（包括组件的标题、描述以及FAQ列表）主要由 `/config/faqs.ts` 文件驱动。

在该配置文件中，通常会导出一个名为 `faqConfig` 的对象，其结构符合 `FaqConfig` 类型：

```typescript
interface FaqItemContent {
    question: I18nTextMap; // 问题文本，支持国际化
    answer: I18nTextMap;   // 答案文本，支持国际化
}

interface FaqItem {
    image?: string;          // 与此FAQ项关联的图片URL (可选)
    content: FaqItemContent; // FAQ项的内容
}

interface FaqConfig {
    title: I18nTextMap;       // 组件的整体标题，支持国际化
    description: I18nTextMap; // 组件的整体描述，支持国际化
    items: FaqItem[];         // FAQ项目列表
}

// I18nTextMap 示例:
// {
//   en: "English Text",
//   zh: "中文文本"
// }
```

组件内部会使用 `getLocalizedConfigText` 工具函数根据当前i18n语言环境自动加载对应的文本。

## 插槽 (Slots)

为了提供更高级的自定义能力，`FaqOne` 组件提供了以下插槽：

-   **`before-main-content`**: 在组件顶层容器的内部、主要FAQ网格布局之前插入内容。
-   **`header`**: 替换组件默认的标题和描述区域。
    -   **作用域数据**:
        -   `title: string` (已本地化的组件标题)
        -   `description: string` (已本地化的组件描述)
- **`pc-image-display`**: 自定义在桌面端（lg及以上断点）显示的图片区域。
    - **作用域数据**:
        - `selectedFaqImage: string | null` (当前选中的FAQ项的图片URL，如果没有选中或没有图片则为`null`)
        - `hasAnyImage: boolean` (表示FAQ列表中是否至少有一个项目包含图片)
- **`accordion-trigger` (作用域插槽)**: 自定义手风琴每个FAQ项的触发器（即问题显示部分）。
    - **作用域数据**:
        - `faqItem: FaqItem` (当前FAQ项的原始数据对象)
        - `localizedQuestion: string` (已本地化的当前FAQ项的问题文本)
        - `index: number` (当前FAQ项在列表中的索引)
- **`accordion-content` (作用域插槽)**: 自定义手风琴每个FAQ项展开后的内容区域（即答案显示部分）。
    - **作用域数据**:
        - `faqItem: FaqItem` (当前FAQ项的原始数据对象)
        - `localizedAnswer: string` (已本地化的当前FAQ项的答案文本)
        - `index: number` (当前FAQ项在列表中的索引)
- **`mobile-image-display` (作用域插槽)**: 自定义在移动端（lg以下断点）显示的图片区域，此插槽位于每个FAQ项展开内容之内。
    - **作用域数据**:
        - `imageSrc: string | undefined` (当前FAQ项的图片URL，如果该项没有图片则为`undefined`)
        - `faqItem: FaqItem` (当前FAQ项的原始数据对象)
- **`after-main-content`**: 在组件顶层容器的内部、主要FAQ网格布局之后，但在底部的 `SupportForm` 之前插入内容。

## 基本用法示例

```vue

<template>
		<FaqOne :expand-all="false"/>
</template>

<script setup lang="ts">
		// FaqOne 组件会自动导入，无需手动引入
</script>
```

## 使用自定义插槽示例

以下示例展示了如何使用 `FaqOne` 组件的各种插槽来自定义其外观和行为：

```vue

<template>
		<FaqOne>
				<!-- 示例：自定义 Header -->
				<template #header="{ title, description }">
						<div class="text-center mb-8">
								<h2 class="text-3xl font-extrabold tracking-tight text-primary">
										🤔 {{ title }} (来自Slot)
								</h2>
								<p class="mt-4 text-lg text-muted-foreground">
										{{ description }} (来自Slot)
								</p>
						</div>
				</template>
				
				<!-- 示例：自定义 PC 端图片展示 -->
				<template #pc-image-display="{ selectedFaqImage, hasAnyImage }">
						<div v-if="hasAnyImage"
											class="mt-10 hidden lg:block relative aspect-[4/3] w-full rounded-lg overflow-hidden border-2 border-dashed border-border">
								<Transition name="fade">
										<img v-if="selectedFaqImage"
															:key="selectedFaqImage"
															:src="selectedFaqImage"
															class="absolute inset-0 h-full w-full object-contain p-4"
															alt="FAQ Image (来自Slot)">
										<div v-else class="absolute inset-0 flex items-center justify-center bg-muted/50">
												<p class="text-muted-foreground italic">请选择一个问题来查看图片 (来自Slot)</p>
										</div>
								</Transition>
						</div>
						<div v-else class="mt-10 hidden lg:block text-center text-muted-foreground italic">
								当前没有任何问题配置了图片 (来自Slot)
						</div>
				</template>
				
				<!-- 示例：自定义手风琴触发器 -->
				<template #accordion-trigger="{ faqItem, localizedQuestion }">
      <span class="flex items-center">
        <Icon name="lucide:help-circle" class="mr-2 h-5 w-5 text-primary flex-shrink-0"/>
        <span class="font-semibold">{{ localizedQuestion }} (来自Slot)</span>
      </span>
				</template>
				
				<!-- 示例：自定义手风琴内容 -->
				<template #accordion-content="{ faqItem, localizedAnswer }">
						<div class="space-y-4">
								<p class="text-base leading-relaxed">
										💡 {{ localizedAnswer }} (来自Slot)
								</p>
								<p v-if="faqItem.image" class="text-sm text-muted-foreground">
										提示：相关图片会在PC侧边栏或下方展示。
								</p>
								<Button variant="outline" size="sm" @click="() => console.log('查看更多关于：', faqItem.content.question)">
										了解更多
								</Button>
						</div>
				</template>
				
				<!-- 示例：自定义移动端图片展示 -->
				<template #mobile-image-display="{ imageSrc, faqItem }">
						<div v-if="imageSrc" class="mt-4 lg:hidden">
								<img :src="imageSrc"
													class="block aspect-video w-full rounded-lg object-cover shadow-md"
													:alt="`Image for ${faqItem.content.question} (来自Slot)`">
								<p class="mt-2 text-xs text-center text-muted-foreground">
										{{ faqItem.content.question }} 的图片 (来自Slot)
								</p>
						</div>
				</template>
		</FaqOne>
</template>

<script setup lang="ts">
		import {Icon} from '#components' // 如果在插槽中使用了 Icon
		import {Button} from '@/components/ui/button' // 如果在插槽中使用了 Button
		// FaqOne 组件会自动导入
</script>
```

**注意**: 在插槽中使用像 `<Icon />` 或 `<Button />` 这样的组件时，如果它们不是全局注册或自动导入的，请确保在
`<script setup>` 中导入它们。不过，在当前项目中，这些常用组件通常是自动导入的。 
# Team 组件使用文档

`Team` 是一个用于展示团队成员信息的 Vue 组件，具有高度的可配置性和国际化支持。它允许灵活的布局调整和通过插槽进行深入定制。

## Props (属性)

以下是 `Team` 组件可接受的 props列表：

| Prop 名称                   | 类型                             | 描述                                                                                            | 可选值                          | 默认值            |
|:--------------------------|:-------------------------------|:----------------------------------------------------------------------------------------------|:-----------------------------|:---------------|
| `layout`                  | `'horizontal'` \| `'vertical'` | 控制整个组件的整体布局：`horizontal` 为左右结构 (左侧描述，右侧成员列表)，`vertical` 为上下结构。在小屏幕上，`horizontal` 也会自动堆叠为上下结构。 | `'horizontal'`, `'vertical'` | `'horizontal'` |
| `avatarShape`             | `'circle'` \| `'square'`       | 控制团队成员头像的形状。                                                                                  | `'circle'`, `'square'`       | `'circle'`     |
| `memberInfoLayout`        | `'horizontal'` \| `'vertical'` | 控制单个成员信息块（头像、姓名、职位）的内部布局。`horizontal`为头像在左，文字在右；`vertical`为头像在上，文字在下。                         | `'horizontal'`, `'vertical'` | `'horizontal'` |
| `itemsPerRowSm`           | `1` \| `2` \| `3` \| `4`       | 控制在中等屏幕 (`sm`) 及以上尺寸，每行显示的团队成员数量。                                                             | `1`, `2`, `3`, `4`           | `2`            |
| `showSocialIcons`         | `boolean`                      | 是否显示团队成员的社交媒体图标链接 (如果数据中提供)。                                                                  | `true`, `false`              | `false`        |
| `showDetailedDescription` | `boolean`                      | 是否显示团队成员的详细文字介绍 (如果数据中提供)。                                                                    | `true`, `false`              | `false`        |

## 数据配置

`Team` 组件的内容（包括默认的组件标题、描述以及团队成员列表）由 `/config/teamConfig.ts` (或相应的 `.json` 文件)驱动。

在该配置文件中：

- `title`: `I18nTextMap` 类型，用于设置组件的默认主标题，支持国际化。
- `description`: `I18nTextMap` 类型，用于设置组件的默认描述文本，支持国际化。
- `members`: 一个数组，每个成员对象 (`TeamMemberConfig`) 包含：
    - `imageUrl`: `string` (头像图片链接)
    - `name`: `I18nTextMap` (姓名，支持国际化)
    - `role`: `I18nTextMap` (职位/角色，支持国际化)
    - `description?`: `I18nTextMap` (详细介绍，可选，支持国际化)
    - `socials?`: `SocialLink[]` (社交链接数组，可选；`SocialLink` 包含 `name`, `url`, `icon`)

组件内部会使用 `getLocalizedConfigText` 工具函数根据当前i18n语言环境自动加载对应的文本，生成 `LocalizedTeamMember`
对象供模板使用。

## 插槽 (Slots)

`Team` 组件提供了以下插槽以实现灵活的定制：

- **`before-layout`**: 在组件最外层主要布局 (`md:grid-cols-3` 或 `grid-cols-1`) 开始之前插入内容。无作用域数据。

- **`header` (作用域插槽)**: 自定义或替换组件左侧（或垂直布局时的顶部）的标题和描述区域。
    - **作用域数据**:
        - `localizedTitle`: `string` - 本地化后的默认标题。
        - `localizedDescription`: `string` - 本地化后的默认描述。
        - `localizedTeam`: `LocalizedTeamMember[]` - 完整且已本地化的团队成员列表。

- **`before-members-layout` (作用域插槽)**: 在右侧（或垂直布局时标题下方）的团队成员列表区域 (`<ul>` 标签外部，但在其容器
  `<div>`内部) 开始之前插入内容。
    - **作用域数据**:
        - `localizedTeam`: `LocalizedTeamMember[]` - 完整且已本地化的团队成员列表。

- **`member-info` (作用域插槽)**: 自定义单个团队成员的头像、姓名和职位部分的渲染。此插槽位于每个成员的 `<li>` 内。
    - **作用域数据**:
        - `member`: `LocalizedTeamMember` - 当前正在迭代的单个成员对象 (已本地化的文本字段)。
        - `componentProps`: `Props` - 组件当前接收到的所有props对象 (例如 `avatarShape`, `memberInfoLayout` 等)
          ，方便在插槽内根据props调整样式或逻辑。

- **`member-details` (作用域插槽)**: 自定义单个团队成员的详细描述和社交链接部分的渲染。此插槽位于 `member-info`
  插槽之后，在同一个成员 `<li>` 内。
    - **作用域数据**:
        - `member`: `LocalizedTeamMember` - 当前正在迭代的单个成员对象。
        - `componentProps`: `Props` - 组件当前接收到的所有props对象 (例如 `showDetailedDescription`, `showSocialIcons`
          等)。

- **`after-layout`**: 在组件最外层主要布局结束之后，但在组件根 `<div>` 闭合之前插入内容。无作用域数据。

## 基本用法示例

```vue
<template>
		<Team
						layout="vertical"
						:items-per-row-sm="3"
						:show-detailed-description="true"
						:show-social-icons="true"
						avatar-shape="square"
						member-info-layout="vertical"
		/>
</template>

<script setup lang="ts">
		// Team 组件通常会自动导入 (Nuxt 3项目特性)
		// import Team from '~/components/market/team/Team.vue';
</script>
```

## 使用自定义插槽示例

```vue
<template>
		<Team :show-detailed-description="true" :show-social-icons="true" layout="horizontal">
				<template #before-layout>
						<div class="mb-4 p-2 bg-teal-50 dark:bg-teal-900 text-center rounded">
								<p class="text-sm text-teal-700 dark:text-teal-300">-- 我们的团队展示即将开始 --</p>
						</div>
				</template>
				
				<template #header="{ localizedTitle, localizedDescription, localizedTeam }">
						<div class="pr-8">
								<h2 class="text-4xl font-extrabold text-teal-600 dark:text-teal-400">
										{{ localizedTitle }} (共 {{ localizedTeam.length }} 位精英)
								</h2>
								<p class="mt-4 text-lg text-gray-600 dark:text-gray-300">
										{{ localizedDescription }} 我们致力于创新与卓越。
								</p>
						</div>
				</template>
				
				<template #before-members-layout="{ localizedTeam }">
						<div class="mb-6 border-b pb-2">
								<h3 class="text-xl font-semibold text-gray-700 dark:text-gray-200">
										核心成员 ({{ localizedTeam.length }} 人):
								</h3>
						</div>
				</template>
				
				<template #member-info="{ member, componentProps }">
						<div :class="[
        'flex items-center gap-x-4',
        componentProps.memberInfoLayout === 'vertical' ? 'flex-col items-start' : 'items-center'
      ]">
								<img
												:class="[
            'h-20 w-20 object-cover',
            componentProps.avatarShape === 'square' ? 'rounded-lg' : 'rounded-full',
            componentProps.memberInfoLayout === 'vertical' ? 'mb-3' : ''
          ]"
												:src="member.imageUrl"
												:alt="member.name + ' avatar'"
												loading="lazy"
								>
								<div>
										<h4 class="text-xl font-bold text-gray-900 dark:text-white">{{ member.name }}</h4>
										<p class="text-md text-teal-500 dark:text-teal-400">{{ member.role }}</p>
								</div>
						</div>
				</template>
				
				<template #member-details="{ member, componentProps }">
						<div class="mt-3 pl-0">
								<p v-if="componentProps.showDetailedDescription && member.description"
											class="text-sm text-gray-600 dark:text-gray-400 italic border-l-2 border-teal-500 pl-3 py-1">
										{{ member.description }}
								</p>
								<div v-if="componentProps.showSocialIcons && member.socials && member.socials.length > 0"
													class="mt-3 flex items-center gap-x-3">
										<a v-for="social in member.socials"
													:key="social.name"
													:href="social.url"
													target="_blank"
													rel="noopener noreferrer"
													class="text-gray-400 hover:text-teal-500 dark:hover:text-teal-300 transition-colors">
												<Icon :name="social.icon" class="h-6 w-6"/>
										</a>
								</div>
						</div>
				</template>
				
				<template #after-layout>
						<div class="mt-10 pt-6 border-t text-center">
								<a href="/careers" class="text-teal-600 hover:text-teal-700 font-semibold">
										加入我们的团队 →
								</a>
						</div>
				</template>
		</Team>
</template>

<script setup lang="ts">
		// import Team from '~/components/market/team/Team.vue';
		// Icon component might need to be imported if not globally available
		// import { Icon } from '#components'; 
</script>
```

**注意**: 上述插槽示例中的CSS类名主要基于Tailwind CSS。实际项目中请根据您的样式系统调整。 
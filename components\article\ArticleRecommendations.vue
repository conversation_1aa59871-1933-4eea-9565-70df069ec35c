<script setup lang="ts">
import {articleApi} from '~/utils/api/articleApi'
import type {ArticleResponse} from '~/types/api'
import type {ArticleParams} from '~/types/api/params/ArticleParams'
import {tMsg} from '~/utils/i18n'

// 组件属性
const props = defineProps<{
  // 当前文章ID，如果为null表示是列表页
  currentArticleId?: string | number | null;
  // 用于控制获取的文章数量，特别是在不显示分页组件时
  pageSize?: number;
}>()

// 加载状态
const loading = ref(true)

// 推荐文章列表
const recommendedArticles = ref<ArticleResponse[]>([])

// 排序方式：true为时间降序，false为时间升序
const sortDesc = ref(true)

// 分页相关状态
const currentPage = ref(1)
const totalPages = ref(1)
const totalArticles = ref(0)
// 每页显示的文章数量
const defaultPageSize = ref(6)
// 非分页时，推荐文章的数量
const recommendationCount = ref(3)

// 实际使用的pageSize
const actualPageSize = computed(() => props.pageSize || defaultPageSize.value)

// 是否为分页模式 (当在 /posts 页面，没有currentArticleId时)
const isPaginatedMode = computed(() => props.currentArticleId === null)

// 加载推荐文章数据
const loadRecommendedArticles = async () => {
    loading.value = true
    const params: ArticleParams = {
        sortSqlList: [`createTime ${sortDesc.value ? 'desc' : 'asc'}`]
    }

    if (isPaginatedMode.value) {
        params.pageNum = currentPage.value
        params.pageSize = actualPageSize.value
    } else {
        params.pageNum = 1
        // 使用props中的pageSize或默认值
        params.pageSize = props.pageSize || recommendationCount.value + 1
    }

    const {code, data} = await articleApi.queryArticleList(params)
    if (code === 200 && data) {
        if (isPaginatedMode.value) {
            recommendedArticles.value = data.result
            totalPages.value = data.pages
            totalArticles.value = data.total
        } else {
            // 过滤掉当前文章，并取指定数量
            recommendedArticles.value = data.result
                .filter(article => article.id?.toString() !== props.currentArticleId?.toString())
                .slice(0, props.pageSize || recommendationCount.value)
        }
    } else {
        recommendedArticles.value = []
        if (isPaginatedMode.value) {
            totalPages.value = 1
            totalArticles.value = 0
        }
    }

    loading.value = false
}

// 切换排序方式
const toggleSortOrder = () => {
    sortDesc.value = !sortDesc.value
    if (isPaginatedMode.value) {
        currentPage.value = 1
    }
    loadRecommendedArticles()
}

// 页码变化处理
const onPageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages.value && newPage !== currentPage.value) {
        currentPage.value = newPage
        loadRecommendedArticles()
    }
}

// 监听props变化，重新加载数据
watch(
    () => [props.currentArticleId, props.pageSize],
    () => {
        currentPage.value = 1
        loadRecommendedArticles()
    },
    {deep: true}
)

// 组件挂载时加载推荐文章
onMounted(() => {
    loadRecommendedArticles()
})
</script>

<template>
  <div class="mt-8">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-xl font-semibold">{{ tMsg('blog.recommended_reading') }}</h3>
      <!-- 只在列表页显示排序按钮 -->
      <button v-if="isPaginatedMode"
              class="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
              @click="toggleSortOrder">
        <Icon :name="!sortDesc ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4" />
        {{ !sortDesc ? tMsg('common.sort_asc') : tMsg('common.sort_desc') }}
      </button>
    </div>

    <!-- 加载状态骨架屏 -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div v-for="i in (isPaginatedMode ? actualPageSize : (props.pageSize || recommendationCount))"
           :key="`skeleton-${i}`"
           class="border border-border rounded-md p-4 space-y-3">
        <div class="h-48 bg-muted rounded animate-pulse" />
        <div class="h-5 bg-muted rounded w-3/4 animate-pulse mt-2" />
        <div class="h-4 bg-muted rounded w-1/3 animate-pulse mt-1" />
        <div class="space-y-1.5 mt-2">
          <div class="h-4 bg-muted rounded w-full animate-pulse" />
          <div class="h-4 bg-muted rounded w-5/6 animate-pulse" />
        </div>
      </div>
    </div>

    <!-- 文章列表 -->
    <div v-else-if="recommendedArticles.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <ArticleCard v-for="article in recommendedArticles" :key="article.id" :article="article" />
    </div>

    <!-- 没有推荐文章的提示 -->
    <div v-else class="text-center py-8 text-muted-foreground">
      {{ tMsg('blog.page.no_recommendations') }}
    </div>

    <!-- 仅在列表页显示 -->
    <div v-if="isPaginatedMode && totalPages > 1 && !loading" class="mt-8 flex justify-end">
      <nav aria-label="Pagination">
        <ul class="inline-flex items-center space-x-4 text-sm">
          <!-- 上一页按钮 -->
          <li>
            <button :disabled="currentPage === 1"
                    class="text-muted-foreground hover:text-primary cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                    @click="onPageChange(currentPage - 1)">
              {{ tMsg('pagination.previous') }}
            </button>
          </li>
          <!-- 下一页按钮 -->
          <li>
            <button :disabled="currentPage === totalPages"
                    class="text-muted-foreground hover:text-primary cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                    @click="onPageChange(currentPage + 1)">
              {{ tMsg('pagination.next') }}
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>

# FooterOne 组件使用文档

`FooterOne` 是一个用于构建网站页脚的 Vue 组件，具有响应式设计、可配置的布局选项和国际化支持。它允许通过 props
控制站点详情、社交链接的显示，并通过插槽进行灵活的内容定制。

## Props (属性)

以下是 `FooterOne` 组件可接受的 props 列表：

| Prop 名称                        | 类型                                             | 描述                                                                                                                                    | 可选值                                      | 默认值      |
|:-------------------------------|:-----------------------------------------------|:--------------------------------------------------------------------------------------------------------------------------------------|:-----------------------------------------|:---------|
| `showSiteDetails`              | `boolean`                                      | 是否显示站点详情区域（通常包含 Logo、站点名称、描述和社交链接）。                                                                                                   | `true`, `false`                          | `true`   |
| `siteDetailsLayout`            | `'top'` \| `'bottom'` \| `'left'` \| `'right'` | 控制站点详情区域相对于导航链接组的布局方式。`'top'` 或 `'left'` 使站点详情优先显示（在导航链接上方或左侧）；`'bottom'` 或 `'right'` 则相反。在移动设备或较小屏幕上，`'left'` 和 `'right'` 布局通常会垂直堆叠。 | `'top'`, `'bottom'`, `'left'`, `'right'` | `'left'` |
| `showSocialLinksInSiteDetails` | `boolean`                                      | 是否在站点详情区域内显示社交媒体图标链接（如果 `footerConfig.socialLinks` 中有数据）。                                                                             | `true`, `false`                          | `true`   |
| `showSocialLinksInCopyright`   | `boolean`                                      | 是否在底部版权信息旁边显示社交媒体图标链接（如果 `footerConfig.socialLinks` 中有数据）。                                                                            | `true`, `false`                          | `false`  |

## 数据配置

`FooterOne` 组件的内容（如站点信息、导航链接、版权文本等）主要由以下配置文件驱动：

* **`@/config/footer.ts` (或相应的 `.json` 文件)**:
    * `organizationDescription`: `I18nTextMap` 类型，站点的简短描述，显示在站点详情区域。
    * `socialLinks`: `SocialLink[]` 数组，包含社交媒体平台的链接信息（如 `href`, `title`, `icon`）。
    * `sections`: `FooterSection[]` 数组，定义页脚导航的各个区块。每个区块 (`FooterSection`) 包含：
        * `title`: `I18nTextMap`，区块标题。
        * `links`: `LinkItem[]` 数组，区块内的导航链接列表。每个链接 (`LinkItem`) 包含 `title` (I18nTextMap) 和 `href`。
    * `copyright`: `I18nTextMap` 类型，版权信息文本。

* **`@/config/site.ts` (或相应的 `.json` 文件)**:
    * `logo`: `string`，站点的 Logo 图片 URL，用于显示在站点详情区域。
    * `title`: `I18nTextMap` 类型，站点的主标题或名称。

组件内部使用 `getLocalizedConfigText` 工具函数根据当前 i18n 语言环境自动加载对应的文本，并使用 `resolveLocalePath`
处理内部导航链接。

## 插槽 (Slots)

`FooterOne` 组件提供了以下插槽以实现灵活的定制：

- **`before-layout`**:
    * **描述**: 在组件最外层 `<footer>` 标签内部，但在主要内容容器 (`div.container`) 之前插入内容。
    * **作用域数据**: 无。

- **`site-details-prepend` (作用域插槽)**:
    * **描述**: 在站点详情 `<div>` 容器内部，在默认的站点详情内容渲染之前插入内容。
    * **作用域数据**:
        * `siteConfig`: `SiteConfig` - 从 `@/config/site` 加载的站点配置对象。
        * `footerConfig`: `FooterConfig` - 从 `@/config/footer` 加载的页脚配置对象。

- **`site-details` (作用域插槽)**:
    * **描述**: 完全自定义站点详情区域的渲染。如果使用此插槽，则会替换掉默认的站点 Logo、名称、描述和社交链接的显示逻辑。
    * **作用域数据**:
        * `siteConfig`: `SiteConfig`
        * `footerConfig`: `FooterConfig`

- **`site-info` (作用域插槽)**:
    * **描述**: 在默认的站点详情区域内，自定义 Logo、站点名称和组织描述部分的渲染。
    * **作用域数据**:
        * `siteConfig`: `SiteConfig`
        * `footerConfig`: `FooterConfig`

- **`site-social-links` (作用域插槽)**:
    * **描述**: 在默认的站点详情区域内，自定义社交链接部分的渲染 (仅当 `props.showSocialLinksInSiteDetails` 为 `true`
      时相关)。
    * **作用域数据**:
        * `socialLinks`: `SocialLink[]` - 从 `footerConfig.socialLinks` 获取的社交链接数组。

- **`site-details-append` (作用域插槽)**:
    * **描述**: 在站点详情 `<div>` 容器内部，在默认的站点详情内容渲染之后插入内容。
    * **作用域数据**:
        * `siteConfig`: `SiteConfig`
        * `footerConfig`: `FooterConfig`

- **`section-header` (作用域插槽)**:
    * **描述**: 自定义页脚导航区域中每个区块的标题渲染。
    * **作用域数据**:
        * `section`: `FooterSection` - 当前正在迭代的导航区块对象。
        * `localizedTitle`: `string` - 本地化后的区块标题。

- **`nav-link` (作用域插槽)**:
    * **描述**: 自定义页脚导航区域中每个导航链接的渲染。
    * **作用域数据**:
        * `link`: `LinkItem` - 当前正在迭代的链接对象。
        * `section`: `FooterSection` - 当前链接所属的导航区块对象。
        * `localizedLinkTitle`: `string` - 本地化后的链接标题。

- **`copyright-prepend` (作用域插槽)**:
    * **描述**: 在版权信息行 (`div.border-t`) 的内容之前，但在其容器外部插入内容。
    * **作用域数据**:
        * `footerConfig`: `FooterConfig`

- **`copyright` (作用域插槽)**:
    * **描述**: 自定义版权文本内容的渲染。默认显示 `footerConfig.copyright`。
    * **作用域数据**: 无显式作用域数据，但可访问父级作用域及默认提供的 `footerConfig.copyright`。

- **`footer-social-links` (作用域插槽)**:
    * **描述**: 在版权信息行，自定义社交链接部分的渲染 (仅当 `props.showSocialLinksInCopyright` 为 `true` 时相关)。
    * **作用域数据**:
        * `socialLinks`: `SocialLink[]` - 从 `footerConfig.socialLinks` 获取的社交链接数组。

- **`after-layout`**:
    * **描述**: 在组件最外层 `<footer>` 标签内部，主要内容容器 (`div.container`) 之后，但在 `<footer>` 闭合之前插入内容。
    * **作用域数据**: 无。

## 基本用法示例

```vue

<template>
	<FooterOne
			:show-site-details="true"
			site-details-layout="left"
			:show-social-links-in-site-details="true"
			:show-social-links-in-copyright="false"
	/>
</template>

<script setup lang="ts">
	// FooterOne 组件通常会自动导入 (Nuxt 3项目特性)
	// import FooterOne from '~/components/market/footer/FooterOne.vue';
</script>
```

## 使用自定义插槽示例

```vue

<template>
	<FooterOne site-details-layout="top" :show-social-links-in-copyright="true">
		<template #before-layout>
			<div class="bg-primary/10 text-primary text-center p-2 text-sm">
				特别通告区域
			</div>
		</template>
		
		<template #site-info="{ siteConfig, footerConfig }">
			<div class="text-center md:text-left">
				<img v-if="siteConfig.logo" :src="siteConfig.logo" alt="Logo" class="w-12 h-12 mx-auto md:mx-0 mb-2">
				<h2 class="text-2xl font-bold">{{ getLocalizedConfigText(siteConfig.title) }}</h2>
				<p class="text-sm text-muted-foreground mt-1">
					{{ getLocalizedConfigText(footerConfig.organizationDescription) }} - 始于创新，追求卓越。
				</p>
			</div>
		</template>
		
		<template #section-header="{ section, localizedTitle }">
			<h4 class="text-lg font-semibold mb-3 text-accent-foreground">
				{{ localizedTitle }}
				<span v-if="section.links.length > 3" class="text-xs text-muted-foreground ml-1">(热门)</span>
			</h4>
		</template>
		
		<template #copyright="{ footerConfig }">
			<p class="text-xs text-muted-foreground/80">
				&copy; {{ new Date().getFullYear() }} {{ getLocalizedConfigText(footerConfig.copyright) }}. 保留所有权利。
			</p>
		</template>
		
		<template #after-layout>
			<div class="text-center py-4 border-t mt-4 text-xs text-muted-foreground">
				页脚构建于 Nuxt & Vue
			</div>
		</template>
	</FooterOne>
</template>

<script setup lang="ts">
	import {getLocalizedConfigText} from '@/utils/i18n';
	// FooterOne, Icon 等组件若未全局注册，可能需要导入
	// import FooterOne from '~/components/market/footer/FooterOne.vue';
	// import { Icon } from '#components'; // 假设 Icon 是全局组件
</script>
```

**注意**: 上述插槽示例中的CSS类名主要基于Tailwind CSS。实际项目中请根据您的样式系统调整。同时，`getLocalizedConfigText`
工具函数在插槽作用域内可能无法直接访问，如果需要在插槽模板内使用，确保它在该上下文中可用（如此示例中在 `<script setup>`
中导入）。 
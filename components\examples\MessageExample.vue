<script setup lang="ts">
// 使用自定义封装的 message 实例
import {Button} from '@/components/ui/button'
import {message} from '@/composables/useMessage'

// 简单成功提示
const handleSimpleToast = () => {
    message.show('操作成功', {
        description: '您的操作已成功完成'
    })
}

// 带操作按钮的成功提示
const handleSuccessWithAction = () => {
    message.success('保存成功', {
        description: '您的文档已成功保存',
        action: {
            label: '查看',
            onClick: () => console.log('用户点击了"查看"按钮')
        }
    })
}

// 带操作按钮的警告提示
const handleWarningWithAction = () => {
    message.warning('更新可用', {
        description: '有新版本可供下载',
        action: {
            label: '立即更新',
            onClick: () => console.log('用户点击了"立即更新"按钮')
        }
    })
}

// 错误操作提示
const handleErrorToast = () => {
    message.error('出现错误', {
        description: '无法连接到服务器，请稍后再试'
    })
}

// 带操作按钮的错误提示
const handleErrorWithAction = () => {
    message.error('操作失败', {
        description: '保存文档时发生错误',
        action: {
            label: '重试',
            onClick: () => console.log('用户点击了"重试"按钮')
        }
    })
}

// 自定义位置的提示
const handlePositionToast = () => {
    message.info('自定义位置', {
        description: '此提示显示在底部',
        position: 'bottom-center'
    })
}

// 自定义持续时间
const handleLongDurationToast = () => {
    message.info('长时间显示', {
        description: '此提示将显示10秒',
        duration: 10000
    })
}

// 不会自动关闭的提示
const handlePersistentToast = () => {
    message.info('重要通知', {
        description: '此提示不会自动关闭，需要手动关闭',
        duration: Infinity
    })
}

// 成功消息
const handleSuccessToast = () => {
    message.success('操作成功', {
        description: '您的操作已成功完成'
    })
}

// 警告消息
const handleWarningToast = () => {
    message.warning('操作警告', {
        description: '请注意此操作可能有风险'
    })
}

// 信息消息
const handleInfoToast = () => {
    message.info('提示信息', {
        description: '这是一条普通提示信息'
    })
}

// 自定义样式的操作按钮
const handleCustomActionStyle = () => {
    message.show('主要操作', {
        description: '这个操作很重要',
        action: {
            label: '确认',
            onClick: () => console.log('用户点击了"确认"按钮')
        }
    })
}

// 带多个操作按钮
const handleMultipleActions = () => {
    message.show('文件上传完成', {
        description: '您的文件已成功上传',
        action: {
            label: '查看',
            onClick: () => console.log('用户点击了"查看"按钮')
        },
        cancel: {
            label: '关闭',
            onClick: () => console.log('用户点击了"关闭"按钮')
        }
    })
}

// 使用 Promise 的 Toast
const handlePromiseToast = () => {
    const promise = new Promise((resolve, reject) => {
        setTimeout(() => {
            Math.random() > 0.5 ? resolve('操作成功') : reject(new Error('操作失败'))
        }, 2000)
    })

    message.promise(promise, {
        loading: '处理中...',
        success: (data) => ({
            title: '成功',
            description: String(data)
        }),
        error: (err) => ({
            title: '失败',
            description: err.message
        })
    })
}

// 添加默认导出以解决导入问题
defineComponent({
    name: 'MessageExample'
})
</script>

<template>
  <div class="flex flex-col gap-4 p-4">
    <h2 class="text-xl font-bold">消息提示示例</h2>

    <div class="grid grid-cols-1 gap-6">
      <!-- Toast 基础示例 -->
      <div>
        <h3 class="text-lg font-semibold mb-3">基础消息</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          <Button variant="default" @click="handleSimpleToast">简单提示</Button>
          <Button variant="success" @click="handleSuccessToast">成功提示</Button>
          <Button variant="warning" @click="handleWarningToast">警告提示</Button>
          <Button variant="info" @click="handleInfoToast">信息提示</Button>
          <Button variant="destructive" @click="handleErrorToast">错误提示</Button>
        </div>
      </div>

      <!-- Toast 操作按钮 -->
      <div>
        <h3 class="text-lg font-semibold mb-3">带操作按钮的消息</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
          <Button variant="default" @click="handleSuccessWithAction">成功带操作</Button>
          <Button variant="outline" @click="handleWarningWithAction">警告带操作</Button>
          <Button variant="destructive" @click="handleErrorWithAction">错误带操作</Button>
          <Button variant="secondary" @click="handleCustomActionStyle">自定义按钮</Button>
          <Button variant="outline" @click="handleMultipleActions">多个操作按钮</Button>
        </div>
      </div>

      <!-- Toast 高级选项 -->
      <div>
        <h3 class="text-lg font-semibold mb-3">高级选项</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          <Button variant="outline" @click="handlePositionToast">自定义位置</Button>
          <Button variant="outline" @click="handleLongDurationToast">自定义时长</Button>
          <Button variant="outline" @click="handlePersistentToast">持久显示</Button>
          <Button variant="outline" @click="handlePromiseToast">Promise 加载</Button>
        </div>
      </div>
    </div>
  </div>
</template>
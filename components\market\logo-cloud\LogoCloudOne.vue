<script setup lang="ts">
import {getLocalizedConfigText} from '@/utils/i18n'
import logoConfig from '@/config/logoCloud'
import AutoScrollContainer from '~/components/common/AutoScrollContainer.vue'
import type {LogoCloudConfig} from '~/types/site/logoCloud'

const props = withDefaults(defineProps<{
    mobileDisplayMode?: 'grid' | 'scroll'
}>(), {
    // 默认为 grid
    mobileDisplayMode: 'grid'
})

// 将导入的 JSON 断言为配置类型
const config = logoConfig as LogoCloudConfig
const localizedTitle = computed(() => getLocalizedConfigText(config.title))
</script>

<template>
  <div class="py-16">
    <div class="mx-auto max-w-7xl">
      <slot name="before_layout" :logo-config="config"/>
      <!-- 标题 -->
      <slot name="title" :localized-title="localizedTitle" :logo-config="config">
        <h2 class="text-center text-lg font-semibold leading-8 text-foreground mb-4 md:mb-6">
          {{ localizedTitle }}
        </h2>
      </slot>

      <slot name="before_logo"/>
      <!-- === Logo 区域 === -->
      <div>
        <!-- 1. 滚动模式 (仅在 mobileDisplayMode 为 'scroll' 时渲染) -->
        <div v-if="props.mobileDisplayMode === 'scroll'" class="lg:hidden">
          <AutoScrollContainer :pause-on-hover="true">
            <!-- Logo 列表: 直接将 img 作为 AutoScrollContainer 的子节点 -->
            <template v-for="(logo, index) in config.logos" :key="index">
              <!-- 允许用户自定义滚动模式下的每个 logo 项 -->
              <slot name="scroll-logo-item"
                    :logo="logo">
                <img class="h-12 flex-none object-contain px-4 sm:px-6" :src="logo.svgUrl" :alt="logo.name">
              </slot>
            </template>
          </AutoScrollContainer>
        </div>

        <!-- 2. Grid 模式 -->
        <!-- 在 scroll 模式下，大屏幕显示 Grid -->
        <!-- 在 grid 模式下，所有屏幕都显示 Grid (利用 Tailwind 响应式) -->
        <div :class="[
          'mx-auto grid max-w-lg items-center gap-x-8 gap-y-10 sm:max-w-xl lg:mx-0 lg:max-w-none',
          props.mobileDisplayMode === 'scroll' ? 'hidden lg:grid lg:grid-cols-5' : 'grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-5'
        ]">
          <template v-for="(logo, index) in config.logos" :key="index">
            <!-- 允许用户自定义 Grid 模式下的每个 logo 项 -->
            <slot name="grid-logo-item"
                  :logo="logo">
              <img class="col-span-2 max-h-12 w-full object-contain lg:col-span-1"
                   :src="logo.svgUrl"
                   :alt="logo.name"
                   height="48">
            </slot>
          </template>
        </div>
      </div>

      <slot name="after_layout" :logo-config="config"/>
    </div>
  </div>
</template>
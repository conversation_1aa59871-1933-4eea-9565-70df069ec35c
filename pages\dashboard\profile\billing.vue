<script setup lang="ts">
import {computed, ref} from 'vue'
import Input from '@/components/ui/input/Input.vue'
import Label from '@/components/ui/label/Label.vue'
import Button from '@/components/ui/button/Button.vue'
import Textarea from '@/components/ui/textarea/Textarea.vue'
import Tabs from '@/components/ui/tabs/Tabs.vue'
import TabsList from '@/components/ui/tabs/TabsList.vue'
import TabsTrigger from '@/components/ui/tabs/TabsTrigger.vue'
import TabsContent from '@/components/ui/tabs/TabsContent.vue'
import {PaymentProviderEnum} from '@/utils/constants/enums/PaymentProviderEnum'
import {payApi} from '@/utils/api/payApi'
import type {BillingHistoryResponse} from '~/types/api/response/BillingHistoryResponse'
import {useMessage} from '~/composables/useMessage'
import {navigateTo} from '#app'

const {message} = useMessage()

// 账单信息
const receiptInfo = ref('')

// 保存账单信息
const saveReceiptInfo = () => {
    message.success('账单信息已更新')
}

// 用于订单信息Tabs
const orderManagementProviders = computed(() => {
    return Object.values(PaymentProviderEnum).map(providerValue => {
        let labelText = ''
        if (providerValue === PaymentProviderEnum.STRIPE) {
            labelText = 'Stripe'
        } else if (providerValue === PaymentProviderEnum.ALIPAY) {
            labelText = '支付宝'
        }
        return {value: providerValue as PaymentProviderEnum, label: labelText}
    })
})

// 处理 Stripe 订单管理按钮点击事件
async function handleManageStripeOrders() {
    const {data: billingData, code, message: apiMessage, requestId} = await payApi.queryBillingHistory()

    if (code === 200 && billingData) {
        const billingInfoList: BillingHistoryResponse[] = billingData
        let portalUrl: string | undefined

        if (billingInfoList && billingInfoList.length > 0) {
            const firstItem = billingInfoList[0] as any
            if (firstItem && firstItem.stripeSetting && typeof firstItem.stripeSetting.customerPortalUrl === 'string') {
                portalUrl = firstItem.stripeSetting.customerPortalUrl
            }
        }

        if (portalUrl) {
            await navigateTo(portalUrl, {external: true, open: {target: '_blank'}})
        } else {
            message.error('无法获取订单管理页面链接，请检查您的账户或稍后再试。')
        }
    } else {
        message.error(`获取订单信息失败: ${apiMessage}`, {showSupport: true, requestId})
    }
}
</script>

<template>
  <div>
    <h2 class="text-2xl font-bold mb-6">账单信息</h2>
    <p class="text-muted-foreground mb-8">管理您的账单和支付方式</p>

    <!-- 订单信息部分 -->
    <div class="mb-8">
      <h3 class="text-xl font-semibold mb-4">订单信息</h3>
      <Tabs :default-value="PaymentProviderEnum.STRIPE" class="w-full">
        <TabsList>
          <TabsTrigger v-for="provider in orderManagementProviders" :key="provider.value" :value="provider.value">
            {{ provider.label }}
          </TabsTrigger>
        </TabsList>
        <TabsContent :value="PaymentProviderEnum.STRIPE" class="mt-4">
          <div class="text-center py-4">
            <p class="mb-4">管理您的 Stripe 订阅、发票和付款方式。</p>
            <Button @click="handleManageStripeOrders">管理订单</Button>
          </div>
        </TabsContent>
        <TabsContent :value="PaymentProviderEnum.ALIPAY" class="mt-4">
          <div class="text-center py-4">
            <p class="mb-4">支付宝订单管理功能暂未上线，敬请期待。</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>

    <!-- 支付方式部分 -->
    <div class="mb-8">
      <h3 class="text-xl font-semibold mb-4">支付方式</h3>
      <Tabs default-value="card" class="w-full">
        <TabsList>
          <TabsTrigger value="card">信用卡</TabsTrigger>
          <TabsTrigger value="paypal">PayPal</TabsTrigger>
          <TabsTrigger value="alipay">支付宝</TabsTrigger>
        </TabsList>
        <TabsContent value="card" class="space-y-4 mt-4">
          <div class="grid gap-4 py-4">
            <div class="grid grid-cols-4 items-center gap-4">
              <Label for="card-name" class="text-right">持卡人姓名</Label>
              <Input id="card-name" placeholder="持卡人姓名" class="col-span-3"/>
            </div>
            <div class="grid grid-cols-4 items-center gap-4">
              <Label for="card-number" class="text-right">卡号</Label>
              <Input id="card-number" placeholder="0000 0000 0000 0000" class="col-span-3"/>
            </div>
            <div class="grid grid-cols-4 items-center gap-4">
              <Label for="expiry" class="text-right">到期日</Label>
              <div class="col-span-3 flex gap-2">
                <Input id="expiry-month" placeholder="MM" class="w-[80px]"/>
                <span class="flex items-center">/</span>
                <Input id="expiry-year" placeholder="YY" class="w-[80px]"/>
              </div>
            </div>
            <div class="grid grid-cols-4 items-center gap-4">
              <Label for="cvc" class="text-right">CVC</Label>
              <Input id="cvc" placeholder="CVC" class="w-[80px]"/>
            </div>
          </div>
          <Button>保存支付方式</Button>
        </TabsContent>
        <TabsContent value="paypal" class="mt-4">
          <div class="text-center py-4">
            <p class="mb-4">使用PayPal账户进行支付</p>
            <Button>连接PayPal账户</Button>
          </div>
        </TabsContent>
        <TabsContent value="alipay" class="mt-4">
          <div class="text-center py-4">
            <p class="mb-4">使用支付宝进行支付</p>
            <Button>连接支付宝账户</Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>

    <!-- 发票信息部分 -->
    <div>
      <h3 class="text-xl font-semibold mb-4">发票信息 <span
        class="text-muted-foreground text-sm font-normal">(可选)</span>
      </h3>
      <p class="text-muted-foreground mb-4">
        如果您需要在发票上添加额外的联系人或税务信息（如企业地址、增值税号等），请在下方输入，这些信息将显示在您的所有发票上。
      </p>
      <form class="space-y-4" @submit.prevent="saveReceiptInfo">
        <div class="space-y-2">
          <Textarea v-model="receiptInfo" placeholder="输入发票所需的额外信息，如企业地址、税号等" class="min-h-[150px]"/>
        </div>
        <Button type="submit">更新</Button>
      </form>
    </div>
  </div>
</template>
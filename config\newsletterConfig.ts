import type {NewsletterConfig} from '@/types/site/newsletter'

// NewsletterOne 组件的配置驱动文本
export const newsletterConfig: NewsletterConfig = {
    titleHtml: {
        en: 'Want product news and updates? <br>Sign up for our newsletter.',
        zh: '想获取产品新闻和更新吗？<br>订阅我们的新闻通讯。'
    },
    descriptionText: {
        en: 'Nostrud amet eu ullamco nisi aute in ad minim nostrud adipisicing velit quis. Duis tempor incididunt dolore.',
        zh: '这是一个占位符描述文本，请替换为有意义的中文内容。Nostrud amet eu ullamco nisi aute in ad minim nostrud adipisicing velit quis. Duis tempor incididunt dolore.'
    },
    emailPlaceholder: {
        en: 'Enter your email',
        zh: '请输入您的邮箱'
    },
    subscribeButtonText: {
        en: 'Subscribe',
        zh: '订阅'
    },
    subscribingButtonText: {
        en: 'Subscribing...',
        zh: '订阅中...'
    },
    privacyNoticeHtml: {
        en: 'We care about your data. Read our <a href="{privacyPolicyUrl}" class="underline underline-offset-2 hover:text-primary">privacy policy</a>.',
        zh: '我们关心您的数据。请阅读我们的 <a href="{privacyPolicyUrl}" class="underline underline-offset-2 hover:text-primary">隐私政策</a>。'
    }
}
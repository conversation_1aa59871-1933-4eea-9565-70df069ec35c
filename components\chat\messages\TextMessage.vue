<script setup lang="ts">
import type {ChatMessage} from '@/types/chat'

// 定义组件的props
defineProps<{
    // 文本消息对象
    message: ChatMessage
}>()
</script>

<template>
  <div class="p-3 bg-primary text-primary-foreground rounded-lg">
    <div v-if="message.replyTo" class="reply-content">
      <div class="reply-user">
        {{ message.replyTo.sender.displayName }}
      </div>
      <div class="reply-text" v-html="message.replyTo.content"/>
    </div>
    <div v-html="message.content"/>
  </div>
</template>

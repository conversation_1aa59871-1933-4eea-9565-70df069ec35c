import type {PageParams} from '~/utils/http/types'

/**
 * @description 第三方平台用户 请求参数
 */
export interface ThirdPartUserParams extends PageParams {

    /**
     * 主键
     */
    id?: number

    /**
     * 第三方平台的用户id
     */
    sub?: string

    /**
     * 第三方平台名
     */
    issuer?: string

    /**
     * 用户昵称
     */
    username?: string

    /**
     * 手机号码
     */
    phone?: string

    /**
     * 邮箱
     */
    email?: string

    /**
     * 头像
     */
    avatar?: string

    /**
     * 博客或者首页
     */
    website?: string

    /**
     * 性别 1: 男生 0: 女生 -1: 其他
     */
    gender?: number

    /**
     * locale
     */
    locale?: string

    /**
     * 地址
     */
    address?: string

    /**
     * 创建时间
     */
    createTime?: Date

    /**
     * 最后更新时间
     */
    updateTime?: Date

}
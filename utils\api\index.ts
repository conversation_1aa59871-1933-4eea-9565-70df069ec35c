// 导出所有API
export * from './userExampleApi'

// 导出通用CRUD工具
export * from './crud'

// 导出工具类型
export type {ApiResponse, RequestOptions, HttpError} from '~/utils/http'

/**
 * 使用示例:
 *
 * // 1. 创建特定业务实体的API
 * interface User {
 *   id: number
 *   username: string
 *   email: string
 * }
 *
 * // 创建用户管理API
 * const userApi = createEntityApi<User>('user');
 *
 * // 分页查询用户
 * const { code, message, data } = await userApi.queryList({
 *   pageNum: 1,
 *   pageSize: 10,
 *   username: 'test' // 业务查询参数
 * });
 *
 * // 2. 使用自定义URL创建CRUD API
 * const customApi = createCrudApi<Product>('/api/v1/custom/products');
 *
 * // 3. 使用现有的业务API
 * const { data } = await storageClassApi.createClass(requestData, appId, 'className');
 */
import type {I18nTextMap} from '~/types/i18n'
import type {PaymentProviderEnum} from '~/utils/constants/enums/PaymentProviderEnum'

export interface ConfigPricingPlan {
    /**
     * 顶部标题
     */
    title: I18nTextMap;

    description: I18nTextMap;
    /**
     * 价格通常不需要按语言变化，但可以根据需求调整
     */
    price: string;

    /**
     * 套餐原始价格
     */
    originalPrice?: string;

    /**
     * 是否是主推套餐
     */
    isPopular?: boolean;
    /**
     * 例如 "Most popular"
     */
    popularText?: I18nTextMap;
    /**
     * 特性列表，每个特性都是一个可翻译对象
     */
    features: I18nTextMap[];
    /**
     * 按钮文本，例如 "Get Started"
     */
    buttonText: I18nTextMap;
    /**
     * 按钮图标名称 (可选)
     */
    buttonIcon?: string;
}

/**
 * 单个付费周期（如月付/年付）的配置接口
 */
export interface ConfigPricingPeriod {
    /**
     * 如果商品是订阅类型，则值为[MONTH|WEEK|YEAR|DAY]。如果是一次性购买，值为ONE_TIME
     */
    key: string;
    /**
     * 显示给用户的周期文本，例如 "Monthly", "Annually"
     */
    showText: I18nTextMap;
    /**
     * 周期的后缀文本，例如 "month", "year" (用于拼接如 "/month")
     */
    periodSuffixText: I18nTextMap;
    /**
     * 该周期下的多个价格计划
     */
    plans: ConfigPricingPlan[];
}

/**
 * 整个价格页面的顶层配置接口
 */
export interface PricingPageConfig {
    /**
     * 页面区域小标题，例如 "Pricing"
     */
    sectionHeader: I18nTextMap;
    /**
     * 页面主标题
     */
    mainTitle: I18nTextMap;
    /**
     * 页面描述文本
     */
    descriptionText: I18nTextMap;
    /**
     * 包含所有付费周期的数组
     */
    periods: ConfigPricingPeriod[];

    /**
     * 支付提供商
     */
    queryPayProvider: PaymentProviderEnum,

    productIdList: number[]
}
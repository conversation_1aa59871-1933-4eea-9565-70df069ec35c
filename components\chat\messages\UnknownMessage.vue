<script setup lang="ts">
import type {ChatMessage, UnknownMessage} from '@/types/chat'
import SupportForm from '~/components/common/SupportForm.vue'

// 定义组件的props
const props = defineProps<{
    // 文本消息对象
    message: ChatMessage
}>()

// 支持对话框状态
const isSupportOpen = ref(false)
const initSupportDesc = ref('')
// 切换支持对话框
const toggleSupportDialog = () => {
    isSupportOpen.value = !isSupportOpen.value
    const {type} = (props.message.clientData as UnknownMessage)
    initSupportDesc.value = `请求支持消息模板: ${type}, 平台侧描述: ${props.message.list[0].messageContent}`
}
</script>

<template>
  <div class="p-3">
    <div v-html="message.content"/>
    <div class="border-t border-white/20 mt-2 pt-2 flex justify-end">
      <button class="flex items-center gap-1 text-sm font-medium text-white/80 hover:text-white transition-colors"
              @click="toggleSupportDialog">
        <Icon name="heroicons:chat-bubble-left-ellipsis-20-solid" class="h-5 w-5"/>
        请求模板支持
      </button>
    </div>
    <SupportForm v-model:is-open="isSupportOpen"
                 :type="'feedback'"
                 :enable-request-id="false"
                 :init-value="initSupportDesc"/>
  </div>
</template>

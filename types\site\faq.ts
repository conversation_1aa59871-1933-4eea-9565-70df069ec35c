// types/site/faq.ts
import type {I18nTextMap} from '~/types/i18n'

// FAQ内容结构 - 使用导入的 I18nTextMap
export interface FaqContent {
    /**
     * 问题
     */
    question: I18nTextMap

    /**
     * 答案
     */
    answer: I18nTextMap
}

// FAQ项目结构
export interface FaqItem {

    /**
     * 问题图片 如果存在，会显示在问题描述下
     */
    image: string | null

    /**
     * 问题内容
     */
    content: FaqContent
}

export interface FaqConfig {
    /**
     * 标题
     */
    title: I18nTextMap

    /**
     * 描述
     */
    description: I18nTextMap

    /**
     * faq列表
     */
    items: FaqItem[]
}
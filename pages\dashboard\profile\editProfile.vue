<script setup lang="ts">
import {ref} from 'vue'
import Input from '@/components/ui/input/Input.vue'
import Label from '@/components/ui/label/Label.vue'
import Button from '@/components/ui/button/Button.vue'
import Textarea from '@/components/ui/textarea/Textarea.vue'
import Avatar from '@/components/ui/avatar/Avatar.vue'
import AvatarImage from '@/components/ui/avatar/AvatarImage.vue'
import AvatarFallback from '@/components/ui/avatar/AvatarFallback.vue'

// 用户信息状态
const user = ref({
    name: '张三',
    email: '<EMAIL>',
    avatar: '/images/avatar.jpg',
    bio: '前端开发工程师，热爱Vue和TypeScript',
    role: '管理员'
})

// 定义表单状态
const profileForm = ref({
    name: user.value.name,
    email: user.value.email,
    bio: user.value.bio
})

// 保存个人信息
const saveProfile = () => {
    // 实现保存逻辑
    user.value.name = profileForm.value.name
    user.value.email = profileForm.value.email
    user.value.bio = profileForm.value.bio
    alert('个人信息已更新')
}
</script>

<template>
  <div>
    <h2 class="text-2xl font-bold mb-6">个人资料</h2>
    <p class="text-muted-foreground mb-8">更新您的个人资料信息</p>

    <div class="flex items-center space-x-4 mb-6">
      <Avatar class="h-16 w-16">
        <AvatarImage :src="user.avatar"/>
        <AvatarFallback>{{ user.name.slice(0, 2) }}</AvatarFallback>
      </Avatar>
      <div class="space-y-1">
        <div class="font-medium">{{ user.name }}</div>
        <div class="text-sm text-muted-foreground">{{ user.role }}</div>
      </div>
    </div>

    <form @submit.prevent="saveProfile" class="space-y-4">
      <div class="space-y-2">
        <Label for="name">姓名</Label>
        <Input id="name" v-model="profileForm.name" placeholder="输入您的姓名"/>
      </div>
      <div class="space-y-2">
        <Label for="email">电子邮箱</Label>
        <Input id="email" type="email" v-model="profileForm.email" placeholder="输入您的邮箱"/>
      </div>
      <div class="space-y-2">
        <Label for="bio">个人简介</Label>
        <Textarea id="bio" v-model="profileForm.bio" placeholder="简单介绍一下自己"/>
      </div>
      <Button type="submit">保存更改</Button>
    </form>
  </div>
</template>
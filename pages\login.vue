<script setup lang="ts">
import {navigateTo} from '#app'
import {nextTick, onBeforeUnmount, onMounted, ref} from 'vue'
import {useMessage} from '~/composables/useMessage'
import type {ApiResponse} from '~/utils/http'
import logger from '~/utils/logger'
import {localStg} from '~/utils/localStorageService'
import {LocalStorageConstant} from '~/utils/constants/localStorage'
import LoginForm from '~/components/auth/LoginForm.vue'
import {resolveLocalePath} from '~/utils/i18n'
import {userAccountApi} from '~/utils/api/userAccountApi'
import type {LoginResultResponse} from '~/types/api/response/LoginResultResponse'
import {executeClearUserData} from '~/utils/auth'
import {projectConfig} from '~/config/projectConfig'

const route = useRoute()
const {message: displayMessage} = useMessage()

const dynamicLoginAreaHeight = ref<string>('100vh')

const calculateAndSetHeight = () => {
    const headerElement = document.getElementById('header')
    if (headerElement) {
        const headerHeight = headerElement.offsetHeight
        const availableHeight = window.innerHeight - headerHeight
        dynamicLoginAreaHeight.value = `${availableHeight}px`
    } else {
        dynamicLoginAreaHeight.value = '100vh'
    }
}

onMounted(async () => {
    calculateAndSetHeight()
    window.addEventListener('resize', calculateAndSetHeight)

    await nextTick()
    const queryParams = route.query
    const code = queryParams.code ? String(queryParams.code) : undefined
    const urlMessage = queryParams.message ? String(queryParams.message) : undefined
    const urlRequestId = queryParams.requestId ? String(queryParams.requestId) : undefined
    const token = queryParams.data ? String(queryParams.data) : undefined

    const localToken = localStg.get(LocalStorageConstant.TOKEN)
    const localUserInfo = localStg.get(LocalStorageConstant.USER_INFO)

    logger.debug('Login page mounted. Query params:', queryParams)
    if (code) {
        executeClearUserData()
        if (code !== '200' || !token) {
            logger.warn('Login callback indicates failure.', {code, urlMessage, urlRequestId})
            if (urlMessage) {
                displayMessage.error(
                    urlMessage, {
                        requestId: urlRequestId,
                        showSupport: true,
                        duration: 5000
                    }
                )
            }
            return
        }

        logger.info('Token found. Attempting to fetch user details.', {token: token})
        localStg.set(LocalStorageConstant.TOKEN, token)

        const response: ApiResponse<LoginResultResponse> = await userAccountApi.queryUserDetailInfo()
        if (response.code === 200 && response.data) {
            // 检查API返回的数据是否包含User接口的必填字段
            if (response.data.userId) {
                localStg.set(LocalStorageConstant.USER_INFO, response.data)
                logger.info('User details fetched and stored successfully.', {details: response.data})
                displayMessage.success('login.successfulWelcomeBack')
                await navigateTo(resolveLocalePath(projectConfig.dashboardEndpointUrl.user), {replace: true})
            } else {
                logger.error('Fetched user details are incomplete (missing required fields).', response.data)
                displayMessage.error('login.fetchUserInfoIncomplete', {
                    requestId: response.requestId,
                    showSupport: true,
                    duration: 5000
                })
                // 获取信息不完整，视为登录失败
                executeClearUserData()
            }
        } else {
            logger.error('Failed to fetch user details or API returned an error.', response)
            displayMessage.error(
                response.message,
                {
                    requestId: response.requestId,
                    showSupport: true,
                    duration: 5000
                }
            )
            executeClearUserData()
        }
    } else {
        // 如果本地不存在token或者用户信息，都清除
        if (!localToken || !localUserInfo) {
            executeClearUserData()
            return
        }
        navigateTo(resolveLocalePath(projectConfig.dashboardEndpointUrl.user), {replace: true})
    }

})

onBeforeUnmount(() => {
    window.removeEventListener('resize', calculateAndSetHeight)
})

</script>

<template>
  <div :style="{ height: dynamicLoginAreaHeight }"
       class="flex items-center justify-center bg-background overflow-hidden">
    <div class="w-full max-w-md">
      <LoginForm/>
    </div>
  </div>
</template>

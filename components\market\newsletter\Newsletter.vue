<script setup lang="ts">
import {isValidEmail} from '@/utils/validator'
import {notificationApi} from '@/utils/api/notificationApi'
import {message} from '@/composables/useMessage'
import {getLocalizedConfigText, resolveLocalePath, tMsg} from '@/utils/i18n'
import {createLogger} from '@/utils/logger'
import {newsletterConfig} from '~/config/newsletterConfig'

const logger = createLogger('NewsletterOne')

const props = defineProps({
    layout: {
        type: String as () => 'horizontal' | 'vertical',
        // 默认水平布局
        default: 'horizontal',
        validator: (value: string) => ['horizontal', 'vertical'].includes(value)
    },
    contentAlign: {
        type: String as () => 'start' | 'center' | 'end',
        // 默认靠左对齐
        default: 'start',
        validator: (value: string) => ['start', 'center', 'end'].includes(value)
    },
    showPrivacyPolicy: {
        type: Boolean as () => boolean,
        default: true
    },
    showTitle: {
        type: Boolean as () => boolean,
        default: true
    },
    showDescription: {
        type: Boolean as () => boolean,
        default: true
    }
})

const email = ref('')
const isSubscribing = ref(false)
const isSubscriptionSuccessful = ref(false)

async function subscribe() {
    if (!isValidEmail(email.value)) {
        message.error(tMsg('validation.email'))
        return
    }

    isSubscribing.value = true
    try {
        const response = await notificationApi.subscribe({
            type: 'email',
            target: email.value
        })

        if (response.code === 200) {
            message.success(tMsg('newsletter.subscribeSuccess'))
            // 清空邮箱输入
            email.value = ''
            isSubscriptionSuccessful.value = true
        } else {
            // 处理后端返回的业务错误，但未抛出异常的情况
            logger.warn('Subscription request was unsuccessful', {
                code: response.code,
                message: response.message,
                email: email.value
            })
            message.error(response.message)
            isSubscriptionSuccessful.value = false
        }
    } catch (error) {
        logger.error('An error occurred while subscribing to the mailing list', error, {email: email.value})
        message.error(tMsg('newsletter.subscribeError'))
        isSubscriptionSuccessful.value = false
    } finally {
        isSubscribing.value = false
    }
}
</script>

<template>
  <div class="w-full rounded-lg bg-card py-16">
    <slot name="before_layout"/>
    <div :class="[
      showTitle || showDescription ? 'grid gap-8' : '',
      { 'md:grid-cols-2 md:items-center': props.layout === 'horizontal' },
    ]">
      <div :class="[
        {
          'text-left': props.contentAlign === 'start',
          'text-center': props.contentAlign === 'center',
          'text-right': props.contentAlign === 'end',
        },
      ]">
        <slot name="title">
          <h2 v-if="showTitle"
              class="mb-4 text-3xl font-bold tracking-tight text-foreground md:text-4xl"
              v-html="getLocalizedConfigText(newsletterConfig.titleHtml)"/>
        </slot>
        <slot name="description">
          <p v-if="showDescription" class="text-muted-foreground">
            {{ getLocalizedConfigText(newsletterConfig.descriptionText) }}
          </p>
        </slot>
      </div>
      <div :class="[
        'flex',
        'flex-col',
        'gap-4',
        {
          'items-start': props.contentAlign === 'start',
          'items-center': props.contentAlign === 'center',
          'items-end': props.contentAlign === 'end',
        },
      ]">
        <slot name="form">
          <div class="flex w-full max-w-md items-center space-x-2">
            <Input id="email-input"
                   v-model="email"
                   type="email"
                   :placeholder="getLocalizedConfigText(newsletterConfig.emailPlaceholder, 'Enter your email')"
                   aria-label="Email for newsletter"
                   class="flex-1"
                   :disabled="isSubscribing"/>
            <Button type="submit"
                    class="text-xs"
                    :disabled="isSubscribing || isSubscriptionSuccessful"
                    @click="subscribe">
              <Icon name="material-symbols:mail" class="h-4 w-4"/>
              {{
                isSubscribing
                  ? getLocalizedConfigText(newsletterConfig.subscribingButtonText, 'Subscribing...')
                  : isSubscriptionSuccessful
                    ? tMsg('newsletter.subscribeSuccess')
                    : getLocalizedConfigText(newsletterConfig.subscribeButtonText, 'Subscribe')
              }}
            </Button>
          </div>
        </slot>
        <slot name="privacy-notice">
          <p v-if="showPrivacyPolicy"
             class="text-xs text-muted-foreground"
             v-html="getLocalizedConfigText(newsletterConfig.privacyNoticeHtml, '', { privacyPolicyUrl: resolveLocalePath('/privacy-policy') })"/>
        </slot>
      </div>
    </div>
    <slot name="after_layout"/>
  </div>
</template>

<script setup lang="ts">
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'

const props = defineProps<{
    content: string
}>()

// 渲染后的HTML内容
const renderedContent = ref('')

// 初始化markdown-it实例，并配置代码高亮
const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    highlight: function (str: string, lang: string) {
        if (lang && hljs.getLanguage(lang)) {
            try {
                return hljs.highlight(str, {language: lang}).value
            } catch (e) {
                logger.error('', e)
            }
        }
        return ''
    }
})

// 模拟文章内容加载
onMounted(() => {
    // 渲染Markdown
    renderedContent.value = md.render(props.content || '')
})
</script>

<template>
  <div class="article-content prose max-w-none" :class="`${$colorMode.value}:prose-invert`">
    <div class="my-8">
      <!-- 渲染解析后的HTML内容 -->
      <div v-html="renderedContent"/>
    </div>
  </div>
</template>

<style>
.article-content {
  font-size: 1.125rem;
  line-height: 1.8;
}

/* 代码块样式优化 */
.article-content pre {
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
}

.article-content code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
</style>
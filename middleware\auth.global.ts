import {LocalStorageConstant} from '~/utils/constants/localStorage'
import {localStg} from '~/utils/localStorageService'
import {resolveLocalePath} from '~/utils/i18n'
import type {LoginResultResponse} from '~/types/api/response/LoginResultResponse'
import {UserTagEnum} from '~/utils/constants/enums/UserTagEnum'
import {executeClearUserData} from '~/utils/auth'
import {projectConfig} from '~/config/projectConfig'

export default defineNuxtRouteMiddleware((to, _from) => {
    if (!import.meta.client) {
        return
    }

    // 检查当前路径是否是任何一个登录页面
    const userLoginPath = resolveLocalePath(projectConfig.loginEndpointUrl.user)
    const adminLoginPath = resolveLocalePath(projectConfig.loginEndpointUrl.admin)

    // 如果当前路径是登录页，不执行重定向
    const toPath = to.path

    if (toPath === userLoginPath || toPath === adminLoginPath) {
        return
    }

    const userInfo = localStg.get<LoginResultResponse>(LocalStorageConstant.USER_INFO)
    const routeType = routePathType(toPath)
    const redirectUri = to.fullPath
    if (1 === routeType) {
        if (!userInfo || UserTagEnum.ORGANIZATION_USER === userInfo.userTag) {
            executeClearUserData()
            return navigateTo({path: userLoginPath, query: {redirectUri: redirectUri}}, {replace: true})
        }
    }
    if (2 === routeType) {
        if (!userInfo || UserTagEnum.ORGANIZATION_USER !== userInfo.userTag) {
            // const messageText = tMsg('auth.unauthenticated_redirect')
            // useMessage().message.error(messageText)
            executeClearUserData()
            return navigateTo({path: adminLoginPath, query: {redirectUri: redirectUri}}, {replace: true})
        }
    }
})

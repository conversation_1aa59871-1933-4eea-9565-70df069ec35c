import type {AuthSourceConfigItem} from '~/types/site/authSourceConfig'

// 预定义的认证源配置
export const authSourceSettings: AuthSourceConfigItem[] = [
    {
        key: 'google',
        name: {en: 'Google', zh: '谷歌'},
        icon: 'logos:google-icon',
        order: 1,
        defaultVisible: true
    },
    {
        key: 'github',
        name: {en: 'GitHub', zh: 'GitHub'},
        icon: 'mdi:github',
        order: 2,
        defaultVisible: true
    },
    {
        key: 'slack',
        name: {en: 'Slack', zh: 'Slack'},
        icon: 'ic:icomoon-free:cloud',
        order: 3,
        defaultVisible: true
    },
    {
        key: 'microsoft',
        name: {en: 'Microsoft', zh: '微软'},
        icon: 'logos:microsoft-icon',
        order: 4,
        defaultVisible: false
    },
    {
        key: 'gitee',
        name: {en: 'Gite<PERSON>', zh: 'Gitee'},
        icon: 'fluent-emoji-high-contrast:broken-heart',
        order: 5,
        defaultVisible: false
    },
    {
        key: 'figma',
        name: {en: 'Apple', zh: '苹果'},
        icon: 'raphael:apple',
        order: 6,
        defaultVisible: false
    },
    {
        key: 'dingtalk',
        name: {en: 'DingTalk', zh: '钉钉'},
        icon: 'raphael:db',
        order: 7,
        defaultVisible: false
    },
    {
        key: 'douyin',
        name: {en: 'DouYin', zh: '抖音'},
        icon: 'raphael:flickr',
        order: 8,
        defaultVisible: false
    }
    // 未来可以根据需要添加更多认证源配置
    // 例如: { key: 'wechat', name: { en: 'WeChat', zh: '微信' }, icon: 'mdi:wechat', order: 6, defaultVisible: false },
]
<script setup lang="ts">
import {onBeforeUnmount, ref, watch} from 'vue'
import type {CustomerAgentClientWsService} from '~/utils/api/ws/agentClient.service'
import type {StatDataResponse} from '~/types/api/response/StatDataResponse'
import type {WebSocketResponse} from '~/utils/http/types'
import type {Platform, PlatformAccount} from '~/types/api/response/PlatformResponse'
import type {QueryStatDataParams} from '~/types/api/params/QueryStatDataParams'

const props = defineProps<{
    agentClientApi: CustomerAgentClientWsService | null
    selectedSubAccount: PlatformAccount | null
    activePlatform: Platform
}>()

const statData = ref<StatDataResponse>({
    todayReception: null,
    yesterdaySatisfaction: null,
    unpaid: null,
    threeMinResponseRate: null,
    paid: null,
    yesterdayAvgResponseTime: null,
    inquiryConversionRate: null,
    notOrder: null
})

let timer: NodeJS.Timeout | null = null

function fetchStatData() {
    if (!props.agentClientApi || !props.selectedSubAccount) {
        return
    }
    const params: QueryStatDataParams = {
        platformId: props.activePlatform.platformId,
        accountId: props.selectedSubAccount.accountId
    }
    props.agentClientApi.queryStatData(params)

    // Mock response
    setTimeout(() => {
        const mockResponse: WebSocketResponse<StatDataResponse> = {
            code: 200,
            message: 'Success',
            eventType: 'CUSTOMER_QUERY_STAT_DATA',
            data: {
                todayReception: String(Math.floor(Math.random() * 100)),
                yesterdaySatisfaction: String(Math.floor(Math.random() * 100)),
                unpaid: Math.random() > 0.5 ? String(Math.floor(Math.random() * 10)) : null,
                threeMinResponseRate: String(Math.floor(Math.random() * 100)),
                paid: String(Math.floor(Math.random() * 50)),
                yesterdayAvgResponseTime: Math.random() > 0.5 ? String(Math.floor(Math.random() * 5)) : null,
                inquiryConversionRate: String(Math.floor(Math.random() * 100)),
                notOrder: String(Math.floor(Math.random() * 20))
            }
        }
        handleStatDataResponse(mockResponse)
    }, 500)
}

function handleStatDataResponse(response: WebSocketResponse<StatDataResponse>) {
    if (response.code === 200 && response.data) {
        statData.value = response.data
    }
}

watch(() => props.agentClientApi, (newApi, oldApi) => {
    if (newApi && newApi !== oldApi) {
        newApi.onResponseForward<StatDataResponse>('CUSTOMER_QUERY_STAT_DATA', handleStatDataResponse)
    }
}, {immediate: true})

watch(() => props.selectedSubAccount, (newAccount) => {
    if (newAccount) {
        // 立即获取一次数据
        fetchStatData()

        // 先清除旧的定时器
        if (timer) {
            clearInterval(timer)
        }
        // 设置新的定时器
        timer = setInterval(fetchStatData, 60 * 1000)
    }
}, {immediate: true, deep: true})

onBeforeUnmount(() => {
    if (timer) {
        clearInterval(timer)
    }
})

</script>

<template>
  <div class="flex items-center">
    <div class="ml-12 grid grid-cols-4 gap-x-10 gap-y-2 text-sm">
      <div class="flex items-center gap-1">
        <span class="text-muted-foreground">今日接待</span>
        <span class="font-semibold">{{ statData.todayReception ?? '--' }}</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="text-muted-foreground">未下单</span>
        <span class="font-semibold">{{ statData.notOrder ?? '--' }}</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="text-muted-foreground">未付款</span>
        <span class="font-semibold">{{ statData.unpaid ?? '--' }}</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="text-muted-foreground">已付款</span>
        <span class="font-semibold">{{ statData.paid ?? '--' }}</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="text-muted-foreground">昨日旺旺满意度</span>
        <span class="font-semibold">{{ statData.yesterdaySatisfaction ?? '--' }}</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="text-muted-foreground">往日3分钟响应率</span>
        <span class="font-semibold">{{
          statData.threeMinResponseRate ? `${statData.threeMinResponseRate}%` : '--'
        }}</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="text-muted-foreground">昨日平均响应时长</span>
        <span class="font-semibold">{{
          statData.yesterdayAvgResponseTime ? `${statData.yesterdayAvgResponseTime}s` :
          '--'
        }}</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="text-muted-foreground">询单转化率</span>
        <span class="font-semibold">{{
          statData.inquiryConversionRate ? `${statData.inquiryConversionRate}%` : '--'
        }}</span>
      </div>
    </div>
  </div>
</template>

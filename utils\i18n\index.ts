/**
 * i18n国际化工具模块
 * 提供国际化相关的工具函数
 */

import {
    getCurrentLanguage,
    getCurrentLocale,
    getLocalizedConfigText,
    hasTranslation,
    resolveLocalePath,
    tMsg,
    tPlural
} from './utils'

// 命名导出
export {
    tMsg,
    getCurrentLocale,
    getCurrentLanguage,
    hasTranslation,
    tPlural,
    getLocalizedConfigText,
    resolveLocalePath
}

// 默认导出
export default {
    getLocaleMessage: tMsg,
    getCurrentLocale,
    getCurrentLanguage,
    hasTranslation,
    tPlural,
    getLocalizedConfigText,
    resolveLocalePath
}
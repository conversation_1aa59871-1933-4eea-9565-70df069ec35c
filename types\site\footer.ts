import type {I18nTextMap} from '@/types/i18n'

/**
 * 页脚链接配置
 */
export interface FooterLink {
  /**
   * 链接标题 (多语言)
   */
  title: I18nTextMap;
  /**
   * 链接地址
   */
  href: string;
}

/**
 * 页脚区域配置
 */
export interface FooterSection {
  /**
   * 区域标题 (多语言)
   */
  title: I18nTextMap;
  /**
   * 该区域下的链接列表
   */
  links: FooterLink[];
}

/**
 * 社交媒体链接配置
 */
export interface SocialLink {
  /**
   * 社交媒体标题 (多语言)
   */
  title: I18nTextMap;
  /**
   * 社交媒体链接地址
   */
  href: string;
  /**
   * 社交媒体图标名称 (来自 icones.js.org)
   */
  icon: string;
}

/**
 * 完整的页脚配置
 */
export interface FooterConfig {
  /**
   * 组织/站点描述 (多语言)
   */
  organizationDescription: I18nTextMap;
  /**
   * 版权信息 (多语言)
   */
  copyright: I18nTextMap;
  /**
   * 导航区域列表
   */
  sections: FooterSection[];
  /**
   * 社交媒体链接列表
   */
  socialLinks: SocialLink[];
}
<script setup lang="ts">
import {ref} from 'vue'
import Input from '@/components/ui/input/Input.vue'
import Label from '@/components/ui/label/Label.vue'
import Button from '@/components/ui/button/Button.vue'
import {Facebook, Github, Instagram, Linkedin, Twitter} from 'lucide-vue-next'

// 社交账号
const socialProfiles = ref({
    github: 'github.com/username',
    twitter: '',
    facebook: '',
    linkedin: 'linkedin.com/in/username',
    instagram: ''
})

// 保存社交账号
const saveProfiles = () => {
    alert('社交账号已更新')
}
</script>

<template>
  <div>
    <h2 class="text-2xl font-bold mb-6">社交账号</h2>
    <p class="text-muted-foreground mb-8">连接您的社交媒体账号</p>

    <form @submit.prevent="saveProfiles" class="space-y-4">
      <!-- GitHub -->
      <div class="grid gap-2">
        <Label for="github">GitHub</Label>
        <div class="flex">
          <div class="flex items-center px-3 border border-r-0 rounded-l-md bg-muted">
            <Github class="h-4 w-4"/>
          </div>
          <Input id="github" v-model="socialProfiles.github" placeholder="github.com/username"
                 class="rounded-l-none"/>
        </div>
      </div>

      <!-- Twitter -->
      <div class="grid gap-2">
        <Label for="twitter">Twitter</Label>
        <div class="flex">
          <div class="flex items-center px-3 border border-r-0 rounded-l-md bg-muted">
            <Twitter class="h-4 w-4"/>
          </div>
          <Input id="twitter" v-model="socialProfiles.twitter" placeholder="twitter.com/username"
                 class="rounded-l-none"/>
        </div>
      </div>

      <!-- LinkedIn -->
      <div class="grid gap-2">
        <Label for="linkedin">LinkedIn</Label>
        <div class="flex">
          <div class="flex items-center px-3 border border-r-0 rounded-l-md bg-muted">
            <Linkedin class="h-4 w-4"/>
          </div>
          <Input id="linkedin" v-model="socialProfiles.linkedin" placeholder="linkedin.com/in/username"
                 class="rounded-l-none"/>
        </div>
      </div>

      <!-- Facebook -->
      <div class="grid gap-2">
        <Label for="facebook">Facebook</Label>
        <div class="flex">
          <div class="flex items-center px-3 border border-r-0 rounded-l-md bg-muted">
            <Facebook class="h-4 w-4"/>
          </div>
          <Input id="facebook" v-model="socialProfiles.facebook" placeholder="facebook.com/username"
                 class="rounded-l-none"/>
        </div>
      </div>

      <!-- Instagram -->
      <div class="grid gap-2">
        <Label for="instagram">Instagram</Label>
        <div class="flex">
          <div class="flex items-center px-3 border border-r-0 rounded-l-md bg-muted">
            <Instagram class="h-4 w-4"/>
          </div>
          <Input id="instagram" v-model="socialProfiles.instagram" placeholder="instagram.com/username"
                 class="rounded-l-none"/>
        </div>
      </div>

      <Button type="submit" class="mt-4">保存社交账号</Button>
    </form>
  </div>
</template>
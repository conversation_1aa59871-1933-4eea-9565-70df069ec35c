import {createApp, h, ref} from 'vue'
import CaptchaDialog from '@/components/common/CaptchaDialog.vue'

interface ShowCaptchaOptions {
  ticket: string;
  apiBase: string;
}

// 这个 ref 将在 app.vue 中用于控制 CaptchaDialog 的 v-model:open
export const isCaptchaDialogVisible = ref(false)

export function useCaptcha() {
    function showCaptcha(options: ShowCaptchaOptions): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            const dialogOpen = ref(true)
            let appInstance: any = null

            const onSuccess = (captchaId: string) => {
                dialogOpen.value = false
                resolve(captchaId)
                cleanup()
            }

            const onClose = () => {
                dialogOpen.value = false
                reject(new Error('验证码窗口已关闭或验证失败'))
                cleanup()
            }

            const container = document.createElement('div')
            document.body.appendChild(container)

            const captchaDialogNode = h(CaptchaDialog, {
                // 直接传递 ref 的 value
                open: dialogOpen.value,
                'onUpdate:open': (value: boolean) => {
                    dialogOpen.value = value
                    // 如果Dialog因任何原因关闭（例如点击外部），则触发onClose
                    if (!value) {
                        onClose()
                    }
                },
                ticket: options.ticket,
                apiBase: options.apiBase,
                onSuccess,
                onClose
            })

            appInstance = createApp(captchaDialogNode)
            appInstance.mount(container)

            function cleanup() {
                if (appInstance) {
                    appInstance.unmount()
                    appInstance = null
                }
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container)
                }
            }
        })
    }

    function openCaptchaDialog() {
        isCaptchaDialogVisible.value = true
    // 注意：现在打开 Dialog 后，您需要在 securityUtils.ts 中
    // nextTick 或类似机制确保 Dialog 的 DOM 渲染完毕后，
    // 再去初始化 TAC 到 '#captcha-box-dialog'
    }

    function closeCaptchaDialog() {
        isCaptchaDialogVisible.value = false
    }

    return {
        showCaptcha,
        openCaptchaDialog,
        closeCaptchaDialog
    }
}
